#include "../include/hat_pts.hpp"
#include "../include/Get_Knot_Vector.hpp"
#include "../include/lgwt.hpp"
#include "../include/mass_matrix.hpp"
#include "../include/eigen_wrapper.hpp"
#include "../include/bspln.hpp"
#include <cmath>
#include <iostream>

std::tuple<Vector, Vector> hat_pts(Integer N, Integer p) {

    Integer k = p + 1;
    Vector t1 = Get_Knot_Vector(N, k);
    Vector t2 = Get_Knot_Vector(N-1, k-1);

    Integer NB_intervals = N - p;
    Integer ord_gi = p; // Gaussian integration order

    Matrix xint(NB_intervals, ord_gi), wint(NB_intervals, ord_gi);
    for(Integer kd=0; kd<NB_intervals; ++kd){
        Real a = t1(p+kd);
        Real b = t1(p+kd+1);
        auto [xg,wg] = lgwt(ord_gi, a, b);
        for(Integer j=0;j<ord_gi;++j){
            xint(kd,j) = xg(j);
            wint(kd,j) = wg(j);
        }
    }

    Matrix mm11 = mass_matrix(p, N,   N,   t1, t1, xint, wint, 0);
    Matrix mm22 = mass_matrix(p, N-1, N-1, t2, t2, xint, wint, 1);

    // naive Cholesky producing upper triangular matrix
    auto chol_upper = [](const Matrix &A){
        Integer n = A.rows;
        Matrix R(n,n,0.0);
        for(Integer i=0;i<n;++i){
            for(Integer j=i;j<n;++j){
                Real sum = A(i,j);
                for(Integer k2=0;k2<i;++k2){ sum -= R(k2,i)*R(k2,j); }
                if(i==j){ if(sum<=0) sum = EPS; R(i,i)=std::sqrt(sum); }
                else     { R(i,j)=sum/R(i,i);} }
        }
        return R; };

    Matrix L11T = chol_upper(mm11);
    Matrix L22T = chol_upper(mm22);
    Matrix L11  = L11T.transpose();
    Matrix L22  = L22T.transpose();

    Matrix invL11 = EigenWrapper::invSPD(L11);
    Matrix invL22 = EigenWrapper::invSPD(L22);

    // Integrals of B-splines and x*B-splines
    Vector intB1(N,0.0), intxB1(N,0.0);
    for(Integer ib1=1; ib1<=N; ++ib1){
        for(Integer kd=0; kd<NB_intervals; ++kd){
            for(Integer lpt=0; lpt<ord_gi; ++lpt){
                Real b1 = bspln(t1, N, ib1, k, xint(kd,lpt));
                Real w  = wint(kd,lpt);
                intB1(ib1-1)  += b1 * w;
                intxB1(ib1-1) += b1 * xint(kd,lpt) * w;
            }
        }
    }

    Vector intB2(N-1,0.0), intxB2(N-1,0.0);
    Integer k_minus1 = k-1;
    for(Integer ib2=1; ib2<=N-1; ++ib2){
        for(Integer kd=0; kd<NB_intervals; ++kd){
            for(Integer lpt=0; lpt<ord_gi; ++lpt){
                Real b2 = bspln(t2, N-1, ib2, k_minus1, xint(kd,lpt));
                Real w  = wint(kd,lpt);
                intB2(ib2-1)  += b2 * w;
                intxB2(ib2-1) += b2 * xint(kd,lpt) * w;
            }
        }
    }

    auto matvec_lambda = [&](const Matrix &M, const Vector &v){ return matvec(M,v); };
    Vector num1 = matvec_lambda(invL11, intxB1);
    Vector den1 = matvec_lambda(invL11, intB1);

    Vector hatpoints1(N);
    for(Integer i=0;i<N;++i){ hatpoints1(i) = num1(i)/(den1(i)+EPS); }
    hatpoints1(N-1) = 1.0;

    Vector num2 = matvec_lambda(invL22, intxB2);
    Vector den2 = matvec_lambda(invL22, intB2);
    Vector hatpoints2(N-1);
    for(Integer i=0;i<N-1;++i){ hatpoints2(i) = num2(i)/(den2(i)+EPS); }
    hatpoints2(N-2) = 1.0;

    return {hatpoints1, hatpoints2};
} 