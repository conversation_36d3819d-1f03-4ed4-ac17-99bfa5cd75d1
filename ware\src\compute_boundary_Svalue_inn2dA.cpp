#include "../include/compute_boundary_Svalue_inn2dA.hpp"
#include "../include/pagemtimes.hpp"
#include "../include/rotate_sij.hpp"
#include "../include/rotate_full_stress.hpp"
#include "../include/select_face.hpp"
#include "../include/common_types.hpp"

static inline Matrix mul_local(const Matrix &A,const Matrix &B){ return pagemtimes(A,B);} 
static inline Matrix trans(const Matrix &A){ return transpose(A);} 

static void boundary_from_S(const Matrix &invLxT, const Matrix &invLzT, const Matrix &S,
                            Vector &mo, Vector &po, Vector &om, Vector &op)
{
    Matrix tmp = mul_local(invLxT, S);
    Integer cols = tmp.cols;
    mo = Vector(cols); po = Vector(cols);
    for(Integer j=0;j<cols;++j){ mo[j]=tmp(0,j); po[j]=tmp(tmp.rows-1,j);} // first & last row

    Matrix tmpT = trans(S);
    tmpT = mul_local(invLzT, tmpT);
    tmp = trans(tmpT);
    Integer rows = tmp.rows;
    om = Vector(rows); op = Vector(rows);
    for(Integer i=0;i<rows;++i){ om[i]=tmp(i,0); op[i]=tmp(i,tmp.cols-1);} 
}

std::vector<Domain2dA> compute_boundary_Svalue_inn2dA(std::vector<Domain2dA>& OM)
{
    for(Integer iom=0;iom<static_cast<Integer>(OM.size()); ++iom){
        Domain2dA &d = OM[iom];
        // extract invL matrices
        const Matrix &invLxT11 = d.invLxT11; const Matrix &invLzT11=d.invLzT11;
        const Matrix &invLxT22 = d.invLxT22; const Matrix &invLzT22=d.invLzT22;

        // stress matrices
        const Matrix &Sxx11 = d.state.Sxx11; const Matrix &Szz11 = d.state.Szz11;
        const Matrix &Sxx22 = d.state.Sxx22; const Matrix &Szz22 = d.state.Szz22;

        // shear stresses (grid 1×2 and 2×1)
        const Matrix &Sxz12 = d.state.Sxz12; // (Nx1 × Nz2)
        const Matrix &Sxz21 = d.state.Sxz21; // (Nx2 × Nz1)

        // declare shear boundary vectors before first use
        Vector Sxz12mo,Sxz12po,Sxz12om,Sxz12op;
        Vector Sxz21mo,Sxz21po,Sxz21om,Sxz21op;

        Vector Sxx11mo,Sxx11po,Sxx11om,Sxx11op;
        Vector Sxx22mo,Sxx22po,Sxx22om,Sxx22op;
        Vector Szz11mo,Szz11po,Szz11om,Szz11op;
        Vector Szz22mo,Szz22po,Szz22om,Szz22op;

        boundary_from_S(invLxT11, invLzT11, Sxx11, Sxx11mo,Sxx11po,Sxx11om,Sxx11op);
        boundary_from_S(invLxT22, invLzT22, Sxx22, Sxx22mo,Sxx22po,Sxx22om,Sxx22op);
        boundary_from_S(invLxT11, invLzT11, Szz11, Szz11mo,Szz11po,Szz11om,Szz11op);
        boundary_from_S(invLxT22, invLzT22, Szz22, Szz22mo,Szz22po,Szz22om,Szz22op);

        // shear uses mixed L matrices: Sxz12 (Nx1,Nz2) -> rows use invLxT11 (Nx1), cols use invLzT22 (Nz2)
        boundary_from_S(invLxT11, invLzT22, Sxz12, Sxz12mo,Sxz12po,Sxz12om,Sxz12op);
        // Sxz21 (Nx2,Nz1)
        boundary_from_S(invLxT22, invLzT11, Sxz21, Sxz21mo,Sxz21po,Sxz21om,Sxz21op);

        // store inn values
        d.state.Sxx11mo_inn = Sxx11mo; d.state.Sxx11po_inn = Sxx11po;
        d.state.Sxx22mo_inn = Sxx22mo; d.state.Sxx22po_inn = Sxx22po;
        d.state.Szz11om_inn = Szz11om; d.state.Szz11op_inn = Szz11op;
        d.state.Szz22om_inn = Szz22om; d.state.Szz22op_inn = Szz22op;
        d.state.Sxx11om_inn = Sxx11om; d.state.Sxx11op_inn = Sxx11op; // 修正MATLAB中的错误
        d.state.Sxx22om_inn = Sxx22om; d.state.Sxx22op_inn = Sxx22op;
        d.state.Szz11mo_inn = Szz11mo; d.state.Szz11po_inn = Szz11po;
        d.state.Szz22mo_inn = Szz22mo; d.state.Szz22po_inn = Szz22po;

        // store shear inn (non-rotated & rotated copy for completeness)
        d.state.Sxz12mo_inn = Sxz12mo; d.state.Sxz12po_inn = Sxz12po;
        d.state.Sxz12om_inn = Sxz12om; d.state.Sxz12op_inn = Sxz12op;
        d.state.Sxz21mo_inn = Sxz21mo; d.state.Sxz21po_inn = Sxz21po;
        d.state.Sxz21om_inn = Sxz21om; d.state.Sxz21op_inn = Sxz21op;

        // 使用完整的应力旋转函数，考虑剪切应力分量
        auto apply_full_rot = [&](int nbrIdx, const Matrix &rot,
                               Vector &sxx, Vector &szz, Vector &sxz) {
            if (nbrIdx <= 0) return; // 修改为nbrIdx <= 0，与MATLAB的nbrIdx != 0更一致
            rotate_full_stress(sxx, szz, sxz, rot);
        };

        // 对所有边界应用完整的应力旋转
        apply_full_rot(d.iNbr_mo, d.rot_mo, Sxx11mo, Szz11mo, Sxz12mo);
        apply_full_rot(d.iNbr_mo, d.rot_mo, Sxx22mo, Szz22mo, Sxz21mo);
        apply_full_rot(d.iNbr_po, d.rot_po, Sxx11po, Szz11po, Sxz12po);
        apply_full_rot(d.iNbr_po, d.rot_po, Sxx22po, Szz22po, Sxz21po);
        apply_full_rot(d.iNbr_om, d.rot_om, Sxx11om, Szz11om, Sxz12om);
        apply_full_rot(d.iNbr_om, d.rot_om, Sxx22om, Szz22om, Sxz21om);
        apply_full_rot(d.iNbr_op, d.rot_op, Sxx11op, Szz11op, Sxz12op);
        apply_full_rot(d.iNbr_op, d.rot_op, Sxx22op, Szz22op, Sxz21op);

        // store rotated normal stress components
        d.state.Sxx11mo_innr = Sxx11mo; d.state.Sxx11po_innr = Sxx11po;
        d.state.Sxx22mo_innr = Sxx22mo; d.state.Sxx22po_innr = Sxx22po;
        d.state.Szz11om_innr = Szz11om; d.state.Szz11op_innr = Szz11op;
        d.state.Szz22om_innr = Szz22om; d.state.Szz22op_innr = Szz22op;
        d.state.Sxx11om_innr = Sxx11om; d.state.Sxx11op_innr = Sxx11op;
        d.state.Sxx22om_innr = Sxx22om; d.state.Sxx22op_innr = Sxx22op;
        d.state.Szz11mo_innr = Szz11mo; d.state.Szz11po_innr = Szz11po;
        d.state.Szz22mo_innr = Szz22mo; d.state.Szz22po_innr = Szz22po;

        // store rotated shear stress components
        d.state.Sxz12mo_innr = Sxz12mo; d.state.Sxz12po_innr = Sxz12po;
        d.state.Sxz12om_innr = Sxz12om; d.state.Sxz12op_innr = Sxz12op;
        d.state.Sxz21mo_innr = Sxz21mo; d.state.Sxz21po_innr = Sxz21po;
        d.state.Sxz21om_innr = Sxz21om; d.state.Sxz21op_innr = Sxz21op;
    }
    return OM;
} 