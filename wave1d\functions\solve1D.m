function [OM, receiver]=solve1D(OM,source,receiver,nt,dt)
    
fprintf("\n... Entering the solver1D...\n")    

    % initializaiton
    for it = 1:nt

        % update wavefield
        OM = update_wavefields1d(OM,dt);

        [OM] = compute_boundary_Uvalue_inn1d(OM);


        [OM] = compute_boundary_Uvalue_out1d(OM);

        % from displacement to stress
        [OM] = compute_KU1d(OM);

        %%%%%%%%%%%%%%%%%%%%%%%%%
        % transfer Orthonormal bases to Canonical B-spline bases
        % compute boundary variables before communication
        [OM] = compute_boundary_Svalue_inn1d(OM);
        %%%%%%%%%%%%%%%%%%%%%%%%%

        %%%%%%%%%%%%%%%%%%%%%%%%%
        % communication boundary variables
        [OM] = compute_boundary_Svalue_out1d(OM);
        %%%%%%%%%%%%%%%%%%%%%%%%%

        %%%%%%%%%%%%%%%%%%%%%%%
        % from stress to acceleration
        [OM] = compute_KS1d(OM);
        %%%%%%%%%%%%%%%%%%%%%%%

        %%%%%%%%%%%%%%%%%%%%%%%
        % Inject the source
        [OM] = add_source1d(OM,source,it);

        %%%%%%%%%%%%%%%%%%%%%%%
        % Saving all the wavefield
        OM=save_wavefields1d(OM,it);
        %%%%%%%%%%%%%%%%%%%%%%%

        %%%%%%%%%%%%%%%%%%%%%%%
        % Saving the waveform of all the receivers
        receiver = save_waveforms1d(OM,receiver,it);
        %%%%%%%%%%%%%%%%%%%%%%%

        %%%%%%%%%%%%%%%%%%%%%%%
        % for checking the stability and plotting the wavefield
        if rem(it,200)==0
            check_stability(OM,it);
            plot_wavefields1d(OM,it,dt)
        end
     
    end

end




% function find_faces(OM)
% % find the faces of the neighbor elements
% ndomain_Faces(2:ndomains,1)    = 1;
% ndomain_Faces(1:ndomains-1,2)  = 2;
%
% end


%
%
% function gen_effective_properties_pts(OM)
%
% end
