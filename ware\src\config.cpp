#include "../include/config.hpp"
#include <iostream>
#include <cstdlib> // getenv

// We rely on toml11 (already vendored in the project).
#include <toml.hpp>

static SimulationConfig load_cfg(){
    SimulationConfig cfg; // defaults already set
    // Search order:
    // 1) environment variable SIM_CONFIG_PATH
    // 2) fallback path "config/config.toml" relative to executable cwd
    std::string path;
    if(const char *env = std::getenv("SIM_CONFIG_PATH")){
        path = env;
    }else{
        path = "config/config.toml";
    }

    try{
        auto tbl = toml::parse(path);
        if(tbl.contains("ppw"))      cfg.ppw      = toml::find<double>(tbl, "ppw");
        if(tbl.contains("frequency"))cfg.frequency = toml::find<double>(tbl, "frequency");
        std::cout << "[config] loaded " << path << ": ppw=" << cfg.ppw << ", frequency=" << cfg.frequency << "\n";
    }catch(const std::exception &ex){
        std::cerr << "[config] warning: " << ex.what() << " (using defaults ppw="
                  << cfg.ppw << ", frequency=" << cfg.frequency << ")\n";
    }
    return cfg;
}

const SimulationConfig &get_simulation_config(){
    static SimulationConfig cfg = load_cfg();
    return cfg;
} 