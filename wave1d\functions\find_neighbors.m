function OM=find_neighbors(OM)
    % find the connected elments
    ndomains = length(OM);
    for iom = 1:ndomains
        OM(iom).iNbrm = 0;
        OM(iom).iNbrp = 0;
    end

    for iom = 1:ndomains
        ix1 = OM(iom).model1d.xa(1);
        ix2 = OM(iom).model1d.xa(end);
        for jom = 1:ndomains
            jx1 = OM(jom).model1d.xa(1);
            jx2 = OM(jom).model1d.xa(end);

            if ix1 == jx2
                OM(iom).iNbrm = jom;
                OM(jom).iNbrp = iom;
            end

            if ix2 == jx1
                OM(jom).iNbrm = iom;
                OM(iom).iNbrp = jom;
            end

        end
    end

end
