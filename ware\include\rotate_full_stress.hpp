#pragma once
#include "common_types.hpp"

/**
 * @brief 对完整应力张量进行旋转
 * 
 * 此函数实现了完整的应力张量旋转，包括剪切应力分量，与MATLAB版本兼容
 * 
 * @param Sxx 应力分量 σxx
 * @param Szz 应力分量 σzz
 * @param Sxz 应力分量 σxz (剪切应力)
 * @param rot 2×2旋转矩阵
 */
void rotate_full_stress(Matrix &Sxx, Matrix &Szz, Matrix &Sxz, const Matrix &rot);

/**
 * @brief 对应力边界向量进行旋转
 * 
 * 此函数实现了边界应力向量的旋转，包括剪切应力分量，与MATLAB版本兼容
 * 
 * @param Sxx 应力分量 σxx
 * @param Szz 应力分量 σzz
 * @param Sxz 应力分量 σxz (剪切应力)
 * @param rot 2×2旋转矩阵
 */
void rotate_full_stress(Vector &Sxx, Vector &Szz, Vector &Sxz, const Matrix &rot); 