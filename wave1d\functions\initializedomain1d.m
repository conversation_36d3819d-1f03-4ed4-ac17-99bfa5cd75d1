function OM = initializedomain1d(OM,nt)
%INITIALIZEDOMAINSTATE1D Summary of this function goes here
%   Detailed explanation goes here
ndomains = length(OM);

for iom = 1:ndomains
    fprintf("... Initialize the state of %d Domain ... \n",iom);
    Nx1 = OM(iom).model1d.Nx1;
    Nx2 = Nx1 - 1;

    OM(iom).state.U1_0    = zeros(Nx1,1);
    OM(iom).state.U1_1    = zeros(Nx1,1);
    OM(iom).state.U1      = zeros(Nx1,1);
    OM(iom).state.Ub1     = zeros(Nx1,1);
    OM(iom).state.dU2dtt1 = zeros(Nx1,1);
    OM(iom).state.dS2dx1  = zeros(Nx2,1);

    OM(iom).state.dU1dx2  = zeros(Nx2,1);
    OM(iom).state.S2      = zeros(Nx2,1);
    OM(iom).state.Sb2     = zeros(Nx2,1);

    OM(iom).state.Ubval_xm     = 0;
    OM(iom).state.Ubval_xp     = 0;
    OM(iom).state.Ubval_xm_inn = 0;
    OM(iom).state.Ubval_xp_inn = 0;
    OM(iom).state.Ubval_xm_out = 0;
    OM(iom).state.Ubval_xp_out = 0;

    OM(iom).state.Sbval_xm     = 0;
    OM(iom).state.Sbval_xp     = 0;
    OM(iom).state.Sbval_xm_inn = 0;
    OM(iom).state.Sbval_xp_inn = 0;
    OM(iom).state.Sbval_xm_out = 0;
    OM(iom).state.Sbval_xp_out = 0;

    OM(iom).state.Umid = zeros(Nx1,floor(nt));
end

for iom = 1:ndomains

    iNbrm = OM(iom).iNbrm;
    iNbrp = OM(iom).iNbrp;
    if  iNbrm == 0
        OM(iom).alpha_xm = 0;
    else
        OM(iom).alpha_xm = 0.5;
        fprintf("... set the alpha_xm of %d Domain ... \n",iom);
    end

    if iNbrp == 0
        OM(iom).alpha_xp = 0;
    else
        OM(iom).alpha_xp = 0.5;
        fprintf("... set the alpha_xp of %d Domain ... \n",iom);
    end

end


end

