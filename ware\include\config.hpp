#pragma once
#include "common_types.hpp"

// Global simulation parameters loaded from a TOML configuration file.
// Currently we care only about `ppw` (points per wavelength) and `frequency` (dominant source frequency).
struct SimulationConfig {
    Real ppw{5.0};       // default: 5 points per wavelength
    Real frequency{40.0}; // default: 40 Hz (matches previous hard-coded fallback)
};

// Retrieve the global simulation configuration. The configuration is loaded
// exactly once on the first call (thread-safe since C++11). Further calls
// return the same instance.
const SimulationConfig &get_simulation_config(); 