﻿#ifndef GLLNODES_H
#define GLLNODES_H

#include <vector>
#include <utility>
#include <cmath>
#include <algorithm>
#include "legacy_defs.h"

inline std::pair<std::vector<double>,std::vector<double>> gllnodes(int N){
    int N1=N+1;
    std::vector<double> x(N1);
    for(int i=0;i<=N;++i) x[i]=std::cos(M_PI*i/N);
    std::vector<std::vector<double>> P(N1,std::vector<double>(N1));
    std::vector<double> xold(N1,2.0);
    const double eps=2.220446049250313e-16;
    while(true){
        double maxd=0.0;
        for(int i=0;i<N1;++i) maxd=std::max(maxd,std::abs(x[i]-xold[i]));
        if(maxd<=eps) break;
        xold=x;
        for(int i=0;i<N1;++i){P[i][0]=1.0;P[i][1]=x[i];}
        for(int k=1;k<N;++k){
            for(int i=0;i<N1;++i){
                P[i][k+1]=((2*k+1)*x[i]*P[i][k]-k*P[i][k-1])/(k+1);
            }
        }
        for(int i=0;i<N1;++i){x[i]=xold[i]-(x[i]*P[i][N]-P[i][N-1])/(N1*P[i][N]);}
    }
    std::vector<double> w(N1);
    for(int i=0;i<N1;++i) w[i]=2.0/(N*N1*P[i][N]*P[i][N]);
    for(int i=0;i<N1/2;++i){std::swap(x[i],x[N1-1-i]);std::swap(w[i],w[N1-1-i]);}
    return {x,w};
}

#endif 