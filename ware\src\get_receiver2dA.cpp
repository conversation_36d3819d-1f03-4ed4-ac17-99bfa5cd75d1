#include "../include/get_receiver2dA.hpp"
#include "../include/bspln.hpp"
#include "../include/Get_Knot_Vector.hpp"
#include <iostream>
#include <vector>

// 创建空的接收器结构体
Receiver2dA create_Receiver2dA() {
    Receiver2dA rece;
    return rece;
}

std::vector<Receiver2dA> get_receiver2dA(const std::vector<Domain2dA>& OM, 
                                       Real dt, 
                                       Integer nt) {
    // 创建接收器数组
    std::vector<Receiver2dA> receivers;
    
    // 创建一个接收器
    Receiver2dA rece = create_Receiver2dA();
    
    // 初始化ur数组，对应MATLAB中的 rece.ur = zeros(nt,3)
    rece.ur = Vector(nt);
    
    // 设置时间数组，对应MATLAB中的 rece.ur(:,1) = (1:nt)'*dt
    rece.time.resize(nt);
    for (Integer i = 0; i < nt; ++i) {
        rece.time[i] = (i + 1) * dt;
    }
    
    // 设置接收器所在域
    rece.iom = 0;  // 使用0-based索引，对应MATLAB的1
    Integer rom = rece.iom;
    
    // 接收器的局部坐标
    rece.xlocal = 1.0;  // 和MATLAB版本一致
    rece.zlocal = 1.0;  // 和MATLAB版本一致
    
    Real xs = rece.xlocal;
    Real zs = rece.zlocal;
    
    // 检查索引有效性
    if (rom >= static_cast<Integer>(OM.size())) {
        std::cerr << "false(" << rom << ")too many" << OM.size() << std::endl;
        // 添加到返回数组并返回，避免后续错误
        receivers.push_back(rece);
        return receivers;
    }
    
    // 获取域参数
    Integer Nx1 = OM[rom].Nx1;
    Integer Nx2 = Nx1 - 1;
    Integer Nz1 = OM[rom].Nz1;
    Integer Nz2 = Nz1 - 1;
    
    Integer px1 = OM[rom].px1;
    Integer px2 = px1 - 1;
    Integer pz1 = OM[rom].pz1;
    Integer pz2 = pz1 - 1;
    
    Integer kx1 = px1 + 1;
    Integer kz1 = pz1 + 1;
    Integer kx2 = px2 + 1;
    Integer kz2 = pz2 + 1;
    
    // 获取节点向量
    Vector tx1 = Get_Knot_Vector(Nx1, kx1);
    Vector tx2 = Get_Knot_Vector(Nx2, kx2);
    Vector tz1 = Get_Knot_Vector(Nz1, kz1);
    Vector tz2 = Get_Knot_Vector(Nz2, kz2);
    
    // 计算B样条基函数值
    rece.rbx1 = Vector(Nx1);
    rece.rbx2 = Vector(Nx2);
    rece.rbz1 = Vector(Nz1);
    rece.rbz2 = Vector(Nz2);
    
    for (Integer i = 0; i < Nx1; ++i) {
        rece.rbx1[i] = bspln(tx1, Nx1, i, kx1, xs);
    }
    
    for (Integer i = 0; i < Nx2; ++i) {
        rece.rbx2[i] = bspln(tx2, Nx2, i, kx2, xs);
    }
    
    for (Integer j = 0; j < Nz1; ++j) {
        rece.rbz1[j] = bspln(tz1, Nz1, j, kz1, zs);
    }
    
    for (Integer j = 0; j < Nz2; ++j) {
        rece.rbz2[j] = bspln(tz2, Nz2, j, kz2, zs);
    }
    
    // 初始化波形数组
    rece.trace_u12.resize(nt, 0.0);
    rece.trace_u21.resize(nt, 0.0);
    
    // 添加到接收器数组
    receivers.push_back(rece);
    
    return receivers;
} 