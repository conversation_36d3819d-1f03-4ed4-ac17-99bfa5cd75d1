#include "../include/create_domain2dA.hpp"

/**
 * @file create_domain2dA.cpp
 * @brief Implementation of domain creation (translated from MATLAB create_domain2dA.m)
 */

Domain2dA create_domain2dA() {
    Domain2dA domain;

    // Initialize grid size defaults
    domain.model2dA.nx = 10;
    domain.model2dA.nz = 10;

    // Coordinate matrices
    domain.model2dA.xa = zeros(10, 10);
    domain.model2dA.za = zeros(10, 10);

    // Material properties defaults
    domain.model2dA.rho = Matrix(10, 10, 2000.0);
    domain.model2dA.mu  = Matrix(10, 10, 2000.0 * 2750.0 * 2750.0);

    // Simple coordinate grid (100 m spacing)
    for(Integer i = 0; i < 10; ++i) {
        for(Integer j = 0; j < 10; ++j) {
            domain.x2d11(i, j) = i * 100.0;
            domain.z2d11(i, j) = j * 100.0;
        }
    }

    // B-spline defaults (order p = 5)
    domain.px1 = 5;
    domain.pz1 = 5;
    domain.Nx1 = 10;
    domain.Nz1 = 10;

    // Geometry defaults
    domain.inflat = 0.44;

    return domain;
} 