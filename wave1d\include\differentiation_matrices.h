﻿#ifndef DIFFERENTIATION_MATRICES_H
#define DIFFERENTIATION_MATRICES_H

#include <vector>
#include <tuple>

std::tuple<std::vector<double>,                // x1d
          std::vector<std::vector<double>>,    // Bm
          std::vector<std::vector<double>>,    // mm11
          std::vector<std::vector<double>>,    // mm22
          std::vector<std::vector<double>>,    // kk12
          std::vector<std::vector<double>>>    // kk21
differentiation_matrices(int N, int p);

#endif 