clear; clc;
baseDir = fileparts(mfilename('fullpath'));

cppDir = fullfile(baseDir,'proj_wavecpp');   % C++
matDir = fullfile(baseDir,'proj_wave1d');    % MATLAB
numElem = 7;
frameSkip = 30;   
receFile = fullfile(matDir,'rece1.txt');
if exist(receFile,'file')
    rec = dlmread(receFile);
    if size(rec,1) >= 2
        dtSim = rec(2,1) - rec(1,1);
    else
        dtSim = 1; % fallback
    end
else
    dtSim = 1;
end

Mat = cell(1,numElem); Cpp = cell(1,numElem);
Xm  = cell(1,numElem); Xc  = cell(1,numElem);
for i=1:numElem
    d = load(fullfile(matDir,sprintf('wfs_element%d.mat',i)),'wfs','x1d');
    Mat{i}=d.wfs; Xm{i}=d.x1d(:);
    R  = dlmread(fullfile(cppDir,sprintf('wfs_element%d_cpp.txt',i)));
    Xc{i}=R(:,1);  Cpp{i}=R(:,2:end);
end
Nt = min(cellfun(@(A)size(A,2),Mat));
Nt = min(Nt, cellfun(@(A)size(A,2),Cpp));

figure('Name','C++ vs MATLAB Wavefield','Color','w');
for it=1:frameSkip:Nt
    xAll=[]; uC=[]; uM=[];
    for i=1:numElem
        xc = Xc{i}; uCseg = Cpp{i}(:,it);
        xm = Xm{i}; uMseg = Mat{i}(:,it);
        if numel(xc)~=numel(xm) || any(abs(xc-xm)>1e-6)
            uMseg = interp1(xm,uMseg,xc,'linear','extrap');
        end
        if i>1
            xc=xc(2:end); uCseg=uCseg(2:end); uMseg=uMseg(2:end);
        end
        xAll=[xAll; xc]; uC=[uC; uCseg]; uM=[uM; uMseg];
    end
    clf; hold on; box on;
    plot(xAll/1e3,uC,'b','LineWidth',1.4);  % C++
    plot(xAll/1e3,uM,'r--','LineWidth',1.4);% MATLAB
    xlabel('x [km]'); ylabel('Displacement');
    % Removed time title as per requirement
    legend({'C++','MATLAB'},'Location','north');
    xlim([-3500 3500]);
    drawnow;
end 