function plot_wavefields1d(OM,it,dt)
%PLOT_DOMAIN Summary of this function goes here


ndomains = length(OM);
figure(1), clf
hold on
for iom = 1:ndomains
    x1d  = OM(iom).model1d.x1d;
    Umid = OM(iom).state.Umid(:,it);
    plot(x1d/1e3,Umid,'k','LineWidth',1.5);
end
xlim([-3480,3480])
% ylim([-2500,2500]);
set(gca,'fontsize',15);
xlabel('x [km]');
ylabel('Displacement');
title([num2str(it*dt) 's']);
hold off
drawnow
end


