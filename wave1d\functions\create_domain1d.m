function domain1d = create_domain1d()

domain1d = struct('model1d', struct('xa',[],'x1d',[],'xp',[],'rho',[],'Vp',[],'lambda',[],'px',[],'Nx1',[]), ...
                  'state',   struct('U1_0',[],'U1_1',[],'U1',[],'Ub1',[],'dU2dtt1',[],'dU1dx2',[],'S2',[],'Sb2',[],'dS2dx1',[]), ...
                  'kk12'    ,[], ...
                  'kk21'    ,[], ...
                  'invL11'  ,[], ...
                  'invL22'  ,[], ...
                  'invLT11' ,[], ...
                  'invLT22' ,[], ...
                  'alpha_xm',[], ...
                  'alpha_xp',[], ...
                  'iNbrm'   ,[], ...
                  'iNbrp'   ,[], ...
                  'rho1'    ,[], ...
                  'Vp2'     ,[]);
end

