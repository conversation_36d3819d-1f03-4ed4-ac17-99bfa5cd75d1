#include "../include/compute_KS2dA.hpp"
#include "../include/pagemtimes.hpp"
#include "../include/common_types.hpp"
#include <omp.h>
#include <iostream>

// -----------------------------------------------------------------------------
// Helper wrappers (use existing Matrix utilities)
// -----------------------------------------------------------------------------
static inline Matrix matmul_local(const Matrix &A,const Matrix &B){ return pagemtimes(A,B); }
static inline Matrix permute_2d_local(const Matrix &A){ return transpose(A); }

/**
 * @brief x方向导数计算辅助函数 - 对应MATLAB中的dFdxp函数
 * 
 * MATLAB对应代码:
 * function dUdxp11 = dFdxp(kkx12,U21,invLx22T,invLx11,U21mo,U21po)
 *      Ub21    = pagemtimes(invLx22T,U21);
 *      dUdxp11 = pagemtimes(-kkx12,Ub21);
 *      dUdxp11(1,:)   = dUdxp11(1,:)   - reshape(U21mo,1,length(U21mo));
 *      dUdxp11(end,:) = dUdxp11(end,:) + reshape(U21po,1,length(U21po));
 *      dUdxp11 = pagemtimes(invLx11,dUdxp11);
 * end
 */
static Matrix dFdxp(const Matrix &kkx, const Matrix &Sxx, const Matrix &invLxT,
                    const Matrix &invLx, const Vector &Sxxmo, const Vector &Sxxpo)
{
    // 从正交基到B样条基函数
    Matrix Ub21 = matmul_local(invLxT, Sxx);

    // dUdxp11 = -kkx * Ub21
    Matrix negK = kkx * (-1.0); // 缩放系数
    Matrix dUdxp11 = matmul_local(negK, Ub21);

    // 边界条件处理
    // MATLAB: dUdxp11(1,:) = dUdxp11(1,:) - reshape(U21mo,1,length(U21mo));
    // MATLAB: dUdxp11(end,:) = dUdxp11(end,:) + reshape(U21po,1,length(U21po));
    if(dUdxp11.rows > 0){
        // 第一行边界处理 - MATLAB中使用reshape将向量转为行向量
        for(Integer j=0; j<dUdxp11.cols && j<Sxxmo.size(); ++j)
            dUdxp11(0,j) -= Sxxmo[j];

        // 最后一行边界处理 - MATLAB中使用reshape将向量转为行向量
        Integer last = dUdxp11.rows-1;
        for(Integer j=0; j<dUdxp11.cols && j<Sxxpo.size(); ++j)
            dUdxp11(last,j) += Sxxpo[j];
    }

    // 返回到正交基
    // MATLAB: dUdxp11 = pagemtimes(invLx,dUdxp11);
    dUdxp11 = matmul_local(invLx, dUdxp11);
    return dUdxp11;
}

/**
 * @brief z方向导数计算辅助函数 - 对应MATLAB中的dFdzp函数
 * 
 * MATLAB对应代码:
 * function dUdzp11 = dFdzp(kkz12,U12,invLzT22,invLz11,U12om,U12op)
 *     U12     = permute(U12,[2,1]);
 *     Ub12    = pagemtimes(invLzT22,U12);
 *     dUdzp11 = pagemtimes(-kkz12,Ub12);
 *     dUdzp11 = permute(dUdzp11,[2,1]);
 *     dUdzp11(:,1)   = dUdzp11(:,1)   - reshape(U12om,length(U12om),1);
 *     dUdzp11(:,end) = dUdzp11(:,end) + reshape(U12op,length(U12op),1);
 *     dUdzp11 = permute(dUdzp11,[2,1]);
 *     dUdzp11 = pagemtimes(invLz11,dUdzp11);
 *     dUdzp11 = permute(dUdzp11,[2,1]);
 * end
 */
static Matrix dFdzp(const Matrix &kkz12, const Matrix &Szz22, const Matrix &invLzT22,
                    const Matrix &invLz11, const Vector &Szz22om, const Vector &Szz22op)
{
    // MATLAB: U12 = permute(U12,[2,1]);
    Matrix U12perm   = permute_2d_local(Szz22);              // transpose
    
    // MATLAB: Ub12 = pagemtimes(invLzT22,U12);
    Matrix Ub12      = matmul_local(invLzT22, U12perm);
    
    // MATLAB: dUdzp11 = pagemtimes(-kkz12,Ub12);
    Matrix negK      = kkz12 * (-1.0);
    Matrix dUdzp11   = matmul_local(negK, Ub12);

    // MATLAB: dUdzp11 = permute(dUdzp11,[2,1]);
    dUdzp11 = permute_2d_local(dUdzp11); // transpose back

    // 边界条件处理
    // MATLAB: dUdzp11(:,1) = dUdzp11(:,1) - reshape(U12om,length(U12om),1);
    // MATLAB: dUdzp11(:,end) = dUdzp11(:,end) + reshape(U12op,length(U12op),1);
    if(dUdzp11.cols>0){
        // 第一列边界处理 - MATLAB中使用reshape将向量转为列向量
        for(Integer i=0; i<dUdzp11.rows && i<Szz22om.size(); ++i)
            dUdzp11(i,0) -= Szz22om[i];
        
        // 最后一列边界处理 - MATLAB中使用reshape将向量转为列向量
        Integer lastc = dUdzp11.cols-1;
        for(Integer i=0; i<dUdzp11.rows && i<Szz22op.size(); ++i)
            dUdzp11(i,lastc) += Szz22op[i];
    }

    // MATLAB: dUdzp11 = permute(dUdzp11,[2,1]);
    dUdzp11 = permute_2d_local(dUdzp11);
    
    // MATLAB: dUdzp11 = pagemtimes(invLz11,dUdzp11);
    dUdzp11 = matmul_local(invLz11, dUdzp11);
    
    // MATLAB: dUdzp11 = permute(dUdzp11,[2,1]);
    dUdzp11 = permute_2d_local(dUdzp11);

    return dUdzp11;
}

/**
 * @brief 主要计算函数 - 将应力计算为加速度 - 对应MATLAB中的compute_KS2dA函数
 * 
 * MATLAB对应代码:
 * function [OM] = compute_KS2dA(OM)
 *   for iom = 1:length(OM)
 *     ...提取矩阵...
 *     dSxxdxp21 = dFdxp(kkx21, Sxx11, invLxT11, invLx22, Sxx11mo, Sxx11po);
 *     dSxxdxp12 = dFdxp(kkx12, Sxx22, invLxT22, invLx11, Sxx22mo, Sxx22po);
 *     dSzzdzp21 = dFdzp(kkz12, Szz22, invLzT22, invLz11, Szz22om, Szz22op);
 *     dSzzdzp12 = dFdzp(kkz21, Szz11, invLzT11, invLz22, Szz11om, Szz11op);
 *     OM(iom).state.dU2dtt21 = dSxxdxp21 + dSzzdzp21;
 *     OM(iom).state.dU2dtt12 = dSxxdxp12 + dSzzdzp12;
 *   end
 * end
 */
std::vector<Domain2dA> compute_KS2dA(std::vector<Domain2dA>& OM)
{
    for(Integer iom=0;iom<static_cast<Integer>(OM.size());++iom){
        Domain2dA &dom = OM[iom];

        // Extract matrices - 提取矩阵
        const Matrix &kkx12 = dom.kkx12;
        const Matrix &kkz12 = dom.kkz12;
        const Matrix &kkx21 = dom.kkx21;
        const Matrix &kkz21 = dom.kkz21;

        const Matrix &invLx11 = dom.invLx11;
        const Matrix &invLz11 = dom.invLz11;
        const Matrix &invLx22 = dom.invLx22;
        const Matrix &invLz22 = dom.invLz22;
        const Matrix &invLxT11= dom.invLxT11;
        const Matrix &invLzT11= dom.invLzT11;
        const Matrix &invLxT22= dom.invLxT22;
        const Matrix &invLzT22= dom.invLzT22;

        // Removed metric tensors that aren't used in MATLAB code

        const Matrix &Sxx11 = dom.state.Sxx11;
        const Matrix &Szz11 = dom.state.Szz11;
        const Matrix &Sxx22 = dom.state.Sxx22;
        const Matrix &Szz22 = dom.state.Szz22;

        const Vector &Sxx11mo = dom.state.Sxx11mo;
        const Vector &Sxx22mo = dom.state.Sxx22mo;
        const Vector &Sxx11po = dom.state.Sxx11po;
        const Vector &Sxx22po = dom.state.Sxx22po;
        const Vector &Szz11om = dom.state.Szz11om;
        const Vector &Szz22om = dom.state.Szz22om;
        const Vector &Szz11op = dom.state.Szz11op;
        const Vector &Szz22op = dom.state.Szz22op;

        // 计算导数 - 完全按照MATLAB代码实现
        Matrix dSxxdxp21 = dFdxp(kkx21, Sxx11, invLxT11, invLx22, Sxx11mo, Sxx11po);
        Matrix dSxxdxp12 = dFdxp(kkx12, Sxx22, invLxT22, invLx11, Sxx22mo, Sxx22po);
        Matrix dSzzdzp21 = dFdzp(kkz12, Szz22, invLzT22, invLz11, Szz22om, Szz22op);
        Matrix dSzzdzp12 = dFdzp(kkz21, Szz11, invLzT11, invLz22, Szz11om, Szz11op);

        // 直接按照MATLAB代码计算，去除之前的metric tensor乘法和Jacobian/density缩放
        // MATLAB: OM(iom).state.dU2dtt21 = dSxxdxp21 + dSzzdzp21;
        // MATLAB: OM(iom).state.dU2dtt12 = dSxxdxp12 + dSzzdzp12;
        dom.state.dU2dtt21 = dSxxdxp21 + dSzzdzp21;
        dom.state.dU2dtt12 = dSxxdxp12 + dSzzdzp12;
    }

    return OM;
} 