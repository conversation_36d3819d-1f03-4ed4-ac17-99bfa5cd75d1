D:\try2\1\wave1d\main1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\main1d.obj
D:\try2\1\wave1d\scr\add_source1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\add_source1d.obj
D:\try2\1\wave1d\scr\bspln.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\bspln.obj
D:\try2\1\wave1d\scr\check_stability.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\check_stability.obj
D:\try2\1\wave1d\scr\compute_boundary_Svalue_inn1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\compute_boundary_Svalue_inn1d.obj
D:\try2\1\wave1d\scr\compute_boundary_Svalue_out1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\compute_boundary_Svalue_out1d.obj
D:\try2\1\wave1d\scr\compute_boundary_Uvalue_inn1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\compute_boundary_Uvalue_inn1d.obj
D:\try2\1\wave1d\scr\compute_boundary_Uvalue_out1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\compute_boundary_Uvalue_out1d.obj
D:\try2\1\wave1d\scr\compute_dt.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\compute_dt.obj
D:\try2\1\wave1d\scr\compute_KS1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\compute_KS1d.obj
D:\try2\1\wave1d\scr\compute_KU1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\compute_KU1d.obj
D:\try2\1\wave1d\scr\find_neighbors.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\find_neighbors.obj
D:\try2\1\wave1d\scr\gen_DFDMatrices.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\gen_DFDMatrices.obj
D:\try2\1\wave1d\scr\get_bandwidth.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\get_bandwidth.obj
D:\try2\1\wave1d\scr\get_receiver1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\get_receiver1d.obj
D:\try2\1\wave1d\scr\gllnodes.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\gllnodes.obj
D:\try2\1\wave1d\scr\initialize_domain1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\initialize_domain1d.obj
D:\try2\1\wave1d\scr\inner_product.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\inner_product.obj
D:\try2\1\wave1d\scr\lgwt.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\lgwt.obj
D:\try2\1\wave1d\scr\mass_matrix.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\mass_matrix.obj
D:\try2\1\wave1d\scr\mesh_sphere1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\mesh_sphere1d.obj
D:\try2\1\wave1d\scr\save_wavefields1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\save_wavefields1d.obj
D:\try2\1\wave1d\scr\save_waveforms1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\save_waveforms1d.obj
D:\try2\1\wave1d\scr\solve1D.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\solve1D.obj
D:\try2\1\wave1d\scr\stiffness_matrix.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\stiffness_matrix.obj
D:\try2\1\wave1d\scr\update_wavefields1d.cpp;D:\try2\1\wave1d\wave1d\x64\Debug\update_wavefields1d.obj
