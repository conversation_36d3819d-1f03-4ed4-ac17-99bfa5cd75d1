# EigenWrapper 函数使用示例

本文档提供了 `EigenWrapper` 命名空间中各函数的使用示例，帮助用户更好地理解如何使用这些函数进行矩阵计算。

## 基本矩阵操作

### 创建矩阵

```cpp
// 创建2x2的单位矩阵
Matrix I = EigenWrapper::identity(2);

// 创建对角矩阵
Vector diag_elements(3);
diag_elements(0) = 1.0;
diag_elements(1) = 2.0;
diag_elements(2) = 3.0;
Matrix D = EigenWrapper::diag(diag_elements);

// 提取矩阵的对角线元素
Matrix A(3, 3);
// ... 初始化矩阵A
Vector diag_A = EigenWrapper::diag(A);
```

### 矩阵分解

#### LU分解

```cpp
// 创建一个3x3矩阵
Matrix A(3, 3);
A(0,0) = 2; A(0,1) = 1; A(0,2) = 1;
A(1,0) = 4; A(1,1) = 3; A(1,2) = 3;
A(2,0) = 8; A(2,1) = 7; A(2,2) = 9;

// 计算LU分解
auto [L, U, P] = EigenWrapper::lu(A);

// 验证 P*A = L*U
Matrix PA = matmul(P, A);
Matrix LU = matmul(L, U);

// 打印结果
std::cout << "P*A:" << std::endl;
for (int i = 0; i < PA.rows; ++i) {
    for (int j = 0; j < PA.cols; ++j) {
        std::cout << PA(i, j) << " ";
    }
    std::cout << std::endl;
}

std::cout << "L*U:" << std::endl;
for (int i = 0; i < LU.rows; ++i) {
    for (int j = 0; j < LU.cols; ++j) {
        std::cout << LU(i, j) << " ";
    }
    std::cout << std::endl;
}
```

#### SVD分解

```cpp
// 创建一个矩阵
Matrix A(3, 2);
A(0,0) = 1; A(0,1) = 2;
A(1,0) = 3; A(1,1) = 4;
A(2,0) = 5; A(2,1) = 6;

// 计算SVD分解
auto [U, S, V] = EigenWrapper::svd(A);

// 验证 A = U*S*V^T
// 创建对角矩阵S_mat
Matrix S_mat(U.cols, V.cols, 0.0);
for (int i = 0; i < S.size(); ++i) {
    S_mat(i, i) = S(i);
}

Matrix US = matmul(U, S_mat);
Matrix USV = matmul(US, EigenWrapper::transpose(V));

// 打印结果
std::cout << "Original A:" << std::endl;
for (int i = 0; i < A.rows; ++i) {
    for (int j = 0; j < A.cols; ++j) {
        std::cout << A(i, j) << " ";
    }
    std::cout << std::endl;
}

std::cout << "Reconstructed A = U*S*V^T:" << std::endl;
for (int i = 0; i < USV.rows; ++i) {
    for (int j = 0; j < USV.cols; ++j) {
        std::cout << USV(i, j) << " ";
    }
    std::cout << std::endl;
}
```

## 矩阵空间函数

### 零空间计算

```cpp
// 创建一个秩为2的3×4矩阵
Matrix A(3, 4);
A(0,0) = 1; A(0,1) = 2; A(0,2) = 3; A(0,3) = 4;
A(1,0) = 5; A(1,1) = 6; A(1,2) = 7; A(1,3) = 8;
A(2,0) = 9; A(2,1) = 10; A(2,2) = 11; A(2,3) = 12;

// 计算零空间
Matrix N = EigenWrapper::null(A);

// 验证 A*N ≈ 0
Matrix AN = matmul(A, N);

// 计算范数
Real norm_AN = 0.0;
for (Integer i = 0; i < AN.rows; ++i)
    for (Integer j = 0; j < AN.cols; ++j)
        norm_AN += AN(i, j) * AN(i, j);
norm_AN = std::sqrt(norm_AN);

std::cout << "Nullspace dimension: " << N.cols << std::endl;
std::cout << "||A*N|| = " << norm_AN << std::endl; // 应该接近0
```

### 矩阵秩计算

```cpp
// 创建一个秩为2的矩阵
Matrix A(3, 4);
A(0,0) = 1; A(0,1) = 2; A(0,2) = 3; A(0,3) = 4;
A(1,0) = 5; A(1,1) = 6; A(1,2) = 7; A(1,3) = 8;
A(2,0) = 9; A(2,1) = 10; A(2,2) = 11; A(2,3) = 12;

// 计算秩
Integer r = EigenWrapper::rank(A);

std::cout << "Matrix A has rank: " << r << std::endl; // 应该是2
```

### 列空间的正交基

```cpp
// 创建一个矩阵
Matrix A(4, 2);
A(0,0) = 1; A(0,1) = 5;
A(1,0) = 2; A(1,1) = 6;
A(2,0) = 3; A(2,1) = 7;
A(3,0) = 4; A(3,1) = 8;

// 计算列空间的正交基
Matrix Q = EigenWrapper::orth(A);

// 验证Q的列是正交的
Matrix QTQ = matmul(EigenWrapper::transpose(Q), Q);

std::cout << "Q^T * Q:" << std::endl;
for (int i = 0; i < QTQ.rows; ++i) {
    for (int j = 0; j < QTQ.cols; ++j) {
        std::cout << QTQ(i, j) << " ";
    }
    std::cout << std::endl;
}
// 对角线元素应为1，非对角线元素应接近0
```

## 特殊矩阵函数

### 矩阵指数

```cpp
// 创建一个矩阵
Matrix A(2, 2);
A(0,0) = 0; A(0,1) = 1;
A(1,0) = -1; A(1,1) = 0;

// 计算矩阵指数 e^A
Matrix expA = EigenWrapper::expm(A);

std::cout << "e^A:" << std::endl;
for (int i = 0; i < expA.rows; ++i) {
    for (int j = 0; j < expA.cols; ++j) {
        std::cout << expA(i, j) << " ";
    }
    std::cout << std::endl;
}
// 对于这个特定的矩阵，结果应该接近旋转矩阵
```

### 矩阵对数

```cpp
// 创建一个正定矩阵
Matrix A(2, 2);
A(0,0) = 2; A(0,1) = 1;
A(1,0) = 1; A(1,1) = 2;

// 计算矩阵对数 log(A)
Matrix logA = EigenWrapper::logm(A);

std::cout << "log(A):" << std::endl;
for (int i = 0; i < logA.rows; ++i) {
    for (int j = 0; j < logA.cols; ++j) {
        std::cout << logA(i, j) << " ";
    }
    std::cout << std::endl;
}

// 验证 e^(log(A)) ≈ A
Matrix expLogA = EigenWrapper::expm(logA);
```

## 复数矩阵支持

```cpp
// 创建复数矩阵
Matrix real_part(2, 2), imag_part(2, 2);
real_part(0,0) = 1; real_part(0,1) = 2;
real_part(1,0) = 3; real_part(1,1) = 4;
imag_part(0,0) = 0.1; imag_part(0,1) = 0.2;
imag_part(1,0) = 0.3; imag_part(1,1) = 0.4;

ComplexMatrix A(real_part, imag_part);

// 复数矩阵乘法
ComplexMatrix B = A * Complex(2.0, 1.0); // 标量乘法

// 提取实部和虚部
Matrix A_real = real(A);
Matrix A_imag = imag(A);

// 共轭转置
ComplexMatrix AH = A.ctranspose();
```

## 错误处理示例

```cpp
// 设置错误处理选项
MatlabError::ErrorOptions options;
options.verbose = true;
options.throw_exceptions = true;
options.log_to_file = true;
options.log_file = "matrix_errors.log";
MatlabError::set_error_options(options);

try {
    // 尝试求解奇异矩阵的逆
    Matrix A(2, 2);
    A(0,0) = 1; A(0,1) = 2;
    A(1,0) = 2; A(1,1) = 4; // 线性相关的行，矩阵奇异
    
    Matrix Ainv = EigenWrapper::inv(A);
} catch (const std::exception& e) {
    std::cerr << "捕获到异常: " << e.what() << std::endl;
}

// 使用维度不匹配的矩阵进行乘法
Matrix A(2, 3);
Matrix B(4, 2);
try {
    Matrix C = matmul(A, B); // 将抛出异常：A.cols != B.rows
} catch (const std::exception& e) {
    std::cerr << "捕获到异常: " << e.what() << std::endl;
}
```

## 性能优化示例

### 使用原地矩阵乘法减少内存分配

```cpp
// 准备大型矩阵
const int size = 1000;
Matrix A(size, size, 1.0);
Matrix B(size, size, 2.0);
Matrix C(size, size); // 结果矩阵

// 使用原地乘法，避免临时对象
EigenWrapper::inplace_matrix_mult(C, A, B);

// 对比常规乘法（会创建临时对象）
Matrix D = matmul(A, B);
``` 