function [b] = bspln(t,n,i,k,x)
% BSPLN Summary of this function goes here
% n: number of basis function
% i: index of the basis function to evaluate
% k: B-spline order = polynomial_order + 1
% t: Knots vector with dimension t(n+k)
% x: Location at which one wants to evaluate the Basis function

if (k==1)
    if x>t(i) && ~(x>t(i+1))
        b=1;
    else
        b=0;
    end
else
    if abs(t(i+k-1) - t(i)) > eps
        c1 = (x-t(i))/(t(i+k-1)-t(i));
    else
        c1 = 0;
    end

    if abs(t(i+k) - t(i+1)) > eps
        c2 = (t(i+k)-x)/(t(i+k)-t(i+1));
    else
        c2 = 0;
    end
    b=c1*bspln(t,n,i,k-1,x)+c2*bspln(t,n,i+1,k-1,x);
end

end
