@echo off
echo Setting up Visual Studio environment...
call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"

echo.
echo Creating build directory...
if not exist build mkdir build

echo.
echo Configuring with CMake...
cd build
cmake .. -G "Visual Studio 17 2022" -A x64

echo.
echo Building with CMake...
cmake --build . --config Debug

echo.
echo Build completed.
cd ..
pause
