/**
 * @file compute_KS1d.cpp
 * @brief 优化的应力→加速度转换计算 - 并行版本
 *
 * 核心算法: dU2dtt1 = invL11 * (-kk12 * invLT22 * S2 + 边界条件)
 * 功能: 将应力场转换为加速度场，用于时间步进
 * 优化: OpenMP并行化 + 内存优化 + 向量化计算
 */

#include "compute_KS1d.hpp"
#include "matrix.hpp"
#include <iostream>
#include <algorithm>
#include <cmath>

#ifdef _OPENMP
#include <omp.h>
#endif

namespace wave {

void compute_KS1d(std::vector<Domain1D>& domains)
{
    const int ndomains = static_cast<int>(domains.size());
    if(ndomains == 0) return;

    // OpenMP并行化: 域级并行，每个线程处理不同的域
    #ifdef _OPENMP
    #pragma omp parallel for schedule(static) default(none) shared(domains, ndomains)
    #endif
    for(int idx = 0; idx < ndomains; ++idx)
    {
        auto &dom = domains[static_cast<std::size_t>(idx)];

        // 获取输入矩阵和数据
        const Matrix &kk12 = dom.kk12;
        const Matrix &invLT22 = dom.invLT22;
        const Matrix &invL11 = dom.invL11;
        const std::vector<double> &S2 = dom.state.S2;
        const double Sbval_xm = dom.state.Sbval_xm;
        const double Sbval_xp = dom.state.Sbval_xp;

        // 第一步: 检查数据有效性
        if(S2.empty() || invLT22.cols == 0 || invLT22.cols != S2.size()) {
            dom.state.dU2dtt1.assign(S2.size(), 0.0);
            continue;
        }

        // 第二步: 计算边界应力 Sb2 = invLT22 * S2
        std::vector<double> Sb2(invLT22.rows);
        for(size_t i = 0; i < invLT22.rows; ++i) {
            double sum = 0.0;
            for(size_t j = 0; j < invLT22.cols; ++j) {
                sum += invLT22(i, j) * S2[j];
            }
            Sb2[i] = sum;
        }

        // 第三步: 检查kk12矩阵维度
        if(kk12.cols == 0 || kk12.cols != Sb2.size()) {
            dom.state.dU2dtt1.assign(Sb2.size(), 0.0);
            continue;
        }

        // 第四步: 计算应力梯度 dS2dx1 = -kk12 * Sb2
        std::vector<double> dS2dx1(kk12.rows);
        for(size_t i = 0; i < kk12.rows; ++i) {
            double sum = 0.0;
            for(size_t j = 0; j < kk12.cols; ++j) {
                sum += kk12(i, j) * Sb2[j];
            }
            dS2dx1[i] = -sum;  // 添加负号
        }

        // 第五步: 添加边界条件
        if(!dS2dx1.empty()) {
            dS2dx1.front() -= Sbval_xm;  // 左边界
            dS2dx1.back() += Sbval_xp;   // 右边界
        }

        // 第六步: 检查invL11矩阵维度
        if(invL11.cols == 0 || invL11.cols != dS2dx1.size()) {
            dom.state.dU2dtt1.assign(dS2dx1.size(), 0.0);
            continue;
        }

        // 第七步: 计算最终加速度 dU2dtt1 = invL11 * dS2dx1
        dom.state.dU2dtt1.resize(invL11.rows);
        for(size_t i = 0; i < invL11.rows; ++i) {
            double sum = 0.0;
            for(size_t j = 0; j < invL11.cols; ++j) {
                sum += invL11(i, j) * dS2dx1[j];
            }
            // 内联数值稳定性处理
            dom.state.dU2dtt1[i] = std::isfinite(sum) ? sum : 0.0;
        }
    }

    // 调试信息输出: 仅在第一次调用时输出最大加速度值
    static bool first_call = true;
    if(first_call) {
        double max_acceleration = 0.0;
        for(const auto &dom : domains) {
            for(double v : dom.state.dU2dtt1) {
                max_acceleration = std::max(max_acceleration, std::fabs(v));
            }
        }
        std::cout << "[DEBUG] KS1d complete: max acceleration |dU2dtt1| = " << max_acceleration << std::endl;
        first_call = false;
    }
}

} // namespace wave