#include "compute_dt.hpp"
#include <algorithm>
#include <cmath>
#include <limits>
#include <iostream>

namespace wave {
std::pair<double, std::size_t> compute_dt(const std::vector<Domain1D>& domains, double duration, double CFL)
{
    double dt_min = std::numeric_limits<double>::max();
    for(const auto &dom : domains){
        int Nx = dom.model1d.Nx1;
        if(Nx < 2) continue;
        double dx = std::abs(dom.model1d.xa.back() - dom.model1d.xa.front()) / (Nx - 1);
        double vp_max = *std::max_element(dom.model1d.Vp.begin(), dom.model1d.Vp.end());
        if(vp_max <= 0.0) vp_max = 1.0;
        double dt_candidate = CFL * dx / vp_max;
        dt_min = std::min(dt_min, dt_candidate);
    }
    if(!std::isfinite(dt_min) || dt_min <= 0.0) dt_min = 0.001;
    std::size_t nt = static_cast<std::size_t>(std::ceil(duration / dt_min));
    std::cout << "[DEBUG] chosen dt = " << dt_min
          << "  nt = " << nt << std::endl;
    return {dt_min, nt};
}
}  