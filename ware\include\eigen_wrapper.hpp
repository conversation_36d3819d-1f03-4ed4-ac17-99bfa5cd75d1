#pragma once
#include "common_types.hpp"
#include <utility>  // 添加 std::pair
#include <tuple>    // 添加 std::tuple

namespace EigenWrapper {
// 基础矩阵操作
Matrix sqrtm(const Matrix& A);
Matrix transpose(const Matrix& A);
Vector matvec(const Matrix& A, const Vector& x);
Matrix inv(const Matrix& A);                     // 矩阵求逆
Matrix pinv(const Matrix& A, Real tol = -1.0);   // 矩阵伪逆
Matrix chol(const Matrix& A, bool lower = false);  // Cholesky分解
Matrix invSPD(const Matrix& A, Real eps=1e-12);
Vector element_divide(const Vector& a, const Vector& b);

// MATLAB兼容矩阵函数
Matrix identity(Integer n);                      // 单位矩阵 (eye函数的实现，避免名称冲突)
Matrix diag(const Vector& v);                    // 向量转为对角矩阵
Vector diag(const Matrix& A);                    // 提取矩阵对角线元素
Matrix triu(const Matrix& A, Integer k = 0);     // 上三角矩阵
Matrix tril(const Matrix& A, Integer k = 0);     // 下三角矩阵
Real norm(const Matrix& A, const std::string& type = "2"); // 矩阵范数
Real cond(const Matrix& A);                      // 条件数
Real det(const Matrix& A);                       // 行列式
std::pair<Vector, Matrix> eigSys(const Matrix& A);  // 特征值分解
std::tuple<Matrix, Vector, Matrix> svd(const Matrix& A);  // 奇异值分解
std::tuple<Matrix, Matrix, Matrix> lu(const Matrix& A);  // LU分解
Matrix expm(const Matrix& A);                    // 矩阵指数
Matrix logm(const Matrix& A);                    // 矩阵对数
Matrix kron(const Matrix& A, const Matrix& B);   // 克罗内克积

// 矩阵空间函数
Matrix null(const Matrix& A, Real tol = -1.0);   // 计算零空间的基
Integer rank(const Matrix& A, Real tol = -1.0);  // 计算矩阵的秩
Matrix orth(const Matrix& A, Real tol = -1.0);   // 计算列空间的正交基

// 优化的矩阵运算
Matrix mtimes(const Matrix& A, const Matrix& B); // 优化的矩阵乘法
Vector mtimes(const Matrix& A, const Vector& x); // 优化的矩阵向量乘法
Matrix mplus(const Matrix& A, const Matrix& B);  // 优化的矩阵加法
Matrix mminus(const Matrix& A, const Matrix& B); // 优化的矩阵减法
Vector reshape(const Matrix& A, Integer rows, Integer cols); // 矩阵重塑

// 内存优化函数
void inplace_matrix_mult(Matrix& C, const Matrix& A, const Matrix& B); // 原地矩阵乘法

// 额外的矩阵运算函数
Matrix create_identity(Integer n);                  // 创建单位矩阵
Matrix matmul(const Matrix& A, const Matrix& B);    // 矩阵乘法

// 类型转换函数
#ifdef USE_EIGEN
Eigen::MatrixXd toEigen(const Matrix& A);           // Matrix转Eigen::MatrixXd
Eigen::VectorXd toEigen(const Vector& v);           // Vector转Eigen::VectorXd
Matrix fromEigen(const Eigen::MatrixXd& A);         // Eigen::MatrixXd转Matrix
Vector fromEigen(const Eigen::VectorXd& v);         // Eigen::VectorXd转Vector
#endif

// 注意：hadamard方法已在Matrix类中定义，transpose方法也在Matrix类中定义
// 注意：矩阵运算符重载已在common_types.hpp中定义，此处不重复声明
}