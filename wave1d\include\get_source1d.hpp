#pragma once
#include <vector>
#include "domain_structs.hpp"
#include "create_source1d.hpp"
#include "bspln.h"
#include "Get_Knot_Vector.h"
#include <cmath>

#if __cpp_lib_numbers >= 201907L
#include <numbers>
#endif

namespace wave {
Source1D get_source1d(const std::vector<Domain1D>& domains, double freq, double dt, std::size_t nt, std::size_t som = 0);

inline Source1D get_source1d(const std::vector<Domain1D>& domains, double freq, double dt, std::size_t nt, std::size_t som) {
    Source1D src = create_source1d();
    if(som >= domains.size()) return src;
    src.iom = som;

    const auto &dom = domains[som];
    int Nx1 = dom.model1d.Nx1;
    int px  = dom.model1d.px;
    int kx1 = px + 1;
    if(Nx1 == 0) return src;

    double x1 = dom.model1d.xa.front();
    double x2 = dom.model1d.xa.back();

    std::vector<double> t1 = Get_Knot_Vector(Nx1, kx1);
    for(double &v : t1) v = v*(x2 - x1) + x1;

    std::vector<double> sgb1(Nx1);
    double xmid = 0.5*(x1 + x2);
    for(int i=0;i<Nx1;++i)
        sgb1[i] = bspln(t1, Nx1, i+1, kx1, xmid);

    bool allZero=true; for(double v:sgb1) if(std::fabs(v)>1e-14){ allZero=false; break; }
    if(allZero && !dom.b1T.a.empty()){
        int mid_idx = Nx1/2; 
        if(mid_idx >= dom.b1T.rows) mid_idx = dom.b1T.rows-1;
        for(int i=0;i<Nx1;++i) sgb1[i] = dom.b1T(mid_idx, i);
    }

    if(dom.invL11.rows == static_cast<std::size_t>(Nx1) && dom.invL11.cols == static_cast<std::size_t>(Nx1))
        src.invMsg1 = matvec(dom.invL11, sgb1);
    else
        src.invMsg1 = sgb1;

#ifdef _DEBUG
    double maxInv=0.0, maxSg=0.0;
    for(double v:src.invMsg1) if(std::fabs(v)>maxInv) maxInv=std::fabs(v);
    for(double v:sgb1) if(std::fabs(v)>maxSg) maxSg=std::fabs(v);
    std::cout << "[DEBUG] get_source1d: iom="<<som<<" max|invMsg1|="<<maxInv<<" max|sgb1|="<<maxSg<<std::endl;
#endif

#if __cpp_lib_numbers >= 201907L
    constexpr double PI = std::numbers::pi;
#else
    constexpr double PI = 3.14159265358979323846;
#endif
    src.Ft.resize(nt);
    for(std::size_t it=0; it<nt; ++it){
        double re = PI*freq*((it+1)*dt - 1.5/freq);
        if(std::fabs(re) > 25.0) {         
            src.Ft[it] = 0.0;
        } else {
            double e = std::exp(-re*re);
            src.Ft[it] = 1e5*(1 - 2*re*re)*e;
        }
    }
    return src;
}
} 