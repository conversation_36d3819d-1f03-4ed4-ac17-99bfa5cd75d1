#pragma once
#include <vector>
#include <utility>
#include <cmath>

namespace wave {
inline std::pair<std::vector<double>, std::vector<double>> lgwt(int N, double a, double b) {
    const double EPS = 1e-14;
    std::vector<double> x(N), w(N);
    int m = (N + 1) / 2; 
    for (int i = 0; i < m; ++i) {
 
        double z = std::cos(M_PI * (i + 0.75) / (N + 0.5));
        double z1;
        do {
            double p1 = 1.0, p2 = 0.0;
            for (int j = 1; j <= N; ++j) {
                double p3 = p2;
                p2 = p1;
                p1 = ((2.0 * j - 1.0) * z * p2 - (j - 1.0) * p3) / j;
            }
            double pp = N * (z * p1 - p2) / (z * z - 1.0);
            z1 = z;
            z = z1 - p1 / pp;
        } while (std::abs(z - z1) > EPS);
        x[i] = -z;
        x[N - 1 - i] = z;
    }

    for (int i = 0; i < N; ++i) {
        double z = x[i];

        double p1 = 1.0, p2 = 0.0;
        for (int j = 1; j <= N; ++j) {
            double p3 = p2;
            p2 = p1;
            p1 = ((2.0 * j - 1.0) * z * p2 - (j - 1.0) * p3) / j;
        }
        double pp = N * (z * p1 - p2) / (z * z - 1.0);
        w[i] = 2.0 / ((1.0 - z * z) * pp * pp);
        x[i] = (b - a) / 2.0 * z + (a + b) / 2.0;
        w[i] *= (b - a) / 2.0;
    }
    return {x, w};
}
}  