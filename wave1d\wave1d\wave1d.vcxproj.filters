﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\include\add_source1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\bspln.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\check_stability.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compute_boundary_Svalue_inn1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compute_boundary_Svalue_out1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compute_boundary_Uvalue_inn1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compute_boundary_Uvalue_out1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compute_dt.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compute_KS1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compute_KU1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\create_basis_parameters.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\create_domain1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\create_receiver1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\create_source1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\dbspln.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\differentiation_matrices.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\domain_structs.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\edndx.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\find_neighbors.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\gen_DFDMatrices.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\get_bandwidth.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\Get_Knot_Vector.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\get_receiver1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\get_source1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\gllnodes.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\initialize_domain1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\inner_product.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\legacy_defs.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\lgwt.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\lgwt.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\mass_matrix.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\matrix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\mesh_sphere1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\save_wavefields1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\save_waveforms1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\solve1D.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\stiffness_matrix.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\update_wavefields1d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compiler_optimization.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\scr\add_source1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\bspln.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\check_stability.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\compute_boundary_Svalue_inn1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\compute_boundary_Svalue_out1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\compute_boundary_Uvalue_inn1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\compute_boundary_Uvalue_out1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\compute_dt.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\compute_KS1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\compute_KU1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\find_neighbors.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\gen_DFDMatrices.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\get_bandwidth.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\get_receiver1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\gllnodes.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\initialize_domain1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\inner_product.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\lgwt.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\mass_matrix.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\mesh_sphere1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\save_wavefields1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\save_waveforms1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\solve1D.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\stiffness_matrix.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\scr\update_wavefields1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\main1d.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
</Project>