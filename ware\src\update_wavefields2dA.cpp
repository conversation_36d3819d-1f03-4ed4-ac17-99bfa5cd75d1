#include "../include/update_wavefields2dA.hpp"
#include "../include/common_types.hpp"
#include <iostream>
#include <cmath>
#include <omp.h>

std::vector<Domain2dA> update_wavefields2dA(std::vector<Domain2dA>& OM, Real dt)
{
    std::cout << "  [DEBUG] update_wavefields2dA: 开始更新，dt = " << dt << std::endl;
    const Real dt2 = dt * dt;
    bool found_bad_value = false;
    
    for(Integer iom = 0; iom < static_cast<Integer>(OM.size()); ++iom){
        Domain2dA &d = OM[iom];

        const Integer nx = d.state.U12.rows;
        const Integer nz = d.state.U12.cols;
        
        // 打印每个域的矩阵维度
        std::cout << "  [DEBUG] Domain " << iom << ": U12 size = " 
                  << nx << "x" << nz << ", dU2dtt12 size = " 
                  << d.state.dU2dtt12.rows << "x" << d.state.dU2dtt12.cols << std::endl;

        // Umid是3D数组，这里不需要分配
        // 在save_wavefields2dA中会根据需要分配

        // 检查加速度是否有NaN/Inf
        bool has_nan_acc = false;
        for(Integer i = 0; i < d.state.dU2dtt12.rows && !has_nan_acc; ++i) {
            for(Integer j = 0; j < d.state.dU2dtt12.cols && !has_nan_acc; ++j) {
                if(!std::isfinite(d.state.dU2dtt12(i,j)) || !std::isfinite(d.state.dU2dtt21(i,j))) {
                    has_nan_acc = true;
                    std::cout << "  [DEBUG] 警告: 发现NaN/Inf在域 " << iom << " 的加速度场 at (" 
                              << i << "," << j << "): dU2dtt12=" << d.state.dU2dtt12(i,j)
                              << ", dU2dtt21=" << d.state.dU2dtt21(i,j) << std::endl;
                }
            }
        }

        for(Integer i = 0; i < nx; ++i){
            for(Integer k = 0; k < nz; ++k){
                // -------- U12 grid (12) --------
                const Real prev1_U12 = d.state.U12_1(i, k); // U12 at t-Δt
                const Real prev0_U12 = d.state.U12_0(i, k); // U12 at t-2Δt
                const Real acc_U12   = d.state.dU2dtt12(i, k);

                const Real newU12 = 2.0 * prev1_U12 - prev0_U12 + dt2 * acc_U12;
                
                // 检查计算是否产生NaN/Inf
                if(!std::isfinite(newU12) && !found_bad_value) {
                    found_bad_value = true;
                    std::cout << "  [ERROR] 域 " << iom << " 中 U12(" << i << "," << k << ") 计算得到非法值: " 
                              << newU12 << std::endl;
                    std::cout << "    输入值: prev1_U12=" << prev1_U12 
                              << ", prev0_U12=" << prev0_U12 
                              << ", acc_U12=" << acc_U12 
                              << ", dt2=" << dt2 << std::endl;
                }

                d.state.U12_0(i, k) = prev1_U12; // roll buffers
                d.state.U12_1(i, k) = newU12;
                d.state.U12(i,  k)  = newU12;

                // -------- U21 grid (21) --------
                const Real prev1_U21 = d.state.U21_1(i, k);
                const Real prev0_U21 = d.state.U21_0(i, k);
                const Real acc_U21   = d.state.dU2dtt21(i, k);

                const Real newU21 = 2.0 * prev1_U21 - prev0_U21 + dt2 * acc_U21;
                
                // 检查计算是否产生NaN/Inf
                if(!std::isfinite(newU21) && !found_bad_value) {
                    found_bad_value = true;
                    std::cout << "  [ERROR] 域 " << iom << " 中 U21(" << i << "," << k << ") 计算得到非法值: " 
                              << newU21 << std::endl;
                    std::cout << "    输入值: prev1_U21=" << prev1_U21 
                              << ", prev0_U21=" << prev0_U21 
                              << ", acc_U21=" << acc_U21 
                              << ", dt2=" << dt2 << std::endl;
                }

                d.state.U21_0(i, k) = prev1_U21;
                d.state.U21_1(i, k) = newU21;
                d.state.U21(i,  k)  = newU21;

                // mid field - 这里不更新Umid，在save_wavefields2dA中处理
                // d.state.Umid在save_wavefields2dA中计算和存储
            }
        }

        // After using the accelerations to update displacement we reset them to zero
        // so that the next time step starts with fresh values (like MATLAB implementation).
        for(Integer i = 0; i < nx; ++i){
            for(Integer k = 0; k < nz; ++k){
                d.state.dU2dtt12(i, k) = 0.0;
                d.state.dU2dtt21(i, k) = 0.0;
            }
        }
    }

    if(found_bad_value) {
        std::cerr << "[update_wavefields2dA] 警告: 在更新波场时发现NaN/Inf" << std::endl;
    } else {
        std::cout << "  [DEBUG] update_wavefields2dA: 所有域波场更新完成" << std::endl;
    }

    return OM;
} 