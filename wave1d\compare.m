clear; clc;
rootDir = fileparts(mfilename("fullpath"));
cppData = readmatrix(fullfile(rootDir,"proj_wavecpp","rece_cpp.txt"));
matData = readmatrix(fullfile(rootDir,"proj_wave1d","rece1.txt"));
tCpp = cppData(:,1); uCpp = cppData(:,2);
tMat = matData(:,1); uMat = matData(:,2);
if ~isequal(tCpp,tMat)
    dtCpp = median(diff(tCpp)); dtMat = median(diff(tMat));
    if dtCpp < dtMat
        tRef = tCpp;
        uMat = interp1(tMat,uMat,tRef,"linear","extrap");
        t = tRef;
    else
        tRef = tMat;
        uCpp = interp1(tCpp,uCpp,tRef,"linear","extrap");
        t = tRef;
    end
else
    t = tCpp;
end
figure("Name","Receiver Waveforms","NumberTitle","off");
plot(t,uCpp,"b",t,uMat,"r--","LineWidth",1);
legend("C++","MATLAB","Location","best");
xlabel("Time (s)"); ylabel("Displacement");
grid on;