#include "../include/compute_dt2dA.hpp"
#include <iostream>
#include <cmath>
#include <algorithm>

std::pair<Real, Integer> compute_dt2dA(const std::vector<Domain2dA>& OM, Real duration) {
    // 初始化dt为很大的值
    Real dt = 9999999.0;
    // 设置CFL条件
    const Real CFL_condition = 0.1;
    
    for (size_t iom = 0; iom < OM.size(); ++iom) {
        const Domain2dA& dom = OM[iom];
        
        Integer Nx1 = dom.Nx1;
        Integer Nz1 = dom.Nz1;
        
        // 计算中心点的索引
        Integer ix = static_cast<Integer>(std::ceil((Nx1 + 1.0) / 2.0)) - 1; // -1转为0-based
        Integer iz = static_cast<Integer>(std::ceil((Nz1 + 1.0) / 2.0)) - 1; // -1转为0-based
        
        // 确保索引在有效范围内
        ix = std::min(ix, Nx1 - 2);
        iz = std::min(iz, Nz1 - 2);
        ix = std::max(ix, 0);
        iz = std::max(iz, 0);
        
        // 获取坐标点
        Real x1 = dom.x2d11(ix, iz);
        Real z1 = dom.z2d11(ix, iz);
        
        Real x2 = dom.x2d11(ix + 1, iz + 1);
        Real z2 = dom.z2d11(ix + 1, iz + 1);
        
        // 计算两点之间的距离
        Real dxz = std::sqrt((x2 - x1) * (x2 - x1) + (z2 - z1) * (z2 - z1));
        
        // 最大波速
        Real Vmax = 5000.0;
        
        // 根据CFL条件计算临时dt
        Real tmp = CFL_condition * dxz / Vmax;
        
        // 更新dt为所有域中的最小值
        dt = std::min(dt, tmp);
    }
    
    // 与MATLAB版本一致，强制设置dt=0.1
    dt = 0.1;
    
    // 计算总时间步数
    Integer nt = static_cast<Integer>(std::ceil(duration / dt));
    
    std::cout << "计算的时间步长dt = " << dt << ", 总时间步数nt = " << nt << std::endl;
    
    return std::make_pair(dt, nt);
} 