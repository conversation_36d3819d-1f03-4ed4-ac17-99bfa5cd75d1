/**
 * @file save_wavefields2dA.cpp
 * @brief 严格按照MATLAB save_wavefields2dA.m实现 - 波场可视化
 *
 * MATLAB原始代码逻辑：
 * 1. 从计算网格到物理网格的变换：
 *    Ub21 = tensorProduct2D(invLxT22, invLzT11, U21)
 *    Ub12 = tensorProduct2D(invLxT11, invLzT22, U12)
 * 2. 物理场计算：
 *    Umid1 = tensorProduct2D(bxT2, bzT1, Ub21)
 *    Umid2 = tensorProduct2D(bxT1, bzT2, Ub12)
 * 3. 合并不同网格：
 *    OM(iom).state.Umid(:,:,it) = (Umid1 + Umid2)/2
 *
 * 注意：MATLAB中Umid是3D数组，第三维是时间步
 * 本实现严格遵循MATLAB版本，不包含额外的文件输出功能
 */

#include "../include/save_wavefields2dA.hpp"
#include "../include/pagemtimes.hpp"

/**
 * @brief 张量积函数 - 严格对应MATLAB的tensorProduct2D
 *
 * MATLAB代码：
 * function C = tensorProduct2D(A, B, X)
 *     C = A * X * B';
 * end
 */
static Matrix tensorProduct2D(const Matrix& A, const Matrix& B, const Matrix& X) {
    // 步骤1: tmp = A * X
    Matrix tmp = pagemtimes(A, X);

    // 步骤2: C = tmp * B^T = A * X * B^T
    Matrix BT = B.transpose();
    return pagemtimes(tmp, BT);
}

/**
 * @brief 主函数 - 严格按照MATLAB save_wavefields2dA.m实现
 *
 * MATLAB原始逻辑：
 * for iom = 1:length(OM)
 *     % 从计算网格到物理网格的变换
 *     Ub21 = tensorProduct2D(invLxT22, invLzT11, U21);
 *     Ub12 = tensorProduct2D(invLxT11, invLzT22, U12);
 *     Umid1 = tensorProduct2D(bxT2, bzT1, Ub21);
 *     Umid2 = tensorProduct2D(bxT1, bzT2, Ub12);
 *     OM(iom).state.Umid(:,:,it) = (Umid1 + Umid2)/2;
 * end
 */
void save_wavefields2dA(std::vector<Domain2dA>& OM, Integer it, const std::string& dir)
{
    // 遍历所有域，严格按照MATLAB逻辑
    for(auto& dom : OM) {
        // ========== 提取必要的变换矩阵 ==========
        const Matrix& invLxT11 = dom.invLxT11;
        const Matrix& invLzT11 = dom.invLzT11;
        const Matrix& invLxT22 = dom.invLxT22;
        const Matrix& invLzT22 = dom.invLzT22;

        const Matrix& bxT1 = dom.bxT1;
        const Matrix& bzT1 = dom.bzT1;
        const Matrix& bxT2 = dom.bxT2;
        const Matrix& bzT2 = dom.bzT2;

        // ========== 提取当前位移场 ==========
        const Matrix& U21 = dom.state.U21;
        const Matrix& U12 = dom.state.U12;

        // ========== 步骤1: 从计算网格到B样条基 ==========
        // MATLAB: Ub21 = tensorProduct2D(invLxT22, invLzT11, U21)
        Matrix Ub21 = tensorProduct2D(invLxT22, invLzT11, U21);

        // MATLAB: Ub12 = tensorProduct2D(invLxT11, invLzT22, U12)
        Matrix Ub12 = tensorProduct2D(invLxT11, invLzT22, U12);

        // ========== 步骤2: 从B样条基到物理网格点 ==========
        // MATLAB: Umid1 = tensorProduct2D(bxT2, bzT1, Ub21)
        Matrix Umid1 = tensorProduct2D(bxT2, bzT1, Ub21);

        // MATLAB: Umid2 = tensorProduct2D(bxT1, bzT2, Ub12)
        Matrix Umid2 = tensorProduct2D(bxT1, bzT2, Ub12);

        // ========== 步骤3: 计算平均物理场 ==========
        // MATLAB: OM(iom).state.Umid(:,:,it) = (Umid1 + Umid2)/2
        Matrix newUmid(Umid1.rows, Umid1.cols);
        for(Integer i = 0; i < Umid1.rows; ++i) {
            for(Integer k = 0; k < Umid1.cols; ++k) {
                newUmid(i, k) = (Umid1(i, k) + Umid2(i, k)) / 2.0;
            }
        }

        // ========== 步骤4: 存储到3D数组中 ==========
        // 严格按照MATLAB版本：OM(iom).state.Umid(:,:,it) = (Umid1 + Umid2)/2
        // 确保Umid向量有足够的空间存储时间步it
        if (dom.state.Umid.size() <= static_cast<size_t>(it)) {
            dom.state.Umid.resize(it + 1);
        }
        dom.state.Umid[it] = newUmid;
    }
} 