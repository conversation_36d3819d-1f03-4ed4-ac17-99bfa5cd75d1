#include "solve1D.hpp"
#include "update_wavefields1d.hpp"
#include "compute_boundary_Uvalue_inn1d.hpp"
#include "compute_boundary_Uvalue_out1d.hpp"
#include "compute_KU1d.hpp"
#include "compute_boundary_Svalue_inn1d.hpp"
#include "compute_boundary_Svalue_out1d.hpp"
#include "compute_KS1d.hpp"
#include "add_source1d.hpp"
#include "save_wavefields1d.hpp"
#include "save_waveforms1d.hpp" 
#include "check_stability.hpp"
#include <cstddef>
#include <fstream>
#include <string>
#include <iomanip>
#include <filesystem>
#include <iostream> 


namespace wave {

static inline void compute_U_boundaries(std::vector<Domain1D>& doms)
{
    compute_boundary_Uvalue_inn1d(doms);
    compute_boundary_Uvalue_out1d(doms);
}

static inline void compute_dU1dx2_S2(std::vector<Domain1D>& doms)
{
    compute_KU1d(doms);
}

static inline void compute_S_boundaries(std::vector<Domain1D>& doms)
{
    compute_boundary_Svalue_inn1d(doms);
    compute_boundary_Svalue_out1d(doms);
}

static inline void compute_dU2dtt1(std::vector<Domain1D>& doms)
{
    compute_KS1d(doms);
}

}  

namespace wave {

void solve1D(std::vector<Domain1D>& domains,
             Source1D&               source,
             Receiver1D&             receiver,
             std::size_t             nt,
             double                  dt)
{
    if(receiver.ur.size() < nt) receiver.ur.assign(nt, 0.0);

    for(std::size_t it = 0; it < nt; ++it)
    {
   
        update_wavefields1d(domains, dt);

        
        for(auto &dom : domains){
            dom.state.Ub1 = matvec(dom.invLT11, dom.state.U1);
        }
        compute_U_boundaries(domains);    

        compute_dU1dx2_S2(domains);       
        compute_S_boundaries(domains);    

        compute_dU2dtt1(domains);         
        add_source1d(domains, source, it);

        save_wavefields1d(domains, it);
        
        if (receiver.iom < domains.size()) {
            const auto& domR = domains[receiver.iom];

            if(domR.state.Umid.rows != 0 && it < domR.state.Umid.cols){
                std::size_t rowIdx = domR.state.Umid.rows - 1; 
                receiver.ur[it] = domR.state.Umid(rowIdx, it);
            }
        }

        if((it + 1) % 200 == 0)
            check_stability(domains, it);
    }
}

}  