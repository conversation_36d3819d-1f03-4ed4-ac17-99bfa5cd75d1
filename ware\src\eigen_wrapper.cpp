#include "../include/eigen_wrapper.hpp"
#include "../include/common_types.hpp"
#include "../include/error_handling.hpp"
#include <stdexcept>
#include <cmath>
#include <string>
#include <iostream>
#include <algorithm>
#include <limits>
#include <vector>

#ifdef USE_EIGEN
#include <Eigen/Dense>
#include <Eigen/LU>
#include <Eigen/SVD>
#include <Eigen/Cholesky>
#include <Eigen/Eigenvalues>
#endif

namespace EigenWrapper {

// 常量定义
static constexpr Real EPS = 1e-15;

/**
 * @brief 检查矩阵是否为方阵
 */
inline void check_square(const Matrix& A) {
    if (A.rows != A.cols) {
        MatlabError::error("check_square", "Matrix must be square",
                       MatlabError::ErrorType::DimensionMismatch);
    }
}

// 错误处理函数
static void error_handler(const std::string& func_name, const std::string& message) {
    std::string error_msg = "Error in " + func_name + ": " + message;
    std::cerr << error_msg << std::endl;
    throw std::runtime_error(error_msg);
}

// MATLAB风格的警告处理函数
static void warning_handler(const std::string& func_name, const std::string& message) {
    std::cout << "Warning: " << func_name << " - " << message << std::endl;
    // 在实际应用中，可以添加日志记录或其他处理
}

/**
 * @brief 矩阵转置
 */
Matrix transpose(const Matrix& A) {
    Matrix AT(A.cols, A.rows);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < A.cols; ++j) {
            AT(j, i) = A(i, j);
        }
    }
    return AT;
}

/**
 * @brief 矩阵求逆
 */
Matrix inv(const Matrix& A) {
    try {
        check_square(A);
        Integer n = A.rows;

#ifdef USE_EIGEN
        Eigen::MatrixXd E(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                E(i, j) = A(i, j);
            }
        }

        // MATLAB风格的多层次容错矩阵求逆
        Eigen::FullPivLU<Eigen::MatrixXd> lu(E);

        // 第一层：标准LU分解
        if (lu.isInvertible()) {
            Eigen::MatrixXd Einv = lu.inverse();
            Matrix B(n, n);
            for (Integer i = 0; i < n; ++i) {
                for (Integer j = 0; j < n; ++j) {
                    B(i, j) = Einv(i, j);
                }
            }
            return B;
        }

        // 第二层：检查条件数并尝试正则化
        Eigen::JacobiSVD<Eigen::MatrixXd> svd(E, Eigen::ComputeFullU | Eigen::ComputeFullV);
        Eigen::VectorXd sigma = svd.singularValues();
        Real cond_num = sigma(0) / sigma(sigma.size()-1);

        if (cond_num > 1e12) {
            warning_handler("inv", "Matrix is ill-conditioned (cond=" + std::to_string(cond_num) + "). Using regularized inverse.");

            // 添加正则化项（MATLAB风格）
            Real reg_param = sigma(0) * std::numeric_limits<Real>::epsilon() * n;
            Eigen::MatrixXd E_reg = E + reg_param * Eigen::MatrixXd::Identity(n, n);

            Eigen::FullPivLU<Eigen::MatrixXd> lu_reg(E_reg);
            if (lu_reg.isInvertible()) {
                Eigen::MatrixXd Einv = lu_reg.inverse();
                Matrix B(n, n);
                for (Integer i = 0; i < n; ++i) {
                    for (Integer j = 0; j < n; ++j) {
                        B(i, j) = Einv(i, j);
                    }
                }
                return B;
            }
        }

        // 第三层：使用SVD伪逆（MATLAB最终回退方案）
        warning_handler("inv", "Matrix is singular to working precision. Using pseudo-inverse.");
        Real tol = std::max(E.rows(), E.cols()) * sigma(0) * std::numeric_limits<Real>::epsilon();

        Eigen::VectorXd sigma_inv = Eigen::VectorXd::Zero(sigma.size());
        for (Integer i = 0; i < sigma.size(); ++i) {
            if (sigma(i) > tol) {
                sigma_inv(i) = 1.0 / sigma(i);
            }
        }

        Eigen::MatrixXd Einv = svd.matrixV() * sigma_inv.asDiagonal() * svd.matrixU().transpose();
        Matrix B(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                B(i, j) = Einv(i, j);
            }
        }
        return B;
#else
        // 简单的高斯-约旦消元法
        Matrix B(n, n);
        Matrix C = A;
        
        // 初始化为单位矩阵
        for (Integer i = 0; i < n; ++i) {
            B(i, i) = 1.0;
        }
        
        // 高斯-约旦消元
        for (Integer i = 0; i < n; ++i) {
            Real pivot = C(i, i);
            if (std::abs(pivot) < 1e-12) {
                error_handler("inv", "Matrix is singular or ill-conditioned");
                return Matrix();
            }
            
            // 归一化当前行
            for (Integer j = 0; j < n; ++j) {
                C(i, j) /= pivot;
                B(i, j) /= pivot;
            }
            
            // 消元其他行
            for (Integer k = 0; k < n; ++k) {
                if (k != i) {
                    Real factor = C(k, i);
                    for (Integer j = 0; j < n; ++j) {
                        C(k, j) -= factor * C(i, j);
                        B(k, j) -= factor * B(i, j);
                    }
                }
            }
        }
        return B;
#endif
    }
    catch (const std::exception& e) {
        error_handler("inv", std::string("Matrix inversion failed: ") + e.what());
        return Matrix();
    }
}

/**
 * @brief 对称正定矩阵求逆
 */
Matrix invSPD(const Matrix& A, Real eps) {
    try {
        check_square(A);
        Integer n = A.rows;

#ifdef USE_EIGEN
        Eigen::MatrixXd E(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                E(i, j) = A(i, j);
            }
        }

        // 尝试Cholesky分解
        Eigen::LLT<Eigen::MatrixXd> llt(E);
        if (llt.info() == Eigen::Success) {
            Eigen::MatrixXd X = llt.solve(Eigen::MatrixXd::Identity(n, n));
            Matrix B(n, n);
            for (Integer i = 0; i < n; ++i) {
                for (Integer j = 0; j < n; ++j) {
                    B(i, j) = X(i, j);
                }
            }
            return B;
        } else {
            // 添加对角扰动后重试
            E.diagonal().array() += eps;
            llt.compute(E);
            if (llt.info() == Eigen::Success) {
                Eigen::MatrixXd X = llt.solve(Eigen::MatrixXd::Identity(n, n));
                Matrix B(n, n);
                for (Integer i = 0; i < n; ++i) {
                    for (Integer j = 0; j < n; ++j) {
                        B(i, j) = X(i, j);
                    }
                }
                return B;
            } else {
                // 回退到一般求逆
                return inv(A);
            }
        }
#else
        // 回退到一般求逆
        return inv(A);
#endif
    }
    catch (const std::exception& e) {
        error_handler("invSPD", std::string("SPD matrix inversion failed: ") + e.what());
        return Matrix();
    }
}

/**
 * @brief 矩阵平方根
 */
Matrix sqrtm(const Matrix& A) {
    try {
        check_square(A);
        Integer n = A.rows;
        
#ifdef USE_EIGEN
        Eigen::MatrixXd E(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                E(i, j) = A(i, j);
            }
        }
        
        // 使用特征值分解
        Eigen::SelfAdjointEigenSolver<Eigen::MatrixXd> eigenSolver(E);
        if (eigenSolver.info() != Eigen::Success) {
            error_handler("sqrtm", "Eigendecomposition failed");
            return Matrix();
        }

        Eigen::VectorXd D = eigenSolver.eigenvalues();
        Eigen::MatrixXd V = eigenSolver.eigenvectors();
        
        // 计算sqrt(D)
        for (Integer i = 0; i < n; ++i) {
            if (D(i) < 0) {
                warning_handler("sqrtm", "Matrix has negative eigenvalues");
                D(i) = 0;
            } else {
                D(i) = std::sqrt(D(i));
            }
        }
        
        // V * sqrt(D) * V^T
        Eigen::MatrixXd sqrtE = V * D.asDiagonal() * V.transpose();
        
        Matrix S(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                S(i, j) = sqrtE(i, j);
            }
        }
        return S;
        
#else
        // 简单的对角矩阵平方根
        Matrix S(n, n);
        for (Integer i = 0; i < n; ++i) {
            if (A(i, i) >= 0) {
                S(i, i) = std::sqrt(A(i, i));
            } else {
                warning_handler("sqrtm", "Negative diagonal element");
                S(i, i) = 0.0;
            }
        }
        return S;
#endif
    }
    catch (const std::exception& e) {
        error_handler("sqrtm", std::string("Matrix square root failed: ") + e.what());
        return Matrix();
    }
}

/**
 * @brief 矩阵向量乘法
 */
Vector matvec(const Matrix& A, const Vector& x) {
    if (A.cols != x.size()) {
        throw std::runtime_error("size mismatch");
    }
    Vector y(A.rows);
    for (Integer i = 0; i < A.rows; ++i) {
        Real s = 0.0;
        for (Integer j = 0; j < A.cols; ++j) {
            s += A(i, j) * x(j);
        }
        y(i) = s;
    }
    return y;
}

/**
 * @brief 向量逐元素除法
 */
Vector element_divide(const Vector& a, const Vector& b) {
    if (a.size() != b.size()) {
        throw std::runtime_error("size mismatch");
    }
    Vector c(a.size());
    for (Integer i = 0; i < a.size(); ++i) {
        c(i) = a(i) / (b(i) + EPS);
    }
    return c;
}

/**
 * @brief 创建单位矩阵
 */
Matrix identity(Integer n) {
    Matrix I(n, n, 0.0);
    for (Integer i = 0; i < n; ++i) {
        I(i, i) = 1.0;
    }
    return I;
}

Matrix create_identity(Integer n) {
    return identity(n);
}

/**
 * @brief 矩阵乘法
 */
Matrix matmul(const Matrix& A, const Matrix& B) {
    if (A.cols != B.rows) {
        MatlabError::error("matmul", "Matrix dimensions mismatch",
                       MatlabError::ErrorType::DimensionMismatch);
        return Matrix();
    }
    
    Matrix C(A.rows, B.cols);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < B.cols; ++j) {
            Real sum = 0.0;
            for (Integer k = 0; k < A.cols; ++k) {
                sum += A(i, k) * B(k, j);
            }
            C(i, j) = sum;
        }
    }
    return C;
}

// 矩阵伪逆
Matrix pinv(const Matrix& A, Real tol) {
    try {
        Integer m = A.rows;
        Integer n = A.cols;

#ifdef USE_EIGEN
        Eigen::MatrixXd E(m, n);
        for (Integer i = 0; i < m; ++i) {
            for (Integer j = 0; j < n; ++j) {
                E(i, j) = A(i, j);
            }
        }

        // 使用SVD计算伪逆
        Eigen::JacobiSVD<Eigen::MatrixXd> svd(E, Eigen::ComputeFullU | Eigen::ComputeFullV);
        Eigen::VectorXd singularValues = svd.singularValues();

        // 设置容差
        Real tolerance = (tol > 0) ? tol : EPS * std::max(m, n) * singularValues(0);

        // 计算伪逆的奇异值
        Eigen::VectorXd invSingularValues(singularValues.size());
        for (Integer i = 0; i < singularValues.size(); ++i) {
            if (singularValues(i) > tolerance) {
                invSingularValues(i) = 1.0 / singularValues(i);
            } else {
                invSingularValues(i) = 0.0;
            }
        }

        // 计算伪逆: A+ = V * S+ * U^T
        Eigen::MatrixXd pinvE = svd.matrixV() * invSingularValues.asDiagonal() * svd.matrixU().transpose();

        Matrix result(n, m);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < m; ++j) {
                result(i, j) = pinvE(i, j);
            }
        }
        return result;
#else
        // 简单实现：对于方阵使用inv，对于非方阵返回转置
        if (m == n) {
            return inv(A);
        } else {
            return transpose(A);
        }
#endif
    }
    catch (const std::exception& e) {
        error_handler("pinv", std::string("Pseudo-inverse failed: ") + e.what());
        return Matrix();
    }
}

// Cholesky分解
Matrix chol(const Matrix& A, bool lower) {
    try {
        check_square(A);
        Integer n = A.rows;

#ifdef USE_EIGEN
        Eigen::MatrixXd E(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                E(i, j) = A(i, j);
            }
        }

        Eigen::LLT<Eigen::MatrixXd> llt(E);
        if (llt.info() != Eigen::Success) {
            error_handler("chol", "Cholesky decomposition failed - matrix not positive definite");
            return Matrix();
        }

        Eigen::MatrixXd L = llt.matrixL();
        Matrix result(n, n);

        if (lower) {
            // 返回下三角矩阵
            for (Integer i = 0; i < n; ++i) {
                for (Integer j = 0; j <= i; ++j) {
                    result(i, j) = L(i, j);
                }
            }
        } else {
            // 返回上三角矩阵 (L^T)
            for (Integer i = 0; i < n; ++i) {
                for (Integer j = i; j < n; ++j) {
                    result(i, j) = L(j, i);
                }
            }
        }
        return result;
#else
        // 简单的Cholesky分解实现
        Matrix L(n, n);

        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j <= i; ++j) {
                if (i == j) {
                    Real sum = 0.0;
                    for (Integer k = 0; k < j; ++k) {
                        sum += L(j, k) * L(j, k);
                    }
                    Real val = A(j, j) - sum;
                    if (val <= 0) {
                        error_handler("chol", "Matrix not positive definite");
                        return Matrix();
                    }
                    L(j, j) = std::sqrt(val);
                } else {
                    Real sum = 0.0;
                    for (Integer k = 0; k < j; ++k) {
                        sum += L(i, k) * L(j, k);
                    }
                    L(i, j) = (A(i, j) - sum) / L(j, j);
                }
            }
        }

        if (lower) {
            return L;
        } else {
            return transpose(L);
        }
#endif
    }
    catch (const std::exception& e) {
        error_handler("chol", std::string("Cholesky decomposition failed: ") + e.what());
        return Matrix();
    }
}

#ifdef USE_EIGEN
// 类型转换函数实现
Eigen::MatrixXd toEigen(const Matrix& A) {
    Eigen::MatrixXd result(A.rows, A.cols);
    if (Matrix::is_col_major) {
        // 列优先存储，直接映射
        Eigen::Map<const Eigen::Matrix<Real, Eigen::Dynamic, Eigen::Dynamic, Eigen::ColMajor>>
            mapped(A.data.data(), A.rows, A.cols);
        result = mapped;
    } else {
        // 行优先存储，需要转置
        for (Integer i = 0; i < A.rows; ++i) {
            for (Integer j = 0; j < A.cols; ++j) {
                result(i, j) = A(i, j);
            }
        }
    }
    return result;
}

Eigen::VectorXd toEigen(const Vector& v) {
    Eigen::VectorXd result(v.size());
    for (Integer i = 0; i < v.size(); ++i) {
        result(i) = v[i];
    }
    return result;
}

Matrix fromEigen(const Eigen::MatrixXd& A) {
    Matrix result(static_cast<Integer>(A.rows()), static_cast<Integer>(A.cols()));
    if (Matrix::is_col_major) {
        // 列优先存储，直接复制
        std::copy(A.data(), A.data() + A.size(), result.data.begin());
    } else {
        // 行优先存储，需要转置
        for (Integer i = 0; i < A.rows(); ++i) {
            for (Integer j = 0; j < A.cols(); ++j) {
                result(i, j) = A(i, j);
            }
        }
    }
    return result;
}

Vector fromEigen(const Eigen::VectorXd& v) {
    Vector result(static_cast<Integer>(v.size()));
    for (Integer i = 0; i < static_cast<Integer>(v.size()); ++i) {
        result[i] = v(i);
    }
    return result;
}
#endif

// MATLAB兼容矩阵函数 - 已在上面定义

Matrix diag(const Vector& v) {
    Integer n = v.size();
    Matrix result(n, n);
    for (Integer i = 0; i < n; ++i) {
        result(i, i) = v[i];
    }
    return result;
}

Vector diag(const Matrix& A) {
    Integer n = std::min(A.rows, A.cols);
    Vector result(n);
    for (Integer i = 0; i < n; ++i) {
        result[i] = A(i, i);
    }
    return result;
}

Matrix triu(const Matrix& A, Integer k) {
    Matrix result(A.rows, A.cols);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < A.cols; ++j) {
            if (j >= i + k) {
                result(i, j) = A(i, j);
            }
        }
    }
    return result;
}

Matrix tril(const Matrix& A, Integer k) {
    Matrix result(A.rows, A.cols);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < A.cols; ++j) {
            if (j <= i + k) {
                result(i, j) = A(i, j);
            }
        }
    }
    return result;
}

Real norm(const Matrix& A, const std::string& type) {
    if (type == "2" || type == "fro") {
        // Frobenius norm
        Real sum = 0.0;
        for (Integer i = 0; i < A.rows; ++i) {
            for (Integer j = 0; j < A.cols; ++j) {
                sum += A(i, j) * A(i, j);
            }
        }
        return std::sqrt(sum);
    } else if (type == "1") {
        // 1-norm (maximum column sum)
        Real maxSum = 0.0;
        for (Integer j = 0; j < A.cols; ++j) {
            Real colSum = 0.0;
            for (Integer i = 0; i < A.rows; ++i) {
                colSum += std::abs(A(i, j));
            }
            maxSum = std::max(maxSum, colSum);
        }
        return maxSum;
    } else if (type == "inf") {
        // Infinity norm (maximum row sum)
        Real maxSum = 0.0;
        for (Integer i = 0; i < A.rows; ++i) {
            Real rowSum = 0.0;
            for (Integer j = 0; j < A.cols; ++j) {
                rowSum += std::abs(A(i, j));
            }
            maxSum = std::max(maxSum, rowSum);
        }
        return maxSum;
    }
    return 0.0;
}

Real cond(const Matrix& A) {
    try {
#ifdef USE_EIGEN
        Eigen::MatrixXd E = toEigen(A);
        Eigen::JacobiSVD<Eigen::MatrixXd> svd(E);
        Eigen::VectorXd singularValues = svd.singularValues();
        if (singularValues.size() == 0 || singularValues(singularValues.size()-1) == 0) {
            return std::numeric_limits<Real>::infinity();
        }
        return singularValues(0) / singularValues(singularValues.size()-1);
#else
        // 简单估计：使用范数比值
        Matrix Ainv = inv(A);
        return norm(A, "2") * norm(Ainv, "2");
#endif
    }
    catch (const std::exception&) {
        return std::numeric_limits<Real>::infinity();
    }
}

Real det(const Matrix& A) {
    try {
        check_square(A);
#ifdef USE_EIGEN
        Eigen::MatrixXd E = toEigen(A);
        return E.determinant();
#else
        // 简单的LU分解计算行列式
        Integer n = A.rows;
        Matrix L = A;
        Real det_val = 1.0;

        for (Integer i = 0; i < n; ++i) {
            // 找主元
            Integer pivot = i;
            for (Integer j = i + 1; j < n; ++j) {
                if (std::abs(L(j, i)) > std::abs(L(pivot, i))) {
                    pivot = j;
                }
            }

            if (std::abs(L(pivot, i)) < EPS) {
                return 0.0; // 奇异矩阵
            }

            if (pivot != i) {
                // 交换行
                for (Integer j = 0; j < n; ++j) {
                    std::swap(L(i, j), L(pivot, j));
                }
                det_val = -det_val; // 行交换改变符号
            }

            det_val *= L(i, i);

            // 消元
            for (Integer j = i + 1; j < n; ++j) {
                Real factor = L(j, i) / L(i, i);
                for (Integer k = i; k < n; ++k) {
                    L(j, k) -= factor * L(i, k);
                }
            }
        }

        return det_val;
#endif
    }
    catch (const std::exception& e) {
        error_handler("det", std::string("Determinant calculation failed: ") + e.what());
        return 0.0;
    }
}

Integer rank(const Matrix& A, Real tol) {
    try {
#ifdef USE_EIGEN
        Eigen::MatrixXd E = toEigen(A);
        Eigen::JacobiSVD<Eigen::MatrixXd> svd(E);
        Eigen::VectorXd singularValues = svd.singularValues();

        Real tolerance = (tol > 0) ? tol : EPS * std::max(A.rows, A.cols) * singularValues(0);

        Integer rank_val = 0;
        for (Integer i = 0; i < singularValues.size(); ++i) {
            if (singularValues(i) > tolerance) {
                rank_val++;
            }
        }
        return rank_val;
#else
        // 简单估计
        return std::min(A.rows, A.cols);
#endif
    }
    catch (const std::exception& e) {
        error_handler("rank", std::string("Rank calculation failed: ") + e.what());
        return 0;
    }
}

// Kronecker积
Matrix kron(const Matrix& A, const Matrix& B) {
    Integer m1 = A.rows, n1 = A.cols;
    Integer m2 = B.rows, n2 = B.cols;
    Matrix result(m1 * m2, n1 * n2);

    for (Integer i = 0; i < m1; ++i) {
        for (Integer j = 0; j < n1; ++j) {
            for (Integer k = 0; k < m2; ++k) {
                for (Integer l = 0; l < n2; ++l) {
                    result(i * m2 + k, j * n2 + l) = A(i, j) * B(k, l);
                }
            }
        }
    }
    return result;
}

// 特征值分解
std::pair<Vector, Matrix> eigSys(const Matrix& A) {
    check_square(A);
    Integer n = A.rows;

#ifdef USE_EIGEN
    Eigen::MatrixXd E = toEigen(A);
    Eigen::EigenSolver<Eigen::MatrixXd> solver(E);

    Eigen::VectorXcd eigenvalues = solver.eigenvalues();
    Eigen::MatrixXcd eigenvectors = solver.eigenvectors();

    Vector vals(n);
    Matrix vecs(n, n);

    for (Integer i = 0; i < n; ++i) {
        vals[i] = eigenvalues(i).real(); // 只取实部
        for (Integer j = 0; j < n; ++j) {
            vecs(j, i) = eigenvectors(j, i).real(); // 只取实部
        }
    }

    return std::make_pair(vals, vecs);
#else
    // 简单实现：返回对角线元素作为特征值，单位矩阵作为特征向量
    Vector vals = diag(A);
    Matrix vecs = identity(n);
    return std::make_pair(vals, vecs);
#endif
}

// SVD分解
std::tuple<Matrix, Vector, Matrix> svd(const Matrix& A) {
    Integer m = A.rows, n = A.cols;

#ifdef USE_EIGEN
    Eigen::MatrixXd E = toEigen(A);
    Eigen::JacobiSVD<Eigen::MatrixXd> svd(E, Eigen::ComputeFullU | Eigen::ComputeFullV);

    Matrix U(m, m), V(n, n);
    Vector S(std::min(m, n));

    Eigen::MatrixXd U_eigen = svd.matrixU();
    Eigen::MatrixXd V_eigen = svd.matrixV();
    Eigen::VectorXd S_eigen = svd.singularValues();

    for (Integer i = 0; i < m; ++i) {
        for (Integer j = 0; j < m; ++j) {
            U(i, j) = U_eigen(i, j);
        }
    }

    for (Integer i = 0; i < n; ++i) {
        for (Integer j = 0; j < n; ++j) {
            V(i, j) = V_eigen(i, j);
        }
    }

    for (Integer i = 0; i < S.size(); ++i) {
        S[i] = S_eigen(i);
    }

    return std::make_tuple(U, S, V);
#else
    // 简单实现：返回单位矩阵
    Matrix U(m, m, 0.0);
    for (Integer i = 0; i < m; ++i) U(i, i) = 1.0;
    Matrix V(n, n, 0.0);
    for (Integer i = 0; i < n; ++i) V(i, i) = 1.0;
    Vector S(std::min(m, n), 1.0);
    return std::make_tuple(U, S, V);
#endif
}

// LU分解
std::tuple<Matrix, Matrix, Matrix> lu(const Matrix& A) {
    check_square(A);
    Integer n = A.rows;

#ifdef USE_EIGEN
    Eigen::MatrixXd E = toEigen(A);
    Eigen::FullPivLU<Eigen::MatrixXd> lu_decomp(E);

    Matrix L(n, n), U(n, n), P(n, n);

    Eigen::MatrixXd L_eigen = lu_decomp.matrixLU().triangularView<Eigen::StrictlyLower>();
    Eigen::MatrixXd U_eigen = lu_decomp.matrixLU().triangularView<Eigen::Upper>();
    Eigen::MatrixXd P_eigen = lu_decomp.permutationP();

    // 设置L的对角线为1
    for (Integer i = 0; i < n; ++i) {
        L_eigen(i, i) = 1.0;
    }

    for (Integer i = 0; i < n; ++i) {
        for (Integer j = 0; j < n; ++j) {
            L(i, j) = L_eigen(i, j);
            U(i, j) = U_eigen(i, j);
            P(i, j) = P_eigen(i, j);
        }
    }

    return std::make_tuple(P, L, U);
#else
    // 简单实现：返回原矩阵作为U，单位矩阵作为L和P
    Matrix L(n, n, 0.0);
    for (Integer i = 0; i < n; ++i) L(i, i) = 1.0;
    Matrix U = A;
    Matrix P(n, n, 0.0);
    for (Integer i = 0; i < n; ++i) P(i, i) = 1.0;
    return std::make_tuple(P, L, U);
#endif
}

// 矩阵指数（简化实现）
Matrix expm(const Matrix& A) {
    check_square(A);
    Integer n = A.rows;

    // 使用泰勒级数近似：exp(A) ≈ I + A + A²/2! + A³/3! + ...
    Matrix result(n, n, 0.0);
    for (Integer i = 0; i < n; ++i) {
        result(i, i) = 1.0; // 单位矩阵
    }

    Matrix term(n, n, 0.0);
    for (Integer i = 0; i < n; ++i) {
        term(i, i) = 1.0; // 单位矩阵
    }

    Real factorial = 1.0;

    for (Integer k = 1; k <= 10; ++k) { // 计算前10项
        factorial *= k;
        term = EigenWrapper::matmul(term, A);
        Matrix scaled_term = term;
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                scaled_term(i, j) /= factorial;
            }
        }
        result = result + scaled_term;
    }

    return result;
}

// 矩阵对数（简化实现）
Matrix logm(const Matrix& A) {
    check_square(A);
    Integer n = A.rows;

    // 简单实现：使用级数展开 log(I + X) = X - X²/2 + X³/3 - ...
    Matrix I(n, n, 0.0);
    for (Integer i = 0; i < n; ++i) {
        I(i, i) = 1.0; // 单位矩阵
    }

    Matrix X(n, n);
    for (Integer i = 0; i < n; ++i) {
        for (Integer j = 0; j < n; ++j) {
            X(i, j) = A(i, j) - I(i, j);
        }
    }
    Matrix result = X;
    Matrix term = X;

    for (Integer k = 2; k <= 10; ++k) {
        term = EigenWrapper::matmul(term, X);
        Matrix scaled_term = term;
        Real sign = (k % 2 == 0) ? -1.0 : 1.0;
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                scaled_term(i, j) *= sign / k;
            }
        }
        result = result + scaled_term;
    }

    return result;
}

// 零空间（简化实现）
Matrix null(const Matrix& A, Real tol) {
    // 使用SVD计算零空间
    auto [U, S, V] = svd(A);

    Real tolerance = (tol > 0) ? tol : EPS * std::max(A.rows, A.cols) * S[0];

    std::vector<Integer> null_indices;
    for (Integer i = 0; i < S.size(); ++i) {
        if (S[i] <= tolerance) {
            null_indices.push_back(i);
        }
    }

    if (null_indices.empty()) {
        return Matrix(A.cols, 0); // 空矩阵
    }

    Matrix null_space(A.cols, static_cast<Integer>(null_indices.size()));
    for (Integer j = 0; j < static_cast<Integer>(null_indices.size()); ++j) {
        Integer idx = null_indices[j];
        for (Integer i = 0; i < A.cols; ++i) {
            null_space(i, j) = V(i, idx);
        }
    }

    return null_space;
}

// 正交基（简化实现）
Matrix orth(const Matrix& A, Real tol) {
    // 使用SVD计算列空间的正交基
    auto [U, S, V] = svd(A);

    Real tolerance = (tol > 0) ? tol : EPS * std::max(A.rows, A.cols) * S[0];

    Integer rank_val = 0;
    for (Integer i = 0; i < S.size(); ++i) {
        if (S[i] > tolerance) {
            rank_val++;
        }
    }

    Matrix orth_basis(A.rows, rank_val);
    for (Integer j = 0; j < rank_val; ++j) {
        for (Integer i = 0; i < A.rows; ++i) {
            orth_basis(i, j) = U(i, j);
        }
    }

    return orth_basis;
}

// 优化的矩阵运算
Matrix mtimes(const Matrix& A, const Matrix& B) {
    return EigenWrapper::matmul(A, B);
}

Vector mtimes(const Matrix& A, const Vector& x) {
    return EigenWrapper::matvec(A, x);
}

Matrix mplus(const Matrix& A, const Matrix& B) {
    return A + B;
}

Matrix mminus(const Matrix& A, const Matrix& B) {
    Matrix result(A.rows, A.cols);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < A.cols; ++j) {
            result(i, j) = A(i, j) - B(i, j);
        }
    }
    return result;
}

} // namespace EigenWrapper
