function [OM] = compute_KS1d(OM)
%COMPUTE_KS Summary of this function goes here

ndomains = length(OM);
for iom = 1:ndomains

    kk12     = OM(iom).kk12;
    invLT22  = OM(iom).invLT22;
    invL11   = OM(iom).invL11;

    S2       = OM(iom).state.S2;
    Sbval_xm = OM(iom).state.Sbval_xm;
    Sbval_xp = OM(iom).state.Sbval_xp;


    Sb2    = invLT22*S2;
    dS2dx1 = -kk12*Sb2;

    dS2dx1(1)   = dS2dx1(1)   - Sbval_xm;
    dS2dx1(end) = dS2dx1(end) + Sbval_xp;

    OM(iom).state.dU2dtt1 = invL11*dS2dx1;
end


end

