function mm = mass_matrix(p,N1,N2,tx1,tx2,xint,wxint,option)

mm = zeros(N1,N2);
[NB_intervals,ord_gi] = size(xint);
if option==0 
    k = p+1;
elseif option==1
    k = p;
end

for ib1 = 1:2*p %b1
    for jb1 = 1:3*p %b1
        for kd = 1:NB_intervals
            for lpt = 1: ord_gi
                b1tmp1 = bspln(tx1,N1,ib1,k,xint(kd,lpt));
                b1tmp2 = bspln(tx2,N2,jb1,k,xint(kd,lpt));
                mm(ib1,jb1) = mm(ib1,jb1) + b1tmp1*b1tmp2*wxint(kd,lpt);
            end
        end
    end
end

for i=2*p+1:(N2-p*2)
    mm(i,(i-p):(i+p)) = mm(i-1,(i-1-p):(i-1+p));
end

for ib1 = (N1-p*2+1):N1 %b1
    for jb1 = (N2-3*p+1):N2 %b1
        for kd = 1:NB_intervals
            for lpt = 1: ord_gi
                b1tmp1 = bspln(tx1,N1,ib1,k,xint(kd,lpt));
                b1tmp2 = bspln(tx2,N2,jb1,k,xint(kd,lpt));
                mm(ib1,jb1) = mm(ib1,jb1) + b1tmp1*b1tmp2*wxint(kd,lpt);
            end
        end
    end
end

end

