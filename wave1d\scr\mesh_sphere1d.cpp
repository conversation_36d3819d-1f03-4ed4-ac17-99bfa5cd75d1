#include "mesh_sphere1d.hpp"
#include <cmath>
#include <algorithm>
#include <iostream>

namespace wave {
std::vector<Domain1D> mesh_sphere1d(const std::vector<double>& rads, double dx, double freq, int px_order, double ppw)
{
    std::vector<double> allrad;
    allrad.reserve(rads.size()*2);
    for(auto it=rads.rbegin(); it!=rads.rend(); ++it) allrad.push_back(-*it);
    allrad.insert(allrad.end(), rads.begin(), rads.end());

    std::size_t ndomains = allrad.size() - 1;
    std::vector<Domain1D> OM(ndomains);

    const double rho_default = 3000.0;   
    const double Vp_default  = 10000.0;  

    for(std::size_t iom=0;iom<ndomains;++iom){
        double r1 = allrad[iom];
        double r2 = allrad[iom+1];
        double dr = std::abs(r2 - r1);

        const double fmax = 3.0 * freq;
        const double vpmin = Vp_default; 
        double wavemin = vpmin / fmax;

        int Nx1 = std::max(3, static_cast<int>(std::ceil(dr / wavemin * ppw)) );
        int px  = std::min(Nx1 - 1, px_order);

        std::vector<double> xa(Nx1);
        for(int i=0;i<Nx1;++i){
            xa[i] = r1 + dr * static_cast<double>(i) / (Nx1 - 1);
        }
        
        std::vector<double> Vp(Nx1, Vp_default);
        std::vector<double> rho(Nx1, rho_default);

        Domain1D &dom = OM[iom];
        dom.model1d.xa  = xa;
        dom.model1d.Vp  = Vp;
        dom.model1d.rho = rho;
        dom.model1d.Nx1 = Nx1;
        dom.model1d.px  = px;

        dom.rho1 = rho_default;
        dom.Vp2  = Vp_default*Vp_default;
    }

    return OM;
}
}  