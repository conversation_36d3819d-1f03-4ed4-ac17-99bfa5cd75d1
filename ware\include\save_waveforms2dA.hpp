#pragma once
#include "common_types.hpp"
#include "receiver.hpp"
#include "mesh_sphere2dA.hpp"
#include <string>

/**
 * @brief 保存波形数据，与MATLAB版本一致
 * 
 * 该函数提取各个接收器位置的波场值，并存储到接收器结构中
 * 
 * @param OM 域结构数组
 * @param rece 接收器数组
 * @param it 当前时间步
 * @return 更新后的接收器数组
 */
std::vector<Receiver2dA> save_waveforms2dA(const std::vector<Domain2dA>& OM, 
                                         std::vector<Receiver2dA>& rece, 
                                         Integer it);

/**
 * @brief 保存全部波形数据到文件，用于模拟结束后
 * 
 * @param rec 接收器数组
 * @param filename 输出文件名
 */
void save_waveforms2dA_final(const std::vector<Receiver2dA>& rec, const std::string& filename); 