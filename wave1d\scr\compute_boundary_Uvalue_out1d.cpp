/**
 * @file compute_boundary_Uvalue_out1d.cpp
 * @brief 优化的域间位移边界条件计算 - 并行版本
 *
 * 功能: 处理相邻域之间的位移边界条件
 * 算法: Ubval = (1-α) * Ubval_inn + α * Ubval_out
 * 优化: OpenMP并行化 + 缓存优化 + 数值稳定性
 */

#include "compute_boundary_Uvalue_out1d.hpp"
#include <cstddef>
#include <cmath>

#ifdef _OPENMP
#include <omp.h>
#endif

namespace wave {

void compute_boundary_Uvalue_out1d(std::vector<Domain1D>& domains)
{
    const int ndomains = static_cast<int>(domains.size());
    if(ndomains == 0) return;

    // OpenMP并行化: 域级并行处理
    // 注意: 只对当前域写入，对邻居域只读，避免数据竞争
    #ifdef _OPENMP
    #pragma omp parallel for schedule(static) default(none) shared(domains, ndomains)
    #endif
    for(int iom = 0; iom < ndomains; ++iom)
    {
        auto &dom = domains[static_cast<std::size_t>(iom)];

        // 优化: 预取权重系数，减少内存访问
        const double alpha_xm = dom.alpha_xm;
        const double alpha_xp = dom.alpha_xp;
        const double one_minus_alpha_xm = 1.0 - alpha_xm;
        const double one_minus_alpha_xp = 1.0 - alpha_xp;

        // 初始化外部边界值
        double Ubval_xm_out = 0.0;
        double Ubval_xp_out = 0.0;

        // 优化: 从左邻居获取位移边界值
        const int iNbrm = dom.iNbrm;
        if(iNbrm >= 0 && static_cast<std::size_t>(iNbrm) < static_cast<std::size_t>(ndomains))
        {
            const auto &nbr = domains[static_cast<std::size_t>(iNbrm)];
            if(!nbr.state.Ub1.empty()) {
                Ubval_xm_out = nbr.state.Ub1.back();  // 左邻居的右边界
            }
        }

        // 优化: 从右邻居获取位移边界值
        const int iNbrp = dom.iNbrp;
        if(iNbrp >= 0 && static_cast<std::size_t>(iNbrp) < static_cast<std::size_t>(ndomains))
        {
            const auto &nbr = domains[static_cast<std::size_t>(iNbrp)];
            if(!nbr.state.Ub1.empty()) {
                Ubval_xp_out = nbr.state.Ub1.front();  // 右邻居的左边界
            }
        }

        // 存储外部边界值
        dom.state.Ubval_xm_out = Ubval_xm_out;
        dom.state.Ubval_xp_out = Ubval_xp_out;

        // 优化: 向量化的边界条件计算
        // 位移边界条件: (1-α) * 内部值 + α * 外部值
        const double Ubval_xm = one_minus_alpha_xm * dom.state.Ubval_xm_inn + alpha_xm * Ubval_xm_out;
        const double Ubval_xp = one_minus_alpha_xp * dom.state.Ubval_xp_inn + alpha_xp * Ubval_xp_out;

        // 数值稳定性检查
        dom.state.Ubval_xm = std::isfinite(Ubval_xm) ? Ubval_xm : 0.0;
        dom.state.Ubval_xp = std::isfinite(Ubval_xp) ? Ubval_xp : 0.0;
    }
}

} // namespace wave