#pragma once
#include <vector>
#include <cassert>
#include <cmath>
#include <algorithm>
#include <Eigen/Dense>
#include <cstddef>
#include <iostream>
#include <limits>


namespace wave {
struct Matrix {
    std::size_t rows{0}, cols{0};
    std::vector<double> a;
    Matrix() = default;
    Matrix(std::size_t r,std::size_t c,double val=0.0):rows(r),cols(c),a(r*c,val){}
    double& operator()(std::size_t r,std::size_t c){return a[r*cols+c];}
    double  operator()(std::size_t r,std::size_t c)const{return a[r*cols+c];}
    std::size_t size() const { return a.size(); }
};

// Basic operations
inline Matrix identity(std::size_t n){ Matrix I(n,n,0.0); for(std::size_t i=0;i<n;++i) I(i,i)=1.0; return I; }

inline Matrix transpose(const Matrix& M){ Matrix T(M.cols,M.rows,0.0); for(std::size_t i=0;i<M.rows;++i) for(std::size_t j=0;j<M.cols;++j) T(j,i)=M(i,j); return T; }

inline Matrix matmul(const Matrix& A,const Matrix& B){ assert(A.cols==B.rows); Matrix C(A.rows,B.cols,0.0); for(std::size_t i=0;i<A.rows;++i) for(std::size_t k=0;k<A.cols;++k){ double aik=A(i,k); for(std::size_t j=0;j<B.cols;++j) C(i,j)+=aik*B(k,j);} return C; }

inline std::vector<double> matvec(const Matrix& A,const std::vector<double>& x){
    if(A.cols!=x.size()){
        static int warnCnt=0;
        if(warnCnt<20){
            std::cerr << "[ERROR] matvec dimension mismatch: A.cols="<<A.cols
                      <<" x.size="<<x.size()<<" – returning zero vector."<<std::endl;
            ++warnCnt;
        }
        return std::vector<double>(A.rows, 0.0);
    }
    std::vector<double> y(A.rows,0.0);
    for(std::size_t i=0;i<A.rows;++i){ double sum=0.0; for(std::size_t j=0;j<A.cols;++j) sum+=A(i,j)*x[j]; y[i]=sum; }
    return y;
}

// Cholesky upper R such that A = R^T R
inline Matrix cholesky_upper(const Matrix& A){
    const std::size_t n = A.rows;
    Matrix R(n,n,0.0);

    // Absolute tolerance based on max diagonal
    double diag_max = 0.0;
    for(std::size_t i=0;i<n;++i) diag_max = std::max(diag_max, std::fabs(A(i,i)));
    const double EPS = std::max(1e-12, diag_max*1e-12);

    for(std::size_t i=0;i<n;++i){
        for(std::size_t j=i;j<n;++j){
            double s = A(i,j);
            for(std::size_t k=0;k<i;++k){
                double rik = R(k,i);
                double rkj = R(k,j);
                if(!std::isfinite(rik) || !std::isfinite(rkj)) continue; // skip bad contrib
                s -= rik*rkj;
            }

            if(i==j){
                if(!std::isfinite(s) || s<=EPS) s = EPS; // regularise
                R(i,i) = std::sqrt(s);
                if(!std::isfinite(R(i,i))) R(i,i) = std::sqrt(EPS);
            }else{
                double rii = R(i,i);
                if(!std::isfinite(rii) || std::abs(rii)<EPS) rii = std::sqrt(EPS);
                if(!std::isfinite(s)) s = 0.0;
                R(i,j) = s/rii;
                if(!std::isfinite(R(i,j))) R(i,j)=0.0;
            }
        }
    }

    // Final sweep: replace any residual non-finite with 0
    for(double &v: R.a) if(!std::isfinite(v)) v = 0.0;

    return R;
}

// Robust inversion of an upper-triangular matrix (R). Skips non-finite terms
// and clamps tiny/invalid diagonals, so result never contains NaN/Inf.
inline Matrix invert_upper(const Matrix& R){
    const std::size_t n = R.rows;
    Matrix Inv(n,n,0.0);
    const double EPS = 1.0e-12;

    for(int i = int(n) - 1; i >= 0; --i){
        double rii = R(i,i);
        if(!std::isfinite(rii) || std::fabs(rii) < EPS) rii = EPS; // regularise
        Inv(i,i) = 1.0 / rii;

        for(int j = i - 1; j >= 0; --j){
            double sum = 0.0;
            for(std::size_t k = j + 1; k <= std::size_t(i); ++k){
                double rjk = R(j,k);
                double vik = Inv(k,i);
                if(!std::isfinite(rjk) || !std::isfinite(vik)) continue; // skip bad contrib
                sum += rjk * vik;
            }
            double rjj = R(j,j);
            if(!std::isfinite(rjj) || std::fabs(rjj) < EPS) rjj = EPS;
            double val = -sum / rjj;
            if(!std::isfinite(val)) val = 0.0;
            Inv(j,i) = val;
        }
    }

    // Replace any residual non-finite entries with zero
    for(double &v : Inv.a) if(!std::isfinite(v)) v = 0.0;
#ifdef _DEBUG
    bool hasBad=false; double sabs=0.0; for(double v:Inv.a){ if(!std::isfinite(v)) hasBad=true; sabs+=std::fabs(v);} if(hasBad) std::cout<<"[DEBUG] invert_upper still has NaNs sumAbs="<<sabs<<std::endl;
#endif
    return Inv;
}

inline Matrix inverse(const Matrix& A){ Matrix R=cholesky_upper(A); Matrix Rin = invert_upper(R); return matmul(transpose(Rin), Rin); }

// ===================== Gauss–Jordan full inverse ==========================
inline Matrix inverse_full(Matrix A){
    // Simple Gauss-Jordan with partial pivoting.  Suitable for small N (<=100).
    std::size_t n = A.rows;
    Matrix I(n,n,0.0);
    for(std::size_t i=0;i<n;++i) I(i,i)=1.0;

    for(std::size_t col=0; col<n; ++col){
        // pivot row = max |A(r,col)|, r>=col
        std::size_t piv=col;
        double piv_abs = std::fabs(A(col,col));
        for(std::size_t r=col+1;r<n;++r){ double v=std::fabs(A(r,col)); if(v>piv_abs){ piv=r; piv_abs=v; }}
        if(piv_abs < 1e-12) piv_abs = 1e-12; // regularise nearly-singular

        if(piv!=col){
            for(std::size_t c=0;c<n;++c){ std::swap(A(col,c),A(piv,c)); std::swap(I(col,c),I(piv,c)); }
        }
        // scale pivot row
        double diag = A(col,col);
        for(std::size_t c=0;c<n;++c){ A(col,c)/=diag; I(col,c)/=diag; }
        // eliminate other rows
        for(std::size_t r=0;r<n;++r){ if(r==col) continue; double factor = A(r,col); if(factor==0.0) continue;
            for(std::size_t c=0;c<n;++c){ A(r,c) -= factor * A(col,c); I(r,c) -= factor * I(col,c); }
        }
    }
    // Replace any residual non-finite by 0
    for(double &v: I.a) if(!std::isfinite(v)) v = 0.0;
    return I;
}

// ================= Helpers for robust SPD square-root =====================
// Jacobi eigenvalue decomposition (symmetric) – adequate for the small spline
// orders used in 1-D DFDM (typically N≤20).  It returns eigen-vectors in V
// (columns) and eigen-values in eigs.
inline void jacobi_eigen(const Matrix &A, Matrix &V, std::vector<double> &eigs,
                         int max_iter = 100, double tol = 1.0e-12){
    std::size_t n = A.rows; V = identity(n); Matrix M = A; eigs.assign(n,0.0);
    for(int it=0; it<max_iter; ++it){
        // locate largest off-diagonal element
        std::size_t p = 0, q = 1; double max_off = 0.0;
        for(std::size_t i=0;i<n;++i) for(std::size_t j=i+1;j<n;++j){
            double v = std::fabs(M(i,j)); if(v > max_off){ max_off = v; p = i; q = j; }
        }
        if(max_off < tol) break; // converged
        double app = M(p,p), aqq = M(q,q), apq = M(p,q);
        double phi = 0.5 * std::atan2(2.0*apq, aqq - app);
        double c = std::cos(phi), s = std::sin(phi);
        // Rotate rows/cols p,q in M
        for(std::size_t k=0;k<n;++k){ double mkp=M(k,p), mkq=M(k,q); M(k,p)=c*mkp - s*mkq; M(k,q)=s*mkp + c*mkq; }
        for(std::size_t k=0;k<n;++k){ double mpk=M(p,k), mqk=M(q,k); M(p,k)=c*mpk - s*mqk; M(q,k)=s*mpk + c*mqk; }
        // enforce symmetry
        for(std::size_t k=0;k<n;++k){ M(k,p)=M(p,k); M(k,q)=M(q,k);}        
        // update eigen-vectors
        for(std::size_t k=0;k<n;++k){ double vkp=V(k,p), vkq=V(k,q); V(k,p)=c*vkp - s*vkq; V(k,q)=s*vkp + c*vkq; }
    }
    for(std::size_t i=0;i<n;++i) eigs[i] = M(i,i);
}

// --- Robust sqrtm with eigen-based fallback (Schur-style) ------------------
// Keeps the function signature unchanged so existing calls continue to work.
inline Matrix sqrtm(const Matrix& A){
    Matrix R = cholesky_upper(A);
    bool chol_ok = true;
    const double ABS_EPS = 1.0e-12;
    double diag_max = 0.0, diag_min = std::numeric_limits<double>::infinity();
    for(std::size_t i=0;i<R.rows && chol_ok;++i){
        double v = R(i,i);
        if(!std::isfinite(v) || v <= ABS_EPS) chol_ok = false;
        diag_max = std::max(diag_max, v);
        diag_min = std::min(diag_min, v);
    }
    // Additional relative condition: if the spread of the diagonal is too large
    // (indicating that many entries were clamped to EPS), treat as failure.
    if(chol_ok && diag_min < diag_max * 1.0e-6) chol_ok = false;
    if(chol_ok) return R;

    // ---- Eigen fallback ----
    std::size_t n = A.rows; Matrix V; std::vector<double> eigs; jacobi_eigen(A, V, eigs);

    // compute maximum eigenvalue for relative floor
    double lam_max = 0.0;
    for(double lam : eigs){ if(std::isfinite(lam)) lam_max = std::max(lam_max, lam); }
    if(lam_max < ABS_EPS) lam_max = 1.0; // safeguard
    double lam_floor = lam_max * 1.0e-6; // keep cond ~ 1e6

    Matrix sqrtD(n,n,0.0);
    for(std::size_t i=0;i<n;++i){
        double lam = eigs[i];
        if(!std::isfinite(lam) || lam < 0.0) lam = 0.0; // remove invalid
        if(lam < lam_floor) lam = lam_floor;           // raise
        sqrtD(i,i) = std::sqrt(lam);
    }
    Matrix temp = matmul(V, sqrtD);
    Matrix S = matmul(temp, transpose(V));
    // force symmetry
    for(std::size_t i=0;i<n;++i) for(std::size_t j=i+1;j<n;++j){ double avg=0.5*(S(i,j)+S(j,i)); S(i,j)=S(j,i)=avg; }
    Matrix R2 = cholesky_upper(S);
    // as a last resort, if R2 still fails, fall back to scaled identity
    for(std::size_t i=0;i<n;++i){ double v = R2(i,i); if(!std::isfinite(v) || v <= ABS_EPS){ Matrix I = identity(n); for(std::size_t k=0;k<n;++k) I(k,k)=std::sqrt(std::max(A(k,k),ABS_EPS)); return I; } }
    return R2;
}

inline double diag_normalise(Matrix& M){
    if(M.rows==0) return 1.0;
    double dmax=0.0;
    for(std::size_t i=0;i<M.rows;++i) dmax=std::max(dmax,std::fabs(M(i,i)));
    if(dmax==0.0) return 1.0;
    for(double &v:M.a) v/=dmax;
    return dmax;
}
    // --- New robust inverse square root for symmetric matrices ---
    inline Matrix inv_sqrtm_symmetric(const Matrix& A) {
        std::size_t n = A.rows;
        Matrix V;
        std::vector<double> eigs;
        // Use Jacobi eigenvalue decomposition
        jacobi_eigen(A, V, eigs); // V has eigenvectors as columns, eigs has eigenvalues

        // Find max eigenvalue for regularization floor
        double lam_max = 0.0;
        for(double lam : eigs) {
            if (std::isfinite(lam)) lam_max = std::max(lam_max, lam);
        }
        if (lam_max <= 0) lam_max = 1.0; // Safeguard for zero matrix

        // Tikhonov-style regularization for eigenvalues
        const double lam_floor = lam_max * 1.0e-12;

        Matrix D_inv_sqrt(n, n, 0.0);
        for(std::size_t i = 0; i < n; ++i) {
            double lam = eigs[i];
            // Project non-positive eigenvalues to a small positive number
            if (!std::isfinite(lam) || lam < lam_floor) {
                lam = lam_floor;
            }
            D_inv_sqrt(i,i) = 1.0 / std::sqrt(lam);
        }

        // Reconstruct the final matrix: V * D^(-1/2) * V'
        Matrix temp = matmul(V, D_inv_sqrt);
        Matrix result = matmul(temp, transpose(V));
        return result;
    }
    inline Matrix calculate_invLT11_with_eigen(const Matrix& mm11) {
    if (mm11.rows == 0 || mm11.cols == 0) {
        throw std::runtime_error("Input matrix mm11 is empty.");
    }
    if (mm11.rows != mm11.cols) {
        throw std::runtime_error("Input matrix mm11 must be square.");
    }

    // Convert wave::Matrix to Eigen::MatrixXd
    Eigen::MatrixXd eigen_m(mm11.rows, mm11.cols);
    for (size_t i = 0; i < mm11.rows; ++i) {
        for (size_t j = 0; j < mm11.cols; ++j) {
            eigen_m(i, j) = mm11(i, j);
        }
    }

    // Use Eigen's stable solver for symmetric matrices
    Eigen::SelfAdjointEigenSolver<Eigen::MatrixXd> es(eigen_m);
    if (es.info() != Eigen::Success) {
        throw std::runtime_error("Eigen decomposition failed.");
    }

    // Calculate M^{-1/2} using a pseudo-inverse approach for stability
    Eigen::VectorXd eigenvalues = es.eigenvalues();
    // Determine dynamic floor proportional to the largest eigenvalue to avoid excessive amplification.
    double lam_max = eigenvalues.maxCoeff();
    if(!std::isfinite(lam_max) || lam_max <= 0.0) lam_max = 1.0;
    const double lam_floor = lam_max * 1.0e-6;   // condition number cap ~1e6
    Eigen::VectorXd D_inv_sqrt_diag(eigenvalues.size());
    for (int i = 0; i < eigenvalues.size(); ++i) {
        double lam = eigenvalues(i);
        if (!std::isfinite(lam) || lam < lam_floor) lam = lam_floor;
        D_inv_sqrt_diag(i) = 1.0 / std::sqrt(lam);
    }
    
    Eigen::MatrixXd D_inv_sqrt = D_inv_sqrt_diag.asDiagonal();
    Eigen::MatrixXd V = es.eigenvectors();
    Eigen::MatrixXd inv_sqrt_m = V * D_inv_sqrt * V.transpose();


    Matrix invLT11(mm11.rows, mm11.cols);
    for (size_t i = 0; i < mm11.rows; ++i) {
        for (size_t j = 0; j < mm11.cols; ++j) {
            invLT11(i, j) = inv_sqrt_m(i, j);
        }
    }

    return invLT11;
}
} 