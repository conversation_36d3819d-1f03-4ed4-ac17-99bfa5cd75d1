{"artifacts": [{"path": "test_matrix_dimensions.exe"}, {"path": "test_matrix_dimensions.pdb"}], "backtrace": 2, "backtraceGraph": {"commands": ["_add_executable", "add_executable", "link_directories", "target_link_libraries", "add_compile_options", "add_definitions", "include_directories"], "files": ["C:/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 77, "parent": 0}, {"command": 0, "file": 0, "line": 600, "parent": 1}, {"command": 2, "file": 1, "line": 27, "parent": 0}, {"command": 3, "file": 1, "line": 78, "parent": 0}, {"command": 3, "file": 1, "line": 82, "parent": 0}, {"command": 4, "file": 1, "line": 49, "parent": 0}, {"command": 4, "file": 1, "line": 51, "parent": 0}, {"command": 5, "file": 1, "line": 15, "parent": 0}, {"command": 5, "file": 1, "line": 28, "parent": 0}, {"command": 6, "file": 1, "line": 8, "parent": 0}, {"command": 6, "file": 1, "line": 14, "parent": 0}, {"command": 6, "file": 1, "line": 26, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 6, "fragment": "/utf-8"}, {"backtrace": 7, "fragment": "/wd4819"}], "defines": [{"backtrace": 8, "define": "USE_EIGEN"}, {"backtrace": 9, "define": "USE_OPENBLAS"}], "includes": [{"backtrace": 10, "path": "D:/新建文件夹/ware/include"}, {"backtrace": 11, "path": "D:/Eigen/eigen-3.4/eigen-3.4.0/eigen-3.4.0"}, {"backtrace": 12, "path": "D:/OpenBLAS-0.3.30-x64/include"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 4, "id": "ware::@6890427a1f51a3e7e1df"}], "id": "test_matrix_dimensions::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 3, "fragment": "-LIBPATH:D:\\OpenBLAS-0.3.30-x64\\lib", "role": "libraryPath"}, {"backtrace": 4, "fragment": "ware.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "D:\\OpenBLAS-0.3.30-x64\\lib\\libopenblas.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "test_matrix_dimensions", "nameOnDisk": "test_matrix_dimensions.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "test_matrix_dimensions.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}