#pragma once
#include "mesh_sphere2dA.hpp"
#include <vector>

/**
 * @brief Add source excitation to wavefield, consistent with MATLAB version
 * @param OM Array of domain structures
 * @param source Source structures
 * @param it Current time step index
 * @return Updated array of domain structures
 */
std::vector<Domain2dA> add_source2dA(std::vector<Domain2dA>& OM, 
                                    const std::vector<SourceStruct>& source, 
                                    Integer it); 