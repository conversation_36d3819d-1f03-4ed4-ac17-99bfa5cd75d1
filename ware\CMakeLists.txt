cmake_minimum_required(VERSION 3.16)
project(ware)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 包含目录
include_directories(include)

# 设置Eigen路径
set(EIGEN_PATH "D:/Eigen/eigen-3.4/eigen-3.4.0/eigen-3.4.0")
if(EXISTS "${EIGEN_PATH}/Eigen/Dense")
    message(STATUS "Found Eigen at: ${EIGEN_PATH}")
    include_directories("${EIGEN_PATH}")
    add_definitions(-DUSE_EIGEN)
    set(EIGEN_FOUND TRUE)
else()
    message(WARNING "Eigen not found at ${EIGEN_PATH}, compiling without Eigen support")
    set(EIGEN_FOUND FALSE)
endif()

# 设置OpenBLAS路径（可选）
set(OPENBLAS_PATH "D:/OpenBLAS-0.3.30-x64")
if(EXISTS "${OPENBLAS_PATH}/include")
    message(STATUS "Found OpenBLAS at: ${OPENBLAS_PATH}")
    include_directories("${OPENBLAS_PATH}/include")
    link_directories("${OPENBLAS_PATH}/lib")
    add_definitions(-DUSE_OPENBLAS)
    set(OPENBLAS_FOUND TRUE)
    # 查找OpenBLAS库文件
    find_library(OPENBLAS_LIB
        NAMES openblas libopenblas
        PATHS "${OPENBLAS_PATH}/lib"
        NO_DEFAULT_PATH)
    if(OPENBLAS_LIB)
        message(STATUS "Found OpenBLAS library: ${OPENBLAS_LIB}")
    else()
        message(WARNING "OpenBLAS library not found in ${OPENBLAS_PATH}/lib")
        set(OPENBLAS_FOUND FALSE)
    endif()
else()
    message(STATUS "OpenBLAS not found, using default BLAS")
    set(OPENBLAS_FOUND FALSE)
endif()

# 添加编译器特定的设置
if(MSVC)
    # 设置UTF-8编码
    add_compile_options(/utf-8)
    # 禁用一些警告
    add_compile_options(/wd4819)
endif()

# 收集所有源文件
file(GLOB_RECURSE WARE_SOURCES "src/*.cpp")

# 排除main文件，因为我们要创建库
list(FILTER WARE_SOURCES EXCLUDE REGEX ".*main.*\\.cpp$")

# 调试：打印所有源文件
message(STATUS "WARE_SOURCES: ${WARE_SOURCES}")

# 确保initialize_domain2dA.cpp被包含
if(NOT "src/initialize_domain2dA.cpp" IN_LIST WARE_SOURCES)
    message(WARNING "initialize_domain2dA.cpp not found in sources, adding manually")
    list(APPEND WARE_SOURCES "src/initialize_domain2dA.cpp")
endif()

# 创建静态库
add_library(ware STATIC ${WARE_SOURCES})

# 创建主程序可执行文件
add_executable(main2dA src/main2dA.cpp)
target_link_libraries(main2dA ware)

# 创建测试程序
add_executable(test_matrix_dimensions test_matrix_dimensions.cpp)
target_link_libraries(test_matrix_dimensions ware)

# 链接OpenBLAS库（如果找到）
if(OPENBLAS_FOUND AND OPENBLAS_LIB)
    target_link_libraries(ware ${OPENBLAS_LIB})
    target_link_libraries(main2dA ${OPENBLAS_LIB})
endif()

# 设置编译选项
if(MSVC)
    target_compile_options(ware PRIVATE /W3)
    target_compile_options(main2dA PRIVATE /W3)
else()
    target_compile_options(ware PRIVATE -Wall -Wextra)
    target_compile_options(main2dA PRIVATE -Wall -Wextra)
endif()
