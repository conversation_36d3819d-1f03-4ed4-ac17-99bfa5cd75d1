{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "ware.lib"}], "backtrace": 2, "backtraceGraph": {"commands": ["_add_library", "add_library", "add_compile_options", "target_compile_options", "add_definitions", "include_directories"], "files": ["C:/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 70, "parent": 0}, {"command": 0, "file": 0, "line": 641, "parent": 1}, {"command": 2, "file": 1, "line": 49, "parent": 0}, {"command": 2, "file": 1, "line": 51, "parent": 0}, {"command": 3, "file": 1, "line": 88, "parent": 0}, {"command": 4, "file": 1, "line": 15, "parent": 0}, {"command": 4, "file": 1, "line": 28, "parent": 0}, {"command": 5, "file": 1, "line": 8, "parent": 0}, {"command": 5, "file": 1, "line": 14, "parent": 0}, {"command": 5, "file": 1, "line": 26, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 3, "fragment": "/utf-8"}, {"backtrace": 4, "fragment": "/wd4819"}, {"backtrace": 5, "fragment": "/W3"}], "defines": [{"backtrace": 6, "define": "USE_EIGEN"}, {"backtrace": 7, "define": "USE_OPENBLAS"}], "includes": [{"backtrace": 8, "path": "D:/新建文件夹/ware/include"}, {"backtrace": 9, "path": "D:/Eigen/eigen-3.4/eigen-3.4.0/eigen-3.4.0"}, {"backtrace": 10, "path": "D:/OpenBLAS-0.3.30-x64/include"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41]}], "id": "ware::@6890427a1f51a3e7e1df", "name": "ware", "nameOnDisk": "ware.lib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "src/Get_Knot_Vector.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/Get_Knot_Vector_shape.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/add_source2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/assemble_global_matrices_sparse.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/bspln.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/check_stability2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/complex_matrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_KS2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_KU2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_boundary_Svalue_inn2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_boundary_Svalue_out2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_boundary_Uvalue_inn2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_boundary_Uvalue_out2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_dt2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/config.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/create_source2d.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/dbspln.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/dist2d.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/eigen_wrapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/error_handling.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/find_connections2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/gen_DFDMatrices2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/get_receiver2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/get_source2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/global_assembly.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/hat_pts.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/lgwt.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/mass_matrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/material_model.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/mesh_sphere2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/pagemtimes.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/refine_model2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/rotate_full_stress.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/save_wavefields2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/save_waveforms2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/setup_basis.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/solver2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/spmv_omp.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/stiffness_matrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/tensorProduct2D.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/update_wavefields2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/initialize_domain2dA.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}