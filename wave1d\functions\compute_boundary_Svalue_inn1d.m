function [OM] = compute_boundary_Svalue_inn1d(OM)

ndomains = length(OM);

%%%%%%%%%%%%%%%%%%%%%%%%%
% transfer Orthonormal bases to Canonical B-spline bases
% compute boundary variables before communication
for iom = 1:ndomains
    invLT22 = OM(iom).invLT22;
    S2      = OM(iom).state.S2;
    Sb2     = invLT22*S2;

    OM(iom).state.Sbval_xm_inn = Sb2(1);
    OM(iom).state.Sbval_xp_inn = Sb2(end);
    OM(iom).state.Sb2          = Sb2;
end
%%%%%%%%%%%%%%%%%%%%%%%%%

end