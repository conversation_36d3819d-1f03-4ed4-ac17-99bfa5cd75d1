#include "../include/dist2d.hpp"
#include <cmath>
#include <algorithm>

std::pair<Integer,Integer> dist2d(const Matrix &xa,const Matrix &za,
                                  const Matrix &rho,const Matrix &mu,
                                  double fmax,double ppw)
{
    // safety checks
    if(xa.rows==0 || xa.cols==0) return {3,3};
    Integer Nx = xa.rows; // physical grid matrix sizes
    Integer Nz = xa.cols;

    // vmin = min(sqrt(mu./rho))
    double vmin = 1e30;

    // Ensure rho and mu have compatible dimensions
    if(rho.rows == 0 || rho.cols == 0 || mu.rows == 0 || mu.cols == 0) {
        // Use default values if matrices are empty
        vmin = 2750.0; // Default P-wave velocity
    } else {
        // Both matrices should have the same dimensions in MATLAB version
        Integer min_rows = std::min(rho.rows, mu.rows);
        Integer min_cols = std::min(rho.cols, mu.cols);

        for(Integer i = 0; i < min_rows; ++i) {
            for(Integer j = 0; j < min_cols; ++j) {
                double rho_val = rho(i, j);
                double mu_val = mu(i, j);
                if(rho_val > 0 && mu_val > 0) {
                    double vp = std::sqrt(mu_val / rho_val);
                    vmin = std::min(vmin, vp);
                }
            }
        }
    }
    if(!std::isfinite(vmin) || vmin<=0) vmin = 3000.0; // fallback
    double wavemin = vmin / fmax;

    // distx, distz along directions
    double max_ddx = 0.0;
    for(Integer col=0; col<Nz; ++col){
        double sum=0.0;
        for(Integer row=0; row<Nx-1; ++row){
            double dx = std::abs(xa(row+1,col)-xa(row,col));
            sum += dx;
        }
        max_ddx = std::max(max_ddx, sum);
    }
    double max_ddz = 0.0;
    for(Integer row=0; row<Nx; ++row){
        double sum=0.0;
        for(Integer col=0; col<Nz-1; ++col){
            double dz = std::abs(za(row,col+1)-za(row,col));
            sum += dz;
        }
        max_ddz = std::max(max_ddz, sum);
    }

    Integer Nx1 = std::max<Integer>(3, static_cast<Integer>(std::ceil(max_ddx/wavemin*ppw)));
    Integer Nz1 = std::max<Integer>(3, static_cast<Integer>(std::ceil(max_ddz/wavemin*ppw)));
    return {Nx1,Nz1};
} 