#include "../include/complex_matrix.hpp"
#include "../include/error_handling.hpp"
#include <stdexcept>
#include <cmath>

namespace EigenWrapper {

// ComplexMatrix构造函数
ComplexMatrix::ComplexMatrix(Integer r, Integer c) : rows(r), cols(c), data(r * c) {}

ComplexMatrix::ComplexMatrix(Integer r, Integer c, const Complex& val) : rows(r), cols(c), data(r * c, val) {}

ComplexMatrix::ComplexMatrix(const Matrix& real, const Matrix& imag) {
    // 检查维度匹配
    if (real.rows != imag.rows || real.cols != imag.cols) {
        MatlabError::error("ComplexMatrix", "Real and imaginary parts must have the same dimensions",
                       MatlabError::ErrorType::DimensionMismatch);
        return;
    }
    
    rows = real.rows;
    cols = real.cols;
    data.resize(rows * cols);
    
    for (Integer i = 0; i < rows; ++i) {
        for (Integer j = 0; j < cols; ++j) {
            data[idx(i, j)] = Complex(real(i, j), imag(i, j));
        }
    }
}

// 访问元素
Complex& ComplexMatrix::operator()(Integer i, Integer j) {
    if (i < 0 || i >= rows || j < 0 || j >= cols) {
        MatlabError::error("ComplexMatrix::operator()", "Index out of bounds",
                       MatlabError::ErrorType::OutOfBounds);
        // 返回第一个元素作为应急措施
        return data[0];
    }
    return data[idx(i, j)];
}

const Complex& ComplexMatrix::operator()(Integer i, Integer j) const {
    if (i < 0 || i >= rows || j < 0 || j >= cols) {
        MatlabError::error("ComplexMatrix::operator()", "Index out of bounds",
                       MatlabError::ErrorType::OutOfBounds);
        // 返回第一个元素作为应急措施
        return data[0];
    }
    return data[idx(i, j)];
}

// 调整大小
void ComplexMatrix::resize(Integer r, Integer c) {
    rows = r;
    cols = c;
    data.resize(r * c);
}

// 基本算术运算
ComplexMatrix ComplexMatrix::operator+(const ComplexMatrix& other) const {
    if (rows != other.rows || cols != other.cols) {
        MatlabError::error("ComplexMatrix::operator+", "Matrix dimensions must agree",
                       MatlabError::ErrorType::DimensionMismatch);
        return ComplexMatrix();
    }
    
    ComplexMatrix result(rows, cols);
    for (Integer i = 0; i < rows; ++i) {
        for (Integer j = 0; j < cols; ++j) {
            result(i, j) = (*this)(i, j) + other(i, j);
        }
    }
    return result;
}

ComplexMatrix ComplexMatrix::operator-(const ComplexMatrix& other) const {
    if (rows != other.rows || cols != other.cols) {
        MatlabError::error("ComplexMatrix::operator-", "Matrix dimensions must agree",
                       MatlabError::ErrorType::DimensionMismatch);
        return ComplexMatrix();
    }
    
    ComplexMatrix result(rows, cols);
    for (Integer i = 0; i < rows; ++i) {
        for (Integer j = 0; j < cols; ++j) {
            result(i, j) = (*this)(i, j) - other(i, j);
        }
    }
    return result;
}

ComplexMatrix ComplexMatrix::operator*(const Complex& scalar) const {
    ComplexMatrix result(rows, cols);
    for (Integer i = 0; i < rows; ++i) {
        for (Integer j = 0; j < cols; ++j) {
            result(i, j) = (*this)(i, j) * scalar;
        }
    }
    return result;
}

// 转置和共轭转置
ComplexMatrix ComplexMatrix::transpose() const {
    ComplexMatrix result(cols, rows);
    for (Integer i = 0; i < rows; ++i) {
        for (Integer j = 0; j < cols; ++j) {
            result(j, i) = (*this)(i, j);
        }
    }
    return result;
}

ComplexMatrix ComplexMatrix::ctranspose() const {
    ComplexMatrix result(cols, rows);
    for (Integer i = 0; i < rows; ++i) {
        for (Integer j = 0; j < cols; ++j) {
            result(j, i) = std::conj((*this)(i, j));
        }
    }
    return result;
}

// 获取实部和虚部
Matrix ComplexMatrix::real() const {
    Matrix result(rows, cols);
    for (Integer i = 0; i < rows; ++i) {
        for (Integer j = 0; j < cols; ++j) {
            result(i, j) = (*this)(i, j).real();
        }
    }
    return result;
}

Matrix ComplexMatrix::imag() const {
    Matrix result(rows, cols);
    for (Integer i = 0; i < rows; ++i) {
        for (Integer j = 0; j < cols; ++j) {
            result(i, j) = (*this)(i, j).imag();
        }
    }
    return result;
}

// ComplexVector构造函数和方法
ComplexVector::ComplexVector(Integer n) : ComplexMatrix(n, 1) {}

ComplexVector::ComplexVector(Integer n, const Complex& val) : ComplexMatrix(n, 1, val) {}

ComplexVector::ComplexVector(const Vector& real, const Vector& imag) {
    // 检查维度匹配
    if (real.size() != imag.size()) {
        MatlabError::error("ComplexVector", "Real and imaginary parts must have the same size",
                       MatlabError::ErrorType::DimensionMismatch);
        return;
    }

    rows = real.size();
    cols = 1;
    data.resize(rows);

    for (Integer i = 0; i < rows; ++i) {
        data[i] = Complex(real[i], imag[i]);
    }
}

Complex& ComplexVector::operator()(Integer i) {
    if (i < 0 || i >= rows) {
        MatlabError::error("ComplexVector::operator()", "Index out of bounds",
                       MatlabError::ErrorType::OutOfBounds);
        // 返回第一个元素作为应急措施
        return data[0];
    }
    return data[i];
}

const Complex& ComplexVector::operator()(Integer i) const {
    if (i < 0 || i >= rows) {
        MatlabError::error("ComplexVector::operator()", "Index out of bounds",
                       MatlabError::ErrorType::OutOfBounds);
        // 返回第一个元素作为应急措施
        return data[0];
    }
    return data[i];
}

Integer ComplexVector::size() const {
    return rows;
}

// 矩阵乘法操作
ComplexMatrix matmul(const ComplexMatrix& A, const ComplexMatrix& B) {
    if (A.cols != B.rows) {
        MatlabError::error("matmul", "Matrix dimensions must agree: A.cols != B.rows",
                       MatlabError::ErrorType::DimensionMismatch);
        return ComplexMatrix();
    }
    
    ComplexMatrix C(A.rows, B.cols);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < B.cols; ++j) {
            Complex sum(0, 0);
            for (Integer k = 0; k < A.cols; ++k) {
                sum += A(i, k) * B(k, j);
            }
            C(i, j) = sum;
        }
    }
    return C;
}

ComplexVector matvec(const ComplexMatrix& A, const ComplexVector& x) {
    if (A.cols != x.rows) {
        MatlabError::error("matvec", "Matrix dimensions must agree: A.cols != x.size",
                       MatlabError::ErrorType::DimensionMismatch);
        return ComplexVector();
    }
    
    ComplexVector y(A.rows);
    for (Integer i = 0; i < A.rows; ++i) {
        Complex sum(0, 0);
        for (Integer j = 0; j < A.cols; ++j) {
            sum += A(i, j) * x(j);
        }
        y(i) = sum;
    }
    return y;
}

Complex dot(const ComplexVector& a, const ComplexVector& b) {
    if (a.size() != b.size()) {
        MatlabError::error("dot", "Vector dimensions must agree",
                       MatlabError::ErrorType::DimensionMismatch);
        return Complex(0, 0);
    }
    
    Complex result(0, 0);
    for (Integer i = 0; i < a.size(); ++i) {
        result += std::conj(a(i)) * b(i);
    }
    return result;
}

// 创建特殊矩阵
ComplexMatrix czeros(Integer rows, Integer cols) {
    return ComplexMatrix(rows, cols, Complex(0, 0));
}

ComplexMatrix cones(Integer rows, Integer cols) {
    return ComplexMatrix(rows, cols, Complex(1, 0));
}

ComplexMatrix create_cidentity(Integer n) {
    ComplexMatrix I = czeros(n, n);
    for (Integer i = 0; i < n; ++i) {
        I(i, i) = Complex(1, 0);
    }
    return I;
}

// 复数矩阵与实矩阵的转换
ComplexMatrix toComplex(const Matrix& A) {
    ComplexMatrix result(A.rows, A.cols);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < A.cols; ++j) {
            result(i, j) = Complex(A(i, j), 0);
        }
    }
    return result;
}

Matrix real(const ComplexMatrix& A) {
    return A.real();
}

Matrix imag(const ComplexMatrix& A) {
    return A.imag();
}

Matrix abs(const ComplexMatrix& A) {
    Matrix result(A.rows, A.cols);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < A.cols; ++j) {
            result(i, j) = std::abs(A(i, j));
        }
    }
    return result;
}

Matrix arg(const ComplexMatrix& A) {
    Matrix result(A.rows, A.cols);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < A.cols; ++j) {
            result(i, j) = std::arg(A(i, j));
        }
    }
    return result;
}

} // namespace EigenWrapper 