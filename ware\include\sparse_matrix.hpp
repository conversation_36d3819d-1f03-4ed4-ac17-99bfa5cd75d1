#pragma once
#include <vector>
#include "common_types.hpp"

// -----------------------------------------------------------------------------
// Minimal CSR (Compressed Sparse Row) container tailored for domain-block matrices.
// Only supports push-back assembly in row order and SpMV.
// -----------------------------------------------------------------------------
struct CSR {
    Integer rows{0}, cols{0};
    std::vector<Integer> rowPtr;   // size = rows+1
    std::vector<Integer> colIdx;   // nnz
    std::vector<Real>    val;      // nnz

    CSR() = default;
    CSR(Integer r, Integer c): rows(r), cols(c){ rowPtr.reserve(r+1); rowPtr.push_back(0); }

    // Start a new row (call once per row in ascending order)
    inline void start_row(){ rowPtr.push_back(static_cast<Integer>(colIdx.size())); }

    inline void push_back(Integer c, Real v){
        colIdx.push_back(c); val.push_back(v);
    }

    inline void finish(){
        if(rowPtr.size()==rows) rowPtr.push_back(static_cast<Integer>(colIdx.size()));
    }

    inline Integer nnz() const { return static_cast<Integer>(val.size()); }
};

// OpenMP-accelerated sparse matrix–vector multiplication
std::vector<Real> spmv_omp(const CSR& A, const std::vector<Real>& x); 