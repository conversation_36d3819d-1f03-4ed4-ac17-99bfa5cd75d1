function [b] = dbspln(t,n,i,k,x,m)
% evaluates the B-spline basis functions B_i(x)
% evaluates the B-spline basis functions B_i(x) or mth its derivative B_i^{m}(x)
% m: order of the derivative to be computed
% n: number of basis function
% i: index of the basis function to evaluate
% k: B-spline order = polynomial_order + 1
% t: Knots vector with dimension t(n+k)
% x: Location at which one wants to evaluate the Basis function

if(m==0) 
    b=bspln(t,n,i,k,x);
elseif m>0 && m<k

    if abs(t(i+k-1) - t(i)) > eps
        c1 = (k-1)/(t(i+k-1)-t(i));
    else
        c1 = 0;
    end

    if abs(t(i+k) - t(i+1)) > eps
        c2 = (k-1)/(t(i+k)-t(i+1));
    else
        c2 = 0;
    end
    b = c1*dbspln(t,n,i,k-1,x,m-1) - c2*dbspln(t,n,i+1,k-1,x,m-1);
else
    b=0;
end

end

