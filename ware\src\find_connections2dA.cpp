#include "../include/find_connections2dA.hpp"
#include <iostream>
#include <cmath>
#include <limits>
#include <algorithm>

// 替换现有的匿名命名空间，使用与MATLAB更一致的逻辑
namespace {
    // 从域中提取四个角点坐标 - 与MATLAB版本一致
    // 角点索引: 0=左下, 1=右下, 2=左上, 3=右上
    void extract_corner_points(const Domain2dA &dom, 
                             std::vector<Real> &xtmp, 
                             std::vector<Real> &ztmp, 
                             Integer idx) {
        if (dom.model2dA.xa.rows == 0 || dom.model2dA.za.rows == 0) return;
        
        Integer nx = dom.model2dA.nx;
        Integer nz = dom.model2dA.nz;
        
        // 在C++中，角点索引为(0,0), (nx-1,0), (0,nz-1), (nx-1,nz-1)
        xtmp[0 + 4*idx] = dom.model2dA.xa(0, 0);          // 左下
        xtmp[1 + 4*idx] = dom.model2dA.xa(nx-1, 0);       // 右下
        xtmp[2 + 4*idx] = dom.model2dA.xa(0, nz-1);       // 左上
        xtmp[3 + 4*idx] = dom.model2dA.xa(nx-1, nz-1);    // 右上

        ztmp[0 + 4*idx] = dom.model2dA.za(0, 0);          // 左下
        ztmp[1 + 4*idx] = dom.model2dA.za(nx-1, 0);       // 右下
        ztmp[2 + 4*idx] = dom.model2dA.za(0, nz-1);       // 左上
        ztmp[3 + 4*idx] = dom.model2dA.za(nx-1, nz-1);    // 右上
    }
    
    // 计算两点的中点 - 用于MATLAB风格的面中点计算
    std::pair<Real, Real> midpoint(Real x1, Real y1, Real x2, Real y2) {
        return {(x1 + x2) / 2.0, (y1 + y2) / 2.0};
    }
}

std::vector<Domain2dA> find_connections2dA(std::vector<Domain2dA>& OM){
    const Real connection_threshold = 50.0; // 与MATLAB相同的阈值
    const Integer ndomain = static_cast<Integer>(OM.size());
    
    // 使用与MATLAB一致的角点存储结构
    std::vector<Real> xtmp(4 * ndomain, 0.0);  // 4个角点 × 域的数量
    std::vector<Real> ztmp(4 * ndomain, 0.0);
    
    // 提取每个域的角点坐标
    std::cout << "Extracting corner points for " << ndomain << " domains..." << std::endl;
    for (Integer iom = 0; iom < ndomain; ++iom) {
        extract_corner_points(OM[iom], xtmp, ztmp, iom);
    }
    
    // 初始化邻域矩阵 - 在MATLAB中是ndomain_iNbrs
    std::vector<std::vector<Integer>> ndomain_iNbrs(4, std::vector<Integer>(ndomain, 0));
    
    // 第一阶段：找到邻域关系 - 使用与MATLAB相同的逻辑
    for (Integer idm = 0; idm < ndomain; ++idm) {
        std::cout << "...找寻域 " << idm << " 的邻域..." << std::endl;
        
        for (Integer iFace = 0; iFace < 4; ++iFace) {
            if (ndomain_iNbrs[iFace][idm] == 0) { // 如果这个面还没找到邻居
                // 确定当前面的两个角点，与MATLAB保持一致
                Integer pt1 = 0, pt2 = 0;
                switch (iFace) {
                    case 0: // 左面 (mo)
                        pt1 = 0; pt2 = 2; break;
                    case 1: // 右面 (po)
                        pt1 = 1; pt2 = 3; break;
                    case 2: // 下面 (om)
                        pt1 = 0; pt2 = 1; break;
                    case 3: // 上面 (op)
                        pt1 = 2; pt2 = 3; break;
                }
                
                // 计算当前面的中点
                Real xmF = xtmp[pt1 + 4*idm];
                Real zmF = ztmp[pt1 + 4*idm];
                Real xpF = xtmp[pt2 + 4*idm];
                Real zpF = ztmp[pt2 + 4*idm];
                
                auto [xm_mid, zm_mid] = midpoint(xmF, zmF, xpF, zpF);
                
                // 寻找可能的邻居
                for (Integer jdm = 0; jdm < ndomain; ++jdm) {
                    if (jdm != idm) { // 不考虑自身
                        for (Integer jFace = 0; jFace < 4; ++jFace) {
                            // 确定潜在邻居面的两个角点
                            Integer pt1Nbr = 0, pt2Nbr = 0;
                            switch (jFace) {
                                case 0: // 左面
                                    pt1Nbr = 0; pt2Nbr = 2; break;
                                case 1: // 右面
                                    pt1Nbr = 1; pt2Nbr = 3; break;
                                case 2: // 下面
                                    pt1Nbr = 0; pt2Nbr = 1; break;
                                case 3: // 上面
                                    pt1Nbr = 2; pt2Nbr = 3; break;
                            }
                            
                            Real xmFNbr = xtmp[pt1Nbr + 4*jdm];
                            Real zmFNbr = ztmp[pt1Nbr + 4*jdm];
                            Real xpFNbr = xtmp[pt2Nbr + 4*jdm];
                            Real zpFNbr = ztmp[pt2Nbr + 4*jdm];
                            
                            auto [xmNbr_mid, zmNbr_mid] = midpoint(xmFNbr, zmFNbr, xpFNbr, zpFNbr);
                            
                            // 使用与MATLAB相同的距离度量
                            if (std::abs(xm_mid - xmNbr_mid) + std::abs(zm_mid - zmNbr_mid) < connection_threshold) {
                                ndomain_iNbrs[jFace][jdm] = idm + 1; // +1 转换为1-based索引，与MATLAB一致
                                ndomain_iNbrs[iFace][idm] = jdm + 1; // +1 转换为1-based索引，与MATLAB一致
                                break; // 找到邻居后跳出内循环
                            }
                        }
                    }
                }
            }
        }
        
        // 更新域结构中的邻居信息
        OM[idm].iNbr_mo = ndomain_iNbrs[0][idm];
        OM[idm].iNbr_po = ndomain_iNbrs[1][idm];
        OM[idm].iNbr_om = ndomain_iNbrs[2][idm];
        OM[idm].iNbr_op = ndomain_iNbrs[3][idm];
    }

    // 初始化面匹配矩阵 - 在MATLAB中是ndomain_iFaces
    std::vector<std::vector<Integer>> ndomain_iFaces(4, std::vector<Integer>(ndomain, 0));
    
    // 第二阶段：确定匹配面 - 使用与MATLAB相同的逻辑
    for (Integer idm = 0; idm < ndomain; ++idm) {
        std::cout << "...查找域 " << idm << " 的匹配面..." << std::endl;
        
        for (Integer iFace = 0; iFace < 4; ++iFace) {
            Real dist = std::numeric_limits<Real>::max();
            
            // 获取当前邻居
            Integer iNbr = 0;
            switch (iFace) {
                case 0: iNbr = ndomain_iNbrs[0][idm]; break; // mo
                case 1: iNbr = ndomain_iNbrs[1][idm]; break; // po
                case 2: iNbr = ndomain_iNbrs[2][idm]; break; // om
                case 3: iNbr = ndomain_iNbrs[3][idm]; break; // op
            }
            
            // 如果有邻居，确定两个域之间匹配的面
            if (iNbr != 0) {
                // 确定当前面的两个角点
                Integer pt1 = 0, pt2 = 0;
                switch (iFace) {
                    case 0: pt1 = 0; pt2 = 2; break; // 左面 (mo)
                    case 1: pt1 = 1; pt2 = 3; break; // 右面 (po)
                    case 2: pt1 = 0; pt2 = 1; break; // 下面 (om)
                    case 3: pt1 = 2; pt2 = 3; break; // 上面 (op)
                }
                
                Real xmF = xtmp[pt1 + 4*idm];
                Real zmF = ztmp[pt1 + 4*idm];
                Real xpF = xtmp[pt2 + 4*idm];
                Real zpF = ztmp[pt2 + 4*idm];
                
                // 检查邻居的每个面以找到最佳匹配
                for (Integer jFace = 0; jFace < 4; ++jFace) {
                    Integer pt1Nbr = 0, pt2Nbr = 0;
                    switch (jFace) {
                        case 0: pt1Nbr = 0; pt2Nbr = 2; break; // 左面
                        case 1: pt1Nbr = 1; pt2Nbr = 3; break; // 右面
                        case 2: pt1Nbr = 0; pt2Nbr = 1; break; // 下面
                        case 3: pt1Nbr = 2; pt2Nbr = 3; break; // 上面
                    }
                    
                    // iNbr已经是1-based索引（来自ndomain_iNbrs），转换为0-based
                    Integer iNbr0 = iNbr - 1; 
                    Real xmFNbr = xtmp[pt1Nbr + 4*iNbr0]; 
                    Real zmFNbr = ztmp[pt1Nbr + 4*iNbr0];
                    Real xpFNbr = xtmp[pt2Nbr + 4*iNbr0];
                    Real zpFNbr = ztmp[pt2Nbr + 4*iNbr0];
                    
                    // 计算同向的距离
                    Real dist1 = (xmF - xmFNbr)*(xmF - xmFNbr) + 
                                 (xpF - xpFNbr)*(xpF - xpFNbr) + 
                                 (zmF - zmFNbr)*(zmF - zmFNbr) + 
                                 (zpF - zpFNbr)*(zpF - zpFNbr);
                    
                    if (dist1 < dist) {
                        dist = dist1;
                        switch (iFace) {
                            case 0: ndomain_iFaces[0][idm] = jFace + 1; break;
                            case 1: ndomain_iFaces[1][idm] = jFace + 1; break;
                            case 2: ndomain_iFaces[2][idm] = jFace + 1; break;
                            case 3: ndomain_iFaces[3][idm] = jFace + 1; break;
                        }
                    }
                    
                    // 计算反向的距离
                    dist1 = (xmF - xpFNbr)*(xmF - xpFNbr) + 
                            (xpF - xmFNbr)*(xpF - xmFNbr) + 
                            (zmF - zpFNbr)*(zmF - zpFNbr) + 
                            (zpF - zmFNbr)*(zpF - zmFNbr);
                    
                    if (dist1 < dist) {
                        dist = dist1;
                        switch (iFace) {
                            case 0: ndomain_iFaces[0][idm] = -(jFace + 1); break;
                            case 1: ndomain_iFaces[1][idm] = -(jFace + 1); break;
                            case 2: ndomain_iFaces[2][idm] = -(jFace + 1); break;
                            case 3: ndomain_iFaces[3][idm] = -(jFace + 1); break;
                        }
                    }
                }
            }
        }
        
        // 更新域结构中的面匹配信息
        OM[idm].iFace_mo = ndomain_iFaces[0][idm];
        OM[idm].iFace_po = ndomain_iFaces[1][idm];
        OM[idm].iFace_om = ndomain_iFaces[2][idm];
        OM[idm].iFace_op = ndomain_iFaces[3][idm];
    }

    // 构建旋转矩阵 - 使用与MATLAB一致的规则
    // 在MATLAB中，对于每个域分别构建四个旋转矩阵
    std::vector<Matrix> rot_mo_vec(ndomain, zeros(2,2));
    std::vector<Matrix> rot_po_vec(ndomain, zeros(2,2));
    std::vector<Matrix> rot_om_vec(ndomain, zeros(2,2));
    std::vector<Matrix> rot_op_vec(ndomain, zeros(2,2));
    
    for (Integer idm = 0; idm < ndomain; ++idm) {
        // 处理mo方向（左侧）
        Integer iNbr_mo = OM[idm].iNbr_mo;
        Integer iFace_mo = OM[idm].iFace_mo;
        if (iNbr_mo != 0) {
            switch (std::abs(iFace_mo)) {
                case 1: // 左面与左面
                    rot_mo_vec[idm](0,0) = -1;
                    rot_mo_vec[idm](1,1) = 1;
                    break;
                case 2: // 左面与右面
                    rot_mo_vec[idm](0,0) = 1;
                    rot_mo_vec[idm](1,1) = (iFace_mo > 0) ? 1 : -1;
                    break;
                case 3: // 左面与下面
                    rot_mo_vec[idm](0,1) = -1;
                    rot_mo_vec[idm](1,0) = -1;
                    break;
                case 4: // 左面与上面
                    rot_mo_vec[idm](0,1) = 1;
                    rot_mo_vec[idm](1,0) = 1;
                    break;
            }
        }
        
        // 处理po方向（右侧）
        Integer iNbr_po = OM[idm].iNbr_po;
        Integer iFace_po = OM[idm].iFace_po;
        if (iNbr_po != 0) {
            switch (std::abs(iFace_po)) {
                case 1: // 右面与左面
                    rot_po_vec[idm](0,0) = 1;
                    rot_po_vec[idm](1,1) = (iFace_po > 0) ? 1 : -1;
                    break;
                case 2: // 右面与右面
                    rot_po_vec[idm](0,0) = -1;
                    rot_po_vec[idm](1,1) = 1;
                    break;
                case 3: // 右面与下面
                    rot_po_vec[idm](0,1) = 1;
                    rot_po_vec[idm](1,0) = 1;
                    break;
                case 4: // 右面与上面
                    rot_po_vec[idm](0,1) = -1;
                    rot_po_vec[idm](1,0) = -1;
                    break;
            }
        }
        
        // 处理om方向（下侧）
        Integer iNbr_om = OM[idm].iNbr_om;
        Integer iFace_om = OM[idm].iFace_om;
        if (iNbr_om != 0) {
            switch (std::abs(iFace_om)) {
                case 1: // 下面与左面
                    rot_om_vec[idm](1,0) = -1;
                    rot_om_vec[idm](0,1) = -1;
                    break;
                case 2: // 下面与右面
                    rot_om_vec[idm](1,0) = 1;
                    rot_om_vec[idm](0,1) = 1;
                    break;
                case 3: // 下面与下面
                    rot_om_vec[idm](1,1) = -1;
                    rot_om_vec[idm](0,0) = 1;
                    break;
                case 4: // 下面与上面
                    rot_om_vec[idm](1,1) = 1;
                    rot_om_vec[idm](0,0) = (iFace_om > 0) ? 1 : -1;
                    break;
            }
        }
        
        // 处理op方向（上侧）
        Integer iNbr_op = OM[idm].iNbr_op;
        Integer iFace_op = OM[idm].iFace_op;
        if (iNbr_op != 0) {
            switch (std::abs(iFace_op)) {
                case 1: // 上面与左面
                    rot_op_vec[idm](1,0) = 1;
                    rot_op_vec[idm](0,1) = 1;
                    break;
                case 2: // 上面与右面
                    rot_op_vec[idm](1,0) = -1;
                    rot_op_vec[idm](0,1) = -1;
                    break;
                case 3: // 上面与下面
                    rot_op_vec[idm](1,1) = 1;
                    rot_op_vec[idm](0,0) = (iFace_op > 0) ? 1 : -1;
                    break;
                case 4: // 上面与上面
                    rot_op_vec[idm](1,1) = -1;
                    rot_op_vec[idm](0,0) = 1;
                    break;
            }
        }
        
        // 更新域中的旋转矩阵
        OM[idm].rot_mo = rot_mo_vec[idm];
        OM[idm].rot_po = rot_po_vec[idm];
        OM[idm].rot_om = rot_om_vec[idm];
        OM[idm].rot_op = rot_op_vec[idm];
    }
    
    std::cout << "[find_connections2dA] 已为 " << ndomain << " 个域建立连接关系" << std::endl;
    
    return OM;
} 