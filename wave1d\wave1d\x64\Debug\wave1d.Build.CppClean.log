d:\111\wave1d\wave1d\x64\debug\vc143.pdb
d:\111\wave1d\wave1d\x64\debug\vc143.idb
d:\111\wave1d\wave1d\x64\debug\mass_matrix.obj
d:\111\wave1d\wave1d\x64\debug\lgwt.obj
d:\111\wave1d\wave1d\x64\debug\inner_product.obj
d:\111\wave1d\wave1d\x64\debug\initialize_domain1d.obj
d:\111\wave1d\wave1d\x64\debug\gllnodes.obj
d:\111\wave1d\wave1d\x64\debug\get_receiver1d.obj
d:\111\wave1d\wave1d\x64\debug\get_bandwidth.obj
d:\111\wave1d\wave1d\x64\debug\gen_dfdmatrices.obj
d:\111\wave1d\wave1d\x64\debug\find_neighbors.obj
d:\111\wave1d\wave1d\x64\debug\compute_ku1d.obj
d:\111\wave1d\wave1d\x64\debug\compute_ks1d.obj
d:\111\wave1d\wave1d\x64\debug\compute_dt.obj
d:\111\wave1d\wave1d\x64\debug\compute_boundary_uvalue_out1d.obj
d:\111\wave1d\wave1d\x64\debug\compute_boundary_uvalue_inn1d.obj
d:\111\wave1d\wave1d\x64\debug\compute_boundary_svalue_out1d.obj
d:\111\wave1d\wave1d\x64\debug\compute_boundary_svalue_inn1d.obj
d:\111\wave1d\wave1d\x64\debug\check_stability.obj
d:\111\wave1d\wave1d\x64\debug\bspln.obj
d:\111\wave1d\wave1d\x64\debug\add_source1d.obj
d:\111\wave1d\wave1d\x64\debug\main1d.obj
d:\111\wave1d\wave1d\x64\debug\update_wavefields1d.obj
d:\111\wave1d\wave1d\x64\debug\stiffness_matrix.obj
d:\111\wave1d\wave1d\x64\debug\solve1d.obj
d:\111\wave1d\wave1d\x64\debug\save_waveforms1d.obj
d:\111\wave1d\wave1d\x64\debug\save_wavefields1d.obj
d:\111\wave1d\wave1d\x64\debug\mesh_sphere1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\vc143.pdb
d:\try2\1\wave1d\wave1d\x64\debug\vc143.idb
d:\111\wave1d\x64\debug\wave1d.exe
d:\111\wave1d\x64\debug\wave1d.pdb
d:\111\wave1d\wave1d\x64\debug\wave1d.ilk
d:\try2\1\wave1d\wave1d\x64\debug\add_source1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\bspln.obj
d:\try2\1\wave1d\wave1d\x64\debug\check_stability.obj
d:\try2\1\wave1d\wave1d\x64\debug\compute_boundary_svalue_inn1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\compute_boundary_svalue_out1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\compute_boundary_uvalue_inn1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\compute_boundary_uvalue_out1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\compute_dt.obj
d:\try2\1\wave1d\wave1d\x64\debug\compute_ks1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\compute_ku1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\find_neighbors.obj
d:\try2\1\wave1d\wave1d\x64\debug\gen_dfdmatrices.obj
d:\try2\1\wave1d\wave1d\x64\debug\get_bandwidth.obj
d:\try2\1\wave1d\wave1d\x64\debug\get_receiver1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\gllnodes.obj
d:\try2\1\wave1d\wave1d\x64\debug\initialize_domain1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\inner_product.obj
d:\try2\1\wave1d\wave1d\x64\debug\lgwt.obj
d:\try2\1\wave1d\wave1d\x64\debug\main1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\mass_matrix.obj
d:\try2\1\wave1d\wave1d\x64\debug\mesh_sphere1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\save_wavefields1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\save_waveforms1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\solve1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\stiffness_matrix.obj
d:\try2\1\wave1d\wave1d\x64\debug\update_wavefields1d.obj
d:\try2\1\wave1d\wave1d\x64\debug\wave1d.ilk
d:\try2\1\wave1d\x64\debug\wave1d.pdb
d:\try2\1\wave1d\x64\debug\wave1d.exe
d:\try2\1\wave1d\wave1d\x64\debug\wave1d.tlog\cl.command.1.tlog
d:\try2\1\wave1d\wave1d\x64\debug\wave1d.tlog\cl.items.tlog
d:\try2\1\wave1d\wave1d\x64\debug\wave1d.tlog\cl.read.1.tlog
d:\try2\1\wave1d\wave1d\x64\debug\wave1d.tlog\cl.write.1.tlog
d:\try2\1\wave1d\wave1d\x64\debug\wave1d.tlog\link.command.1.tlog
d:\try2\1\wave1d\wave1d\x64\debug\wave1d.tlog\link.read.1.tlog
d:\try2\1\wave1d\wave1d\x64\debug\wave1d.tlog\link.secondary.1.tlog
d:\try2\1\wave1d\wave1d\x64\debug\wave1d.tlog\link.write.1.tlog
