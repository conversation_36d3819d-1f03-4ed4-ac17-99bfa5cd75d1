#include "check_stability.hpp"
#include <iostream>
#include <limits>
#include <cmath>

namespace wave {

void check_stability(const std::vector<Domain1D>& domains, std::size_t it)
{
    double maxU = -std::numeric_limits<double>::infinity();

    for(const auto &dom : domains)
    {
        const Matrix &Umid = dom.state.Umid;
        if(Umid.cols == 0 || it >= Umid.cols) continue;

        for(std::size_t r = 0; r < Umid.rows; ++r)
        {
            double val = Umid(r, it);
            if(!std::isfinite(val)) val = 0.0;
            if(val > maxU) maxU = val;
        }
    }

    const std::size_t LOG_INTERVAL = 5000;
    if(it % LOG_INTERVAL == 0)
        std::cout << "Time step " << it << ": maximum displacement is " << maxU << std::endl;

    if(std::fabs(maxU) > 1e10)
    {
        std::cout << "unstable, return!!!" << std::endl;
    }
}

}  