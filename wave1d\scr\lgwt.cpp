#include "lgwt.h"
#include "legacy_defs.h"
#include <vector>
#include <cmath>
#include <algorithm>

std::pair<std::vector<double>, std::vector<double>> lgwt(int N, double a, double b)
{
    const double eps = 2.220446049250313e-16;
    N = N - 1;             
    int N1 = N + 1;
    int N2 = N + 2;

    
    std::vector<double> xu(N1);
    for (int i = 0; i < N1; ++i)
        xu[i] = -1.0 + 2.0 * i / (N1 - 1);

    std::vector<double> y(N1);
    for (int i = 0; i < N1; ++i)
        y[i] = std::cos((2 * i + 1) * M_PI / (2 * N + 2)) + (0.27 / N1) * std::sin(M_PI * xu[i] * N / N2);

    
    std::vector<std::vector<double>> L(N1, std::vector<double>(N2, 0.0));
    std::vector<double> Lp(N1, 0.0);
    std::vector<double> y0(N1, 2.0);

    
    while (true)
    {
        double maxdiff = 0.0;
        for (int i = 0; i < N1; ++i)
            maxdiff = std::max(maxdiff, std::abs(y[i] - y0[i]));
        if (maxdiff <= eps)
            break;

        y0 = y;

        
        for (int i = 0; i < N1; ++i) {
            L[i][0] = 1.0;
            L[i][1] = y[i];
        }
        for (int k = 2; k <= N1; ++k) {
            for (int i = 0; i < N1; ++i) {
                L[i][k] = ((2 * k - 1) * y[i] * L[i][k - 1] - (k - 1) * L[i][k - 2]) / k;
            }
        }

        
        for (int i = 0; i < N1; ++i)
            Lp[i] = N2 * (L[i][N] - y[i] * L[i][N1]) / (1.0 - y[i] * y[i]);

        
        for (int i = 0; i < N1; ++i)
            y[i] = y0[i] - L[i][N1] / Lp[i];
    }

    
    std::vector<double> x(N1);
    std::vector<double> w(N1);
    for (int i = 0; i < N1; ++i) {
        x[i] = (a * (1.0 - y[i]) + b * (1.0 + y[i])) / 2.0;
        w[i] = (b - a) / ((1.0 - y[i] * y[i]) * Lp[i] * Lp[i]) * (N2 * N2) / (N1 * N1);
    }

    
    for (int i = 0; i < N1 / 2; ++i) {
        std::swap(x[i], x[N1 - 1 - i]);
        std::swap(w[i], w[N1 - 1 - i]);
    }

    return {x, w};
}