function [source] = get_source1d(OM,freq,dt,nt)
% GET_SOURCE Summary of this function goes here

% source side
source     = create_source1d();
source.iom = 4;
som = source.iom;

x1  = OM(som).model1d.xa(1);
x2  = OM(som).model1d.xa(end);
Nx1 = OM(som).model1d.Nx1;
px  = OM(som).model1d.px;
kx1 = px + 1;
t1  = Get_Knot_Vector(Nx1,kx1)*(x2-x1) + x1;

sgb1  = zeros(Nx1,1);
for i = 1:Nx1
    sgb1(i) = bspln(t1,Nx1,i,kx1,(x1+x2)/2);
end

invL11  = OM(som).invL11;
invMsg1 = invL11*sgb1;
source.invMsg1 = invMsg1;

re = pi*freq*((1:nt)*dt-1.5/freq);
Ft = 1e5*(1-2*re.^2).*exp(-re.^2);
source.Ft = Ft;

end

