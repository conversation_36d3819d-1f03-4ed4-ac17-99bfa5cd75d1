#include "add_source1d.hpp"
#include <iostream>
#include <cstddef>
#include <cmath>

namespace wave {
void add_source1d(std::vector<Domain1D>& domains, const Source1D& source, std::size_t it)
{
    if(source.iom >= domains.size()) return;
    if(it >= source.Ft.size()) return;

    auto &dom = domains[source.iom];
    double Ft_val = source.Ft[it];

    if(dom.state.dU2dtt1.size() < source.invMsg1.size())
        dom.state.dU2dtt1.resize(source.invMsg1.size(), 0.0);

    for(std::size_t i=0; i<source.invMsg1.size(); ++i){
        double v = Ft_val * source.invMsg1[i];
        if(std::isfinite(v))
            dom.state.dU2dtt1[i] += v;
    }

#ifdef _DEBUG
    if(it==0){
        double s=0.0; for(double v:dom.state.dU2dtt1) s+=std::fabs(v);
        std::cout << "[DEBUG] after source: sum|dU2dtt1|="<<s<<std::endl;
    }
#endif
}
} 