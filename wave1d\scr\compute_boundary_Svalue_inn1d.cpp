

#include "compute_boundary_Svalue_inn1d.hpp"
#include "matrix.hpp"
#include <cstddef>
#include <algorithm>
#include <cmath>

#ifdef _OPENMP
#include <omp.h>
#endif

namespace wave {

void compute_boundary_Svalue_inn1d(std::vector<Domain1D>& domains)
{
    const int ndomains = static_cast<int>(domains.size());
    if(ndomains == 0) return;


    #ifdef _OPENMP
    #pragma omp parallel for schedule(static) default(none) shared(domains, ndomains)
    #endif
    for(int idx = 0; idx < ndomains; ++idx)
    {
        auto &dom = domains[static_cast<std::size_t>(idx)];

        const Matrix &invLT22 = dom.invLT22;
        const std::vector<double> &S2 = dom.state.S2;

        if(invLT22.rows == 0 || invLT22.cols == 0 || S2.empty()) {
            dom.state.Sb2.clear();
            dom.state.Sbval_xm_inn = 0.0;
            dom.state.Sbval_xp_inn = 0.0;
            continue;
        }

        if(invLT22.cols != S2.size()) {
            dom.state.Sb2.assign(invLT22.rows, 0.0);
            dom.state.Sbval_xm_inn = 0.0;
            dom.state.Sbval_xp_inn = 0.0;
            continue;
        }
        dom.state.Sb2.resize(invLT22.rows);

        const size_t rows = invLT22.rows;
        const size_t cols = invLT22.cols;

        for(size_t i = 0; i < rows; ++i) {
            double sum = 0.0;
            for(size_t j = 0; j < cols; ++j) {
                sum += invLT22(i, j) * S2[j];
            }
            dom.state.Sb2[i] = std::isfinite(sum) ? sum : 0.0;
        }

        if(!dom.state.Sb2.empty()) {
            dom.state.Sbval_xm_inn = dom.state.Sb2.front();
            dom.state.Sbval_xp_inn = dom.state.Sb2.back();
        } else {
            dom.state.Sbval_xm_inn = 0.0;
            dom.state.Sbval_xp_inn = 0.0;
        }
    }
}

}