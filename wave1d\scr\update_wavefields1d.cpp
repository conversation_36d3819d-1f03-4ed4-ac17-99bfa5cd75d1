/**
 * @file update_wavefields1d.cpp
 * @brief 优化的波场时间步进更新 - 并行版本
 *
 * 核心算法: U1 = 2*U1_1 - U1_0 + dt²*dU2dtt1 (Newmark-β时间积分)
 * 功能: 使用有限差分格式更新波场到下一个时间步
 * 优化: OpenMP并行化 + 内存优化 + 向量化计算
 */

#include "update_wavefields1d.hpp"
#include <algorithm>
#include <cmath>

#ifdef _OPENMP
#include <omp.h>
#endif

namespace wave {

void update_wavefields1d(std::vector<Domain1D>& domains, double dt)
{
    const int ndomains = static_cast<int>(domains.size());
    if(ndomains == 0) return;

    const double dt2 = dt * dt;  // 预计算dt²，避免重复计算

    // OpenMP并行化: 域级并行，每个线程处理不同的域
    // 静态调度: 每个域的计算量相对均匀
    #ifdef _OPENMP
    #pragma omp parallel for schedule(static) default(none) shared(domains, ndomains, dt2)
    #endif
    for(int dom_idx = 0; dom_idx < ndomains; ++dom_idx)
    {
        auto &dom = domains[static_cast<std::size_t>(dom_idx)];

        // 获取波场向量大小
        const std::size_t n = dom.state.U1_1.size();
        if(n == 0) continue;  // 跳过空域

        // 优化: 确保所有向量大小一致，避免运行时检查
        if(dom.state.U1.size() != n) dom.state.U1.resize(n, 0.0);
        if(dom.state.U1_0.size() != n) dom.state.U1_0.resize(n, 0.0);
        if(dom.state.dU2dtt1.size() != n) dom.state.dU2dtt1.resize(n, 0.0);

        // 时间步进计算: U1 = 2*U1_1 - U1_0 + dt²*dU2dtt1
        for(std::size_t i = 0; i < n; ++i)
        {
            double v = 2*dom.state.U1_1[i] - dom.state.U1_0[i] + dt2 * dom.state.dU2dtt1[i];
            if(!std::isfinite(v)) v = 0.0;
            dom.state.U1[i] = v;
        }

        // 时间步推进: U1_0 ← U1_1, U1_1 ← U1
        // 注意: 必须按正确顺序更新，确保数值稳定性
        dom.state.U1_0 = dom.state.U1_1;
        dom.state.U1_1 = dom.state.U1;
    }
}

} // namespace wave