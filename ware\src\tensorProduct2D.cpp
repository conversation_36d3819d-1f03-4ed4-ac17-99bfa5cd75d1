#include "../include/tensorProduct2D.hpp"
#include "../include/pagemtimes.hpp"
#include <iostream>

/**
 * @brief Implement 2D tensor product operation corresponding to MATLAB version
 * 
 * This function calculates 2D tensor product quickly, corresponding to MATLAB's tensorProduct2D function
 * Calculation: val2D = Bx_mat * coef2D * By_mat^T
 * 
 * @param Bx_mat basis function matrix in x direction
 * @param By_mat basis function matrix in y direction
 * @param coef2D 2D coefficient matrix
 * @return result matrix
 */
Matrix tensorProduct2D(const Matrix& Bx_mat, const Matrix& By_mat, const Matrix& coef2D){
    // Check if matrix dimensions match
    if (Bx_mat.cols != coef2D.rows || By_mat.cols != coef2D.cols) {
        std::cerr << "tensorProduct2D: matrix dimensions mismatch" << std::endl;
        std::cerr << "Bx_mat: " << Bx_mat.rows << "x" << Bx_mat.cols << std::endl;
        std::cerr << "By_mat: " << By_mat.rows << "x" << By_mat.cols << std::endl;
        std::cerr << "coef2D: " << coef2D.rows << "x" << coef2D.cols << std::endl;
        return Matrix(); // Return empty matrix
    }
    
    try {
        // 1. Calculate val2D = Bx_mat * coef2D (equivalent to MATLAB pagemtimes)
        Matrix val2D = pagemtimes(Bx_mat, coef2D);
        
        // 2. Transpose matrix (equivalent to MATLAB permute)
        Matrix val2DT = val2D.transpose();
        
        // 3. Calculate val2DT = By_mat * val2DT
        val2DT = pagemtimes(By_mat, val2DT);
        
        // 4. Transpose again to get final result
        return val2DT.transpose();
    }
    catch (const std::exception& e) {
        std::cerr << "tensorProduct2D exception: " << e.what() << std::endl;
        return Matrix();
    }
} 