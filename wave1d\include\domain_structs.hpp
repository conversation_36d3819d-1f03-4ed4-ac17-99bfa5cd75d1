#pragma once
#include <vector>
#include "matrix.hpp"

namespace wave {

struct DomainState1D {
    std::vector<double> dU2dtt1;                 
    std::vector<double> S2;                     
    std::vector<double> Sb2;                   

    std::vector<double> Ub1;                   
    double Ubval_xm_inn{0.0}, Ubval_xp_inn{0.0};
    double Ubval_xm_out{0.0}, Ubval_xp_out{0.0};
    double Ubval_xm{0.0},   Ubval_xp{0.0};

    double Sbval_xm_inn{0.0}, Sbval_xp_inn{0.0};
    double Sbval_xm{0.0},   Sbval_xp{0.0};
    double Sbval_xm_out{0.0}, Sbval_xp_out{0.0};

    Matrix Umid;                 

    std::vector<double> dU1dx2;        
    std::vector<double> dS2dx1;           

    Matrix b1T; 

    std::vector<double> U1_0; 
    std::vector<double> U1_1;  
    std::vector<double> U1;  
};

struct Domain1D {
    int iNbrm{-1};   
    int iNbrp{-1};   

    double alpha_xm{0.0};
    double alpha_xp{0.0};

    Matrix invLT22;

    DomainState1D state;

    struct Model1D {
        std::vector<double> xa;  
        std::vector<double> Vp;  
        std::vector<double> rho; 
            int Nx1{0};         
            int px{0};              
    } model1d;

    Matrix kk12;
    Matrix kk21;
    Matrix invL11;
    Matrix invL22;
    Matrix invLT11;
    Matrix b1T; 
    double rho1{0.0};
    double Vp2{0.0};
};

struct Source1D {
    std::size_t iom{0};               
    std::vector<double> Ft;           
    std::vector<double>    invMsg1;      
};

}  