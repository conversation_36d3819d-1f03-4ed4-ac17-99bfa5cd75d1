#include <iostream>
#include <vector>
#include <fstream>
#include <iomanip>
#include <string>
#include <chrono>

#ifdef _WIN32
    #include <direct.h>
    #define mkdir(path) _mkdir(path)
#else
    #include <sys/stat.h>
    #define mkdir(path) mkdir(path, 0755)
#endif
#include "mesh_sphere1d.hpp"
#include "find_neighbors.hpp"
#include "gen_DFDMatrices.hpp"
#include "compute_dt.hpp"
#include "initialize_domain1d.hpp"
#include "get_source1d.hpp"
#include "get_receiver1d.hpp"
#include "solve1D.hpp"

template<typename Cont2D>
void dump2txt(const std::string& fn, const Cont2D& A, size_t R, size_t C)
{
    std::ofstream f(fn);
    f<<std::setprecision(17);
    for(size_t i=0;i<R;++i){
        for(size_t j=0;j<C;++j){
            f<<A(i,j)<<' ';
        }
        f<<'\n';
    }
}

int main(){
    using namespace wave;

    auto start_time = std::chrono::high_resolution_clock::now();

    std::vector<double> rads_km = {600.0, 1221.5, 2914.25, 3480.0};
    for(double &r : rads_km) r *= 1000.0; 

    const double ppw = 3.5;
    const double freq = 0.05;        
    const int    px_order = 4;       
    const double dx   = 10000.0;     
    const double duration = 1000.0;  

    const bool flag_save_files = true;

    std::vector<Domain1D> OM = mesh_sphere1d(rads_km, dx, freq, px_order, ppw);

    find_neighbors(OM);
    gen_DFDMatrices(OM);

    auto dt_nt = compute_dt(OM, duration, 0.18);
    double dt = dt_nt.first;
    std::size_t nt = dt_nt.second;

    initialize_domain1d(OM, nt);

    Source1D source = get_source1d(OM, freq, dt, nt, 3);
    Receiver1D receiver = get_receiver1d(OM, dt, nt, 6);   

    solve1D(OM, source, receiver, nt, dt);

    if(flag_save_files){

        std::string output_path = "proj_wavecpp";
        mkdir(output_path.c_str());

        for(std::size_t iom=0;iom<OM.size();++iom){
            const auto& dom = OM[iom];
            std::string fname = output_path + "/wfs_element" + std::to_string(iom+1) + "_cpp.txt";
            std::ofstream fout(fname);
            fout << std::setprecision(15);
            std::size_t Nx = dom.state.Umid.rows;
            std::size_t Nt = dom.state.Umid.cols;
            for(std::size_t r=0;r<Nx;++r){
                fout << dom.model1d.xa[r];
                for(std::size_t t=0;t<Nt;++t){
                    fout << " " << dom.state.Umid(r,t);
                }
                fout << "\n";
            }
        }
        std::string rece_file = output_path + "/rece_cpp.txt";
        std::ofstream rece_out(rece_file);
        rece_out.setf(std::ios::scientific);
        rece_out << std::setprecision(17);
        
        for(std::size_t it=0; it<receiver.ur.size(); ++it){
            double val = receiver.ur[it];
            if(!std::isfinite(val)) val = 0.0;
            rece_out << std::setprecision(15) << ((it+1)*dt) << " " << val << "\n";
        }
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto elapsed_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    std::cout << "time: " << elapsed_time.count() << std::endl;

    return 0;
}
