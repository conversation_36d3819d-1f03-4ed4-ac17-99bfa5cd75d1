/**
 * @file mass_matrix.cpp
 * @brief 优化的质量矩阵计算 - 并行版本
 *
 * 功能: 计算B样条基函数的质量矩阵
 * 算法: M[i,j] = ∫ B_i(x) * B_j(x) dx (高斯积分)
 * 优化: OpenMP并行化 + 数值积分优化 + 内存访问优化
 */

#include "mass_matrix.h"
#include "bspln.h"
#include <vector>
#include <algorithm>
#include <cstddef>
#include <cmath>

#ifdef _OPENMP
#include <omp.h>
#endif

std::vector<std::vector<double>> mass_matrix(
    int p,
    int N1,
    int N2,
    const std::vector<double>& tx1,
    const std::vector<double>& tx2,
    const std::vector<std::vector<double>>& xint,
    const std::vector<std::vector<double>>& wxint,
    int option)
{
    // 预分配结果矩阵
    std::vector<std::vector<double>> mm(static_cast<std::size_t>(N1),
                                       std::vector<double>(static_cast<std::size_t>(N2), 0.0));

    // 确定B样条阶数
    const int k = (option == 0) ? (p + 1) : p;

    // 优化: 预计算积分参数
    const std::size_t NB_intervals = xint.size();
    const std::size_t ord_gi = NB_intervals ? xint[0].size() : 0;

    if(NB_intervals == 0 || ord_gi == 0) return mm;

    // 优化的矩阵元素贡献计算函数
    auto add_contrib = [&](int ib1, int jb1) -> void {
        double sum = 0.0;

        // 优化: 减少数组访问，提高缓存效率
        for(std::size_t kd = 0; kd < NB_intervals; ++kd) {
            const auto& xint_kd = xint[kd];
            const auto& wxint_kd = wxint[kd];

            for(std::size_t lpt = 0; lpt < ord_gi; ++lpt) {
                const double xi = xint_kd[lpt];
                const double w = wxint_kd[lpt];

                // 计算B样条基函数值
                const double b1tmp1 = bspln(tx1, N1, ib1, k, xi);
                const double b1tmp2 = bspln(tx2, N2, jb1, k, xi);

                // 优化: 内联数值稳定性检查
                if(std::isfinite(b1tmp1) && std::isfinite(b1tmp2)) {
                    sum += b1tmp1 * b1tmp2 * w;
                }
            }
        }

        mm[ib1 - 1][jb1 - 1] += sum;
    };

    if(option == 0) {
        // 第一阶段: 并行计算头部区域
        const int limit_i = std::min(2*p, N1);
        const int limit_j = std::min(3*p, N2);

        #ifdef _OPENMP
        #pragma omp parallel for collapse(2) schedule(dynamic) default(none) \
                shared(mm, limit_i, limit_j, add_contrib)
        #endif
        for(int ib1 = 1; ib1 <= limit_i; ++ib1) {
            for(int jb1 = 1; jb1 <= limit_j; ++jb1) {
                add_contrib(ib1, jb1);
            }
        }

        // 第二阶段: 中间区域的模式复制（串行，因为有数据依赖）
        for(int i = 2*p + 1; i <= N1 - 2*p; ++i) {
            for(int j_offset = 0; j_offset <= 2*p; ++j_offset) {
                const int j_dest = i - p + j_offset;
                const int j_src = j_dest - 1;
                if(j_dest >= 1 && j_dest <= N2 && j_src >= 1 && j_src <= N2) {
                    mm[i - 1][j_dest - 1] = mm[i - 2][j_src - 1];
                }
            }
        }

        // 第三阶段: 并行计算尾部区域
        #ifdef _OPENMP
        #pragma omp parallel for collapse(2) schedule(dynamic) default(none) \
                shared(mm, N1, N2, p, add_contrib)
        #endif
        for(int ib1 = N1 - 2*p + 1; ib1 <= N1; ++ib1) {
            for(int jb1 = N2 - 3*p + 1; jb1 <= N2; ++jb1) {
                add_contrib(ib1, jb1);
            }
        }
    } else {
        // Option 1: 类似的并行化策略
        const int M1 = N1;
        const int limit_i = std::min(2*p, M1);
        const int limit_j = std::min(3*p, N2);

        // 第一阶段: 并行计算头部区域
        #ifdef _OPENMP
        #pragma omp parallel for collapse(2) schedule(dynamic) default(none) \
                shared(mm, limit_i, limit_j, add_contrib)
        #endif
        for(int ib1 = 1; ib1 <= limit_i; ++ib1) {
            for(int jb1 = 1; jb1 <= limit_j; ++jb1) {
                add_contrib(ib1, jb1);
            }
        }

        // 第二阶段: 中间区域的模式复制
        for(int i = 2*p + 1; i <= N2 - 2*p; ++i) {
            for(int j_offset = 0; j_offset <= 2*p; ++j_offset) {
                const int j_dest = i - p + j_offset;
                const int j_src = j_dest - 1;
                if(j_dest >= 1 && j_dest <= N2 && j_src >= 1 && j_src <= N2) {
                    mm[i - 1][j_dest - 1] = mm[i - 2][j_src - 1];
                }
            }
        }

        // 第三阶段: 并行计算尾部区域
        #ifdef _OPENMP
        #pragma omp parallel for collapse(2) schedule(dynamic) default(none) \
                shared(mm, N1, N2, p, add_contrib)
        #endif
        for(int ib1 = N1 - 2*p + 1; ib1 <= N1; ++ib1) {
            for(int jb1 = N2 - 3*p + 1; jb1 <= N2; ++jb1) {
                add_contrib(ib1, jb1);
            }
        }
    }

    auto zero_row = [&](const std::vector<std::vector<double>>& M,int i){
        for(int j=0;j<M[0].size();++j) if(std::fabs(M[i][j])>1e-14) return false;
        return true;
    };
    for(int i=0;i<N1;++i){
        if(zero_row(mm,i)){
            mm[i][i] = 1e-6;          
        }
    }
    for(int i=0;i<N2;++i){
        if(zero_row(mm,i)){
            mm[i][i] = 1e-6;
        }
    }

    return mm;
} 