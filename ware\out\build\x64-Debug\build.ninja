# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: ware
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:\新建文件夹\ware\out\build\x64-Debug\
# =============================================================================
# Object build statements for STATIC_LIBRARY target ware


#############################################
# Order-only phony target for ware

build cmake_object_order_depends_target_ware: phony || .

build CMakeFiles\ware.dir\src\Get_Knot_Vector.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\Get_Knot_Vector.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\Get_Knot_Vector_shape.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\Get_Knot_Vector_shape.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\add_source2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\add_source2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\assemble_global_matrices_sparse.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\assemble_global_matrices_sparse.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\bspln.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\bspln.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\check_stability2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\check_stability2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\complex_matrix.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\complex_matrix.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\compute_KS2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\compute_KS2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\compute_KU2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\compute_KU2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\compute_boundary_Svalue_inn2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\compute_boundary_Svalue_inn2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\compute_boundary_Svalue_out2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\compute_boundary_Svalue_out2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\compute_boundary_Uvalue_inn2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\compute_boundary_Uvalue_inn2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\compute_boundary_Uvalue_out2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\compute_boundary_Uvalue_out2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\compute_dt2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\compute_dt2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\config.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\config.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\create_source2d.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\create_source2d.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\dbspln.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\dbspln.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\dist2d.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\dist2d.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\eigen_wrapper.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\eigen_wrapper.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\error_handling.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\error_handling.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\find_connections2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\find_connections2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\gen_DFDMatrices2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\gen_DFDMatrices2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\get_receiver2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\get_receiver2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\get_source2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\get_source2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\global_assembly.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\global_assembly.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\hat_pts.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\hat_pts.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\lgwt.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\lgwt.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\mass_matrix.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\mass_matrix.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\material_model.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\material_model.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\mesh_sphere2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\mesh_sphere2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\pagemtimes.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\pagemtimes.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\refine_model2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\refine_model2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\rotate_full_stress.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\rotate_full_stress.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\save_wavefields2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\save_wavefields2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\save_waveforms2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\save_waveforms2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\setup_basis.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\setup_basis.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\solver2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\solver2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\spmv_omp.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\spmv_omp.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\stiffness_matrix.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\stiffness_matrix.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\tensorProduct2D.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\tensorProduct2D.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\update_wavefields2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\update_wavefields2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb

build CMakeFiles\ware.dir\src\initialize_domain2dA.cpp.obj: CXX_COMPILER__ware_unscanned_Debug D$:\新建文件夹\ware\src\initialize_domain2dA.cpp || cmake_object_order_depends_target_ware
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\ware.dir
  OBJECT_FILE_DIR = CMakeFiles\ware.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_PDB = ware.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target ware


#############################################
# Link the static library ware.lib

build ware.lib: CXX_STATIC_LIBRARY_LINKER__ware_Debug CMakeFiles\ware.dir\src\Get_Knot_Vector.cpp.obj CMakeFiles\ware.dir\src\Get_Knot_Vector_shape.cpp.obj CMakeFiles\ware.dir\src\add_source2dA.cpp.obj CMakeFiles\ware.dir\src\assemble_global_matrices_sparse.cpp.obj CMakeFiles\ware.dir\src\bspln.cpp.obj CMakeFiles\ware.dir\src\check_stability2dA.cpp.obj CMakeFiles\ware.dir\src\complex_matrix.cpp.obj CMakeFiles\ware.dir\src\compute_KS2dA.cpp.obj CMakeFiles\ware.dir\src\compute_KU2dA.cpp.obj CMakeFiles\ware.dir\src\compute_boundary_Svalue_inn2dA.cpp.obj CMakeFiles\ware.dir\src\compute_boundary_Svalue_out2dA.cpp.obj CMakeFiles\ware.dir\src\compute_boundary_Uvalue_inn2dA.cpp.obj CMakeFiles\ware.dir\src\compute_boundary_Uvalue_out2dA.cpp.obj CMakeFiles\ware.dir\src\compute_dt2dA.cpp.obj CMakeFiles\ware.dir\src\config.cpp.obj CMakeFiles\ware.dir\src\create_source2d.cpp.obj CMakeFiles\ware.dir\src\dbspln.cpp.obj CMakeFiles\ware.dir\src\dist2d.cpp.obj CMakeFiles\ware.dir\src\eigen_wrapper.cpp.obj CMakeFiles\ware.dir\src\error_handling.cpp.obj CMakeFiles\ware.dir\src\find_connections2dA.cpp.obj CMakeFiles\ware.dir\src\gen_DFDMatrices2dA.cpp.obj CMakeFiles\ware.dir\src\get_receiver2dA.cpp.obj CMakeFiles\ware.dir\src\get_source2dA.cpp.obj CMakeFiles\ware.dir\src\global_assembly.cpp.obj CMakeFiles\ware.dir\src\hat_pts.cpp.obj CMakeFiles\ware.dir\src\lgwt.cpp.obj CMakeFiles\ware.dir\src\mass_matrix.cpp.obj CMakeFiles\ware.dir\src\material_model.cpp.obj CMakeFiles\ware.dir\src\mesh_sphere2dA.cpp.obj CMakeFiles\ware.dir\src\pagemtimes.cpp.obj CMakeFiles\ware.dir\src\refine_model2dA.cpp.obj CMakeFiles\ware.dir\src\rotate_full_stress.cpp.obj CMakeFiles\ware.dir\src\save_wavefields2dA.cpp.obj CMakeFiles\ware.dir\src\save_waveforms2dA.cpp.obj CMakeFiles\ware.dir\src\setup_basis.cpp.obj CMakeFiles\ware.dir\src\solver2dA.cpp.obj CMakeFiles\ware.dir\src\spmv_omp.cpp.obj CMakeFiles\ware.dir\src\stiffness_matrix.cpp.obj CMakeFiles\ware.dir\src\tensorProduct2D.cpp.obj CMakeFiles\ware.dir\src\update_wavefields2dA.cpp.obj CMakeFiles\ware.dir\src\initialize_domain2dA.cpp.obj
  LANGUAGE_COMPILE_FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /machine:x64
  OBJECT_DIR = CMakeFiles\ware.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\ware.dir\ware.pdb
  TARGET_FILE = ware.lib
  TARGET_PDB = ware.pdb

# =============================================================================
# Object build statements for EXECUTABLE target main2dA


#############################################
# Order-only phony target for main2dA

build cmake_object_order_depends_target_main2dA: phony || cmake_object_order_depends_target_ware

build CMakeFiles\main2dA.dir\src\main2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\新建文件夹\ware\src\main2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /W3
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA.pdb


# =============================================================================
# Link build statements for EXECUTABLE target main2dA


#############################################
# Link the executable main2dA.exe

build main2dA.exe: CXX_EXECUTABLE_LINKER__main2dA_Debug CMakeFiles\main2dA.dir\src\main2dA.cpp.obj | ware.lib D$:\OpenBLAS-0.3.30-x64\lib\libopenblas.lib || ware.lib
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = ware.lib  D:\OpenBLAS-0.3.30-x64\lib\libopenblas.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:D:\OpenBLAS-0.3.30-x64\lib
  OBJECT_DIR = CMakeFiles\main2dA.dir
  POST_BUILD = C:\windows\system32\cmd.exe /C "cd /D D:\新建文件夹\ware\out\build\x64-Debug && C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/Users/<USER>/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/新建文件夹/ware/out/build/x64-Debug/main2dA.exe -installedDir C:/Users/<USER>/vcpkg/installed/x64-windows/debug/bin -OutVariable out"
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_FILE = main2dA.exe
  TARGET_IMPLIB = main2dA.lib
  TARGET_PDB = main2dA.pdb

# =============================================================================
# Object build statements for EXECUTABLE target test_matrix_dimensions


#############################################
# Order-only phony target for test_matrix_dimensions

build cmake_object_order_depends_target_test_matrix_dimensions: phony || cmake_object_order_depends_target_ware

build CMakeFiles\test_matrix_dimensions.dir\test_matrix_dimensions.cpp.obj: CXX_COMPILER__test_matrix_dimensions_unscanned_Debug D$:\新建文件夹\ware\test_matrix_dimensions.cpp || cmake_object_order_depends_target_test_matrix_dimensions
  DEFINES = -DUSE_EIGEN -DUSE_OPENBLAS
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819
  INCLUDES = -ID:\新建文件夹\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\test_matrix_dimensions.dir
  OBJECT_FILE_DIR = CMakeFiles\test_matrix_dimensions.dir
  TARGET_COMPILE_PDB = CMakeFiles\test_matrix_dimensions.dir\
  TARGET_PDB = test_matrix_dimensions.pdb


# =============================================================================
# Link build statements for EXECUTABLE target test_matrix_dimensions


#############################################
# Link the executable test_matrix_dimensions.exe

build test_matrix_dimensions.exe: CXX_EXECUTABLE_LINKER__test_matrix_dimensions_Debug CMakeFiles\test_matrix_dimensions.dir\test_matrix_dimensions.cpp.obj | ware.lib D$:\OpenBLAS-0.3.30-x64\lib\libopenblas.lib || ware.lib
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = ware.lib  D:\OpenBLAS-0.3.30-x64\lib\libopenblas.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:D:\OpenBLAS-0.3.30-x64\lib
  OBJECT_DIR = CMakeFiles\test_matrix_dimensions.dir
  POST_BUILD = C:\windows\system32\cmd.exe /C "cd /D D:\新建文件夹\ware\out\build\x64-Debug && C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/Users/<USER>/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/新建文件夹/ware/out/build/x64-Debug/test_matrix_dimensions.exe -installedDir C:/Users/<USER>/vcpkg/installed/x64-windows/debug/bin -OutVariable out"
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\test_matrix_dimensions.dir\
  TARGET_FILE = test_matrix_dimensions.exe
  TARGET_IMPLIB = test_matrix_dimensions.lib
  TARGET_PDB = test_matrix_dimensions.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\windows\system32\cmd.exe /C "cd /D D:\新建文件夹\ware\out\build\x64-Debug && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\windows\system32\cmd.exe /C "cd /D D:\新建文件夹\ware\out\build\x64-Debug && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SD:\新建文件夹\ware -BD:\新建文件夹\ware\out\build\x64-Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util

# =============================================================================
# Target aliases.

build main2dA: phony main2dA.exe

build test_matrix_dimensions: phony test_matrix_dimensions.exe

build ware: phony ware.lib

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/新建文件夹/ware/out/build/x64-Debug

build all: phony ware.lib main2dA.exe test_matrix_dimensions.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\GNU.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-GNU-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-GNU.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake C$:\Users\15019\vcpkg\scripts\buildsystems\vcpkg.cmake CMakeCache.txt CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake D$:\新建文件夹\ware\CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\GNU.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-GNU-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-GNU.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake C$:\Users\15019\vcpkg\scripts\buildsystems\vcpkg.cmake CMakeCache.txt CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake D$:\新建文件夹\ware\CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
