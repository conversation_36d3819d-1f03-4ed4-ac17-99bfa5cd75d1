#include "../include/Get_Knot_Vector.hpp"
#include "../include/setup_basis.hpp"
#include "../include/lgwt.hpp"
#include "../include/bspln.hpp"

Basis setup_basis(Integer N,Integer p){
    Vector tx = Get_Knot_Vector(N, p+1);
    return {N,p,tx};
}

Matrix inner_product2(const Basis& b1,const Basis& b2){
    Integer N1 = b1.nb;
    Integer M2 = b2.nb;
    Integer pN1 = b1.pb;
    Integer pM2 = b2.pb;

    const Vector& t1 = b1.tx;
    const Vector& t2 = b2.tx;

    Integer kN1 = pN1+1;
    Integer kM2 = pM2+1;
    Integer ord_gi = static_cast<Integer>(std::ceil((pN1 + pM2 + 1)/2.0));

    // create combined node vector
    std::vector<Real> nodes;
    for(Integer i=pN1; i<=N1; ++i){ nodes.push_back(t1(i)); }
    for(Integer i=pM2; i<=M2; ++i){ nodes.push_back(t2(i)); }
    std::sort(nodes.begin(), nodes.end());
    nodes.erase(std::unique(nodes.begin(), nodes.end()), nodes.end());

    Integer NB = static_cast<Integer>(nodes.size()) - 1;
    Matrix int12(NB, ord_gi); Matrix wint12(NB, ord_gi);
    for(Integer kd=0; kd<NB; ++kd){ auto [xg,wg]=lgwt(ord_gi,nodes[kd],nodes[kd+1]);
        for(Integer g=0; g<ord_gi; ++g){ int12(kd,g)=xg(g); wint12(kd,g)=wg(g);} }

    Matrix T(N1, M2, 0.0);
    for(Integer ib1=1; ib1<=N1; ++ib1){
        for(Integer jb1=1; jb1<=M2; ++jb1){
            Real sum=0.0;
            for(Integer kd=0; kd<NB; ++kd){
                for(Integer lpt=0; lpt<ord_gi; ++lpt){
                    Real b1 = bspln(t1, N1, ib1, kN1, int12(kd,lpt));
                    Real b2 = bspln(t2, M2, jb1, kM2, int12(kd,lpt));
                    sum += b1*b2*wint12(kd,lpt);
                }
            }
            T(ib1-1, jb1-1) = sum;
        }
    }
    return T;
} 