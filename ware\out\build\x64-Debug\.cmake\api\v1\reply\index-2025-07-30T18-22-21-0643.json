{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "cpack": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe", "ctest": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe", "root": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 6, "string": "3.31.6-msvc6", "suffix": "msvc6"}}, "objects": [{"jsonFile": "codemodel-v2-d9c13689412361efc57e.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-6330dd04e7e0b9499e88.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-ab5ca1d3f8ce423a0ad1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-201aa140dd25edc24932.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-MicrosoftVS": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "cmakeFiles", "version": 1}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}], "responses": [{"jsonFile": "cache-v2-6330dd04e7e0b9499e88.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-ab5ca1d3f8ce423a0ad1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "codemodel-v2-d9c13689412361efc57e.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-201aa140dd25edc24932.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}]}}}}