#include "../include/mesh_sphere2dA.hpp"
#include "../include/find_connections2dA.hpp"
#include "../include/refine_model2dA.hpp"
#include "../include/create_source2d.hpp"
#include "../include/get_source2dA.hpp"
#include "../include/get_receiver2dA.hpp"
#include "../include/compute_dt2dA.hpp"
#include "../include/add_source2dA.hpp"
#include "../include/gen_DFDMatrices2dA.hpp"
#include "../include/initialize_domain2dA.hpp"
#include "../include/compute_KU2dA.hpp"
#include "../include/compute_KS2dA.hpp"
#include "../include/compute_boundary_Uvalue_inn2dA.hpp"
#include "../include/compute_boundary_Uvalue_out2dA.hpp"
#include "../include/compute_boundary_Svalue_inn2dA.hpp"
#include "../include/compute_boundary_Svalue_out2dA.hpp"
#include "../include/update_wavefields2dA.hpp"
#include "../include/save_wavefields2dA.hpp"
#include "../include/save_waveforms2dA.hpp"
#include "../include/check_stability2dA.hpp"
#include "../include/solver2dA.hpp"
#include "../include/common_types.hpp"
#include <iostream>
#include <vector>
#include <fstream>
#include <string>
#include <iomanip>  // For setprecision
#include <direct.h> // Windows directory creation
#include <io.h> // For _access

// Helper function: Create directory
void create_directory(const std::string& path) {
    _mkdir(path.c_str()); // Windows directory creation function
}

// Helper function: Save matrix to text file with detailed info
void save_matrix_to_file(const Matrix& mat, const std::string& filename, const std::string& description = "") {
    std::ofstream outfile(filename);
    if (!outfile) {
        std::cerr << "Unable to open output file: " << filename << std::endl;
        return;
    }

    // Write header with matrix information
    outfile << "# " << description << std::endl;
    outfile << "# Matrix size: " << mat.rows << " x " << mat.cols << std::endl;
    outfile << "# Format: space-separated values, one row per line" << std::endl;

    // Write matrix data
    for (Integer i = 0; i < mat.rows; ++i) {
        for (Integer j = 0; j < mat.cols; ++j) {
            outfile << std::scientific << std::setprecision(16) << mat(i, j);
            if (j < mat.cols - 1) outfile << " ";
        }
        outfile << std::endl;
    }

    outfile.close();
    std::cout << "Matrix saved: " << filename << " (" << mat.rows << "x" << mat.cols << ")" << std::endl;
}

// Helper function: Save vector to text file
void save_vector_to_file(const Vector& vec, const std::string& filename, const std::string& description = "") {
    std::ofstream outfile(filename);
    if (!outfile) {
        std::cerr << "Unable to open output file: " << filename << std::endl;
        return;
    }

    // Write header with vector information
    outfile << "# " << description << std::endl;
    outfile << "# Vector size: " << vec.size() << std::endl;
    outfile << "# Format: one value per line" << std::endl;

    // Write vector data
    for (Integer i = 0; i < vec.size(); ++i) {
        outfile << std::scientific << std::setprecision(16) << vec(i) << std::endl;
    }

    outfile.close();
    std::cout << "Vector saved: " << filename << " (size " << vec.size() << ")" << std::endl;
}

// Helper function: Save complete domain information
void save_domain_complete(const Domain2dA& dom, const std::string& output_dir, Integer domain_idx) {
    std::string domain_dir = output_dir + "domain_" + std::to_string(domain_idx) + "/";
    create_directory(domain_dir);

    std::cout << "=== Saving complete data for Domain " << domain_idx << " ===" << std::endl;

    // Save domain parameters
    std::ofstream param_file(domain_dir + "parameters.txt");
    if (param_file) {
        param_file << "# Domain " << domain_idx << " Parameters" << std::endl;
        param_file << "iom = " << dom.iom << std::endl;
        param_file << "region = " << dom.region << std::endl;
        param_file << "x_min = " << std::scientific << std::setprecision(16) << dom.x_min << std::endl;
        param_file << "x_max = " << std::scientific << std::setprecision(16) << dom.x_max << std::endl;
        param_file << "z_min = " << std::scientific << std::setprecision(16) << dom.z_min << std::endl;
        param_file << "z_max = " << std::scientific << std::setprecision(16) << dom.z_max << std::endl;
        param_file << "Nx1 = " << dom.Nx1 << std::endl;
        param_file << "Nz1 = " << dom.Nz1 << std::endl;
        param_file << "Nx2 = " << dom.Nx2 << std::endl;
        param_file << "Nz2 = " << dom.Nz2 << std::endl;
        param_file << "px1 = " << dom.px1 << std::endl;
        param_file << "pz1 = " << dom.pz1 << std::endl;
        param_file << "mu = " << std::scientific << std::setprecision(16) << dom.mu << std::endl;
        param_file << "rho = " << std::scientific << std::setprecision(16) << dom.rho << std::endl;
        param_file << "iNbr_mo = " << dom.iNbr_mo << std::endl;
        param_file << "iNbr_po = " << dom.iNbr_po << std::endl;
        param_file << "iNbr_om = " << dom.iNbr_om << std::endl;
        param_file << "iNbr_op = " << dom.iNbr_op << std::endl;
        param_file.close();
        std::cout << "Parameters saved to: " << domain_dir << "parameters.txt" << std::endl;
    }

    // Save state matrices
    std::cout << "Saving state matrices..." << std::endl;
    if (dom.state.U12.rows > 0 && dom.state.U12.cols > 0) {
        save_matrix_to_file(dom.state.U12, domain_dir + "state_U12.txt", "State U12 matrix");
    }
    if (dom.state.U21.rows > 0 && dom.state.U21.cols > 0) {
        save_matrix_to_file(dom.state.U21, domain_dir + "state_U21.txt", "State U21 matrix");
    }
    if (dom.state.Sxx11.rows > 0 && dom.state.Sxx11.cols > 0) {
        save_matrix_to_file(dom.state.Sxx11, domain_dir + "state_Sxx11.txt", "State Sxx11 matrix");
    }
    if (dom.state.Szz11.rows > 0 && dom.state.Szz11.cols > 0) {
        save_matrix_to_file(dom.state.Szz11, domain_dir + "state_Szz11.txt", "State Szz11 matrix");
    }
    if (dom.state.Sxx22.rows > 0 && dom.state.Sxx22.cols > 0) {
        save_matrix_to_file(dom.state.Sxx22, domain_dir + "state_Sxx22.txt", "State Sxx22 matrix");
    }
    if (dom.state.Szz22.rows > 0 && dom.state.Szz22.cols > 0) {
        save_matrix_to_file(dom.state.Szz22, domain_dir + "state_Szz22.txt", "State Szz22 matrix");
    }

    // Save boundary vectors
    std::cout << "Saving boundary vectors..." << std::endl;
    if (dom.state.U12mo.size() > 0) {
        save_vector_to_file(dom.state.U12mo, domain_dir + "state_U12mo.txt", "State U12mo boundary vector");
    }
    if (dom.state.U12po.size() > 0) {
        save_vector_to_file(dom.state.U12po, domain_dir + "state_U12po.txt", "State U12po boundary vector");
    }
    if (dom.state.U21mo.size() > 0) {
        save_vector_to_file(dom.state.U21mo, domain_dir + "state_U21mo.txt", "State U21mo boundary vector");
    }
    if (dom.state.U21po.size() > 0) {
        save_vector_to_file(dom.state.U21po, domain_dir + "state_U21po.txt", "State U21po boundary vector");
    }
    if (dom.state.U12om.size() > 0) {
        save_vector_to_file(dom.state.U12om, domain_dir + "state_U12om.txt", "State U12om boundary vector");
    }
    if (dom.state.U12op.size() > 0) {
        save_vector_to_file(dom.state.U12op, domain_dir + "state_U12op.txt", "State U12op boundary vector");
    }
    if (dom.state.U21om.size() > 0) {
        save_vector_to_file(dom.state.U21om, domain_dir + "state_U21om.txt", "State U21om boundary vector");
    }
    if (dom.state.U21op.size() > 0) {
        save_vector_to_file(dom.state.U21op, domain_dir + "state_U21op.txt", "State U21op boundary vector");
    }

    // Save physical wavefields (Umid)
    std::cout << "Saving physical wavefields..." << std::endl;
    for (size_t t = 0; t < dom.state.Umid.size(); ++t) {
        if (dom.state.Umid[t].rows > 0 && dom.state.Umid[t].cols > 0) {
            std::string umid_filename = domain_dir + "state_Umid_t" + std::to_string(t) + ".txt";
            save_matrix_to_file(dom.state.Umid[t], umid_filename, "Physical wavefield at time step " + std::to_string(t));
        }
    }

    // Save DFD matrices
    std::cout << "Saving DFD matrices..." << std::endl;
    if (dom.kkx12.rows > 0 && dom.kkx12.cols > 0) {
        save_matrix_to_file(dom.kkx12, domain_dir + "kkx12.txt", "DFD matrix kkx12");
    }
    if (dom.kkz12.rows > 0 && dom.kkz12.cols > 0) {
        save_matrix_to_file(dom.kkz12, domain_dir + "kkz12.txt", "DFD matrix kkz12");
    }
    if (dom.kkx21.rows > 0 && dom.kkx21.cols > 0) {
        save_matrix_to_file(dom.kkx21, domain_dir + "kkx21.txt", "DFD matrix kkx21");
    }
    if (dom.kkz21.rows > 0 && dom.kkz21.cols > 0) {
        save_matrix_to_file(dom.kkz21, domain_dir + "kkz21.txt", "DFD matrix kkz21");
    }

    // Save inverse L matrices
    std::cout << "Saving inverse L matrices..." << std::endl;
    if (dom.invLx11.rows > 0 && dom.invLx11.cols > 0) {
        save_matrix_to_file(dom.invLx11, domain_dir + "invLx11.txt", "Inverse L matrix invLx11");
    }
    if (dom.invLz11.rows > 0 && dom.invLz11.cols > 0) {
        save_matrix_to_file(dom.invLz11, domain_dir + "invLz11.txt", "Inverse L matrix invLz11");
    }
    if (dom.invLx22.rows > 0 && dom.invLx22.cols > 0) {
        save_matrix_to_file(dom.invLx22, domain_dir + "invLx22.txt", "Inverse L matrix invLx22");
    }
    if (dom.invLz22.rows > 0 && dom.invLz22.cols > 0) {
        save_matrix_to_file(dom.invLz22, domain_dir + "invLz22.txt", "Inverse L matrix invLz22");
    }
    if (dom.invLxT11.rows > 0 && dom.invLxT11.cols > 0) {
        save_matrix_to_file(dom.invLxT11, domain_dir + "invLxT11.txt", "Inverse L transpose matrix invLxT11");
    }
    if (dom.invLzT11.rows > 0 && dom.invLzT11.cols > 0) {
        save_matrix_to_file(dom.invLzT11, domain_dir + "invLzT11.txt", "Inverse L transpose matrix invLzT11");
    }
    if (dom.invLxT22.rows > 0 && dom.invLxT22.cols > 0) {
        save_matrix_to_file(dom.invLxT22, domain_dir + "invLxT22.txt", "Inverse L transpose matrix invLxT22");
    }
    if (dom.invLzT22.rows > 0 && dom.invLzT22.cols > 0) {
        save_matrix_to_file(dom.invLzT22, domain_dir + "invLzT22.txt", "Inverse L transpose matrix invLzT22");
    }

    // Save coordinate transformation matrices
    std::cout << "Saving coordinate transformation matrices..." << std::endl;
    if (dom.dxpdx11.rows > 0 && dom.dxpdx11.cols > 0) {
        save_matrix_to_file(dom.dxpdx11, domain_dir + "dxpdx11.txt", "Coordinate derivative dxpdx11");
    }
    if (dom.dzpdx11.rows > 0 && dom.dzpdx11.cols > 0) {
        save_matrix_to_file(dom.dzpdx11, domain_dir + "dzpdx11.txt", "Coordinate derivative dzpdx11");
    }
    if (dom.dxpdz11.rows > 0 && dom.dxpdz11.cols > 0) {
        save_matrix_to_file(dom.dxpdz11, domain_dir + "dxpdz11.txt", "Coordinate derivative dxpdz11");
    }
    if (dom.dzpdz11.rows > 0 && dom.dzpdz11.cols > 0) {
        save_matrix_to_file(dom.dzpdz11, domain_dir + "dzpdz11.txt", "Coordinate derivative dzpdz11");
    }
    if (dom.dxpdx22.rows > 0 && dom.dxpdx22.cols > 0) {
        save_matrix_to_file(dom.dxpdx22, domain_dir + "dxpdx22.txt", "Coordinate derivative dxpdx22");
    }
    if (dom.dzpdx22.rows > 0 && dom.dzpdx22.cols > 0) {
        save_matrix_to_file(dom.dzpdx22, domain_dir + "dzpdx22.txt", "Coordinate derivative dzpdx22");
    }
    if (dom.dxpdz22.rows > 0 && dom.dxpdz22.cols > 0) {
        save_matrix_to_file(dom.dxpdz22, domain_dir + "dxpdz22.txt", "Coordinate derivative dxpdz22");
    }
    if (dom.dzpdz22.rows > 0 && dom.dzpdz22.cols > 0) {
        save_matrix_to_file(dom.dzpdz22, domain_dir + "dzpdz22.txt", "Coordinate derivative dzpdz22");
    }

    // Save Jacobian matrices
    std::cout << "Saving Jacobian matrices..." << std::endl;
    if (dom.Jac11.rows > 0 && dom.Jac11.cols > 0) {
        save_matrix_to_file(dom.Jac11, domain_dir + "Jac11.txt", "Jacobian matrix Jac11");
    }
    if (dom.Jac22.rows > 0 && dom.Jac22.cols > 0) {
        save_matrix_to_file(dom.Jac22, domain_dir + "Jac22.txt", "Jacobian matrix Jac22");
    }

    // Save material property matrices
    std::cout << "Saving material property matrices..." << std::endl;
    if (dom.mu11.rows > 0 && dom.mu11.cols > 0) {
        save_matrix_to_file(dom.mu11, domain_dir + "mu11.txt", "Material property mu11");
    }
    if (dom.mu22.rows > 0 && dom.mu22.cols > 0) {
        save_matrix_to_file(dom.mu22, domain_dir + "mu22.txt", "Material property mu22");
    }

    // Save basis function matrices
    std::cout << "Saving basis function matrices..." << std::endl;
    if (dom.bxT1.rows > 0 && dom.bxT1.cols > 0) {
        save_matrix_to_file(dom.bxT1, domain_dir + "bxT1.txt", "Basis function transpose bxT1");
    }
    if (dom.bzT1.rows > 0 && dom.bzT1.cols > 0) {
        save_matrix_to_file(dom.bzT1, domain_dir + "bzT1.txt", "Basis function transpose bzT1");
    }
    if (dom.bxT2.rows > 0 && dom.bxT2.cols > 0) {
        save_matrix_to_file(dom.bxT2, domain_dir + "bxT2.txt", "Basis function transpose bxT2");
    }
    if (dom.bzT2.rows > 0 && dom.bzT2.cols > 0) {
        save_matrix_to_file(dom.bzT2, domain_dir + "bzT2.txt", "Basis function transpose bzT2");
    }

    // Save mass matrices
    std::cout << "Saving mass matrices..." << std::endl;
    if (dom.mm11.rows > 0 && dom.mm11.cols > 0) {
        save_matrix_to_file(dom.mm11, domain_dir + "mm11.txt", "Mass matrix mm11");
    }
    if (dom.mm22.rows > 0 && dom.mm22.cols > 0) {
        save_matrix_to_file(dom.mm22, domain_dir + "mm22.txt", "Mass matrix mm22");
    }

    std::cout << "=== Domain " << domain_idx << " complete data saved ===" << std::endl;
}

// Helper function: Save complete OM structure
void save_complete_OM(const std::vector<Domain2dA>& OM, const std::string& output_dir) {
    std::cout << "\n======== SAVING COMPLETE OM STRUCTURE ========" << std::endl;

    // Create main output directory
    create_directory(output_dir);

    // Save global OM information
    std::ofstream global_info(output_dir + "OM_global_info.txt");
    if (global_info) {
        global_info << "# Complete OM Structure Information" << std::endl;
        global_info << "# Total number of domains: " << OM.size() << std::endl;
        global_info << "# Generated by DFDM 2D C++ implementation" << std::endl;
        global_info << std::endl;

        for (size_t i = 0; i < OM.size(); ++i) {
            global_info << "Domain " << i << ":" << std::endl;
            global_info << "  iom = " << OM[i].iom << std::endl;
            global_info << "  region = " << OM[i].region << std::endl;
            global_info << "  bounds = [" << OM[i].x_min << ", " << OM[i].x_max
                       << "] x [" << OM[i].z_min << ", " << OM[i].z_max << "]" << std::endl;
            global_info << "  basis_sizes = " << OM[i].Nx1 << "x" << OM[i].Nz1
                       << " and " << OM[i].Nx2 << "x" << OM[i].Nz2 << std::endl;
            global_info << "  material = mu=" << OM[i].mu << ", rho=" << OM[i].rho << std::endl;
            global_info << std::endl;
        }
        global_info.close();
        std::cout << "Global OM info saved to: " << output_dir << "OM_global_info.txt" << std::endl;
    }

    // Save each domain completely
    for (size_t i = 0; i < OM.size(); ++i) {
        save_domain_complete(OM[i], output_dir, static_cast<Integer>(i));
    }

    std::cout << "======== COMPLETE OM STRUCTURE SAVED ========\n" << std::endl;
}

// Helper function: Check if file exists
bool file_exists(const std::string& filename) {
    return _access(filename.c_str(), 0) == 0;
}

// Helper function: Check if configuration file exists
bool check_config_file() {
    std::string config_path = "config/config.toml";
    bool exists = file_exists(config_path);

    if (!exists) {
        // Try alternative paths
        std::vector<std::string> alternative_paths = {
            "./config/config.toml",
            "../config/config.toml",
            "../../config/config.toml"
        };

        for (const auto& path : alternative_paths) {
            if (file_exists(path)) {
                return true;
            }
        }
    }

    return exists;
}

// Helper function: Print domain information (simplified)
void print_domain_info(const std::vector<Domain2dA>& OM, Integer index) {
    if (index < 0 || index >= OM.size()) {
        return;
    }

    const Domain2dA& dom = OM[index];
    std::cout << "Domain " << index << ": region=" << dom.region
              << ", Nx1=" << dom.Nx1 << ", Nz1=" << dom.Nz1 << std::endl;
}

int main(){
    try {
        std::cout << "======== Starting DFDM 2D Simulation ========" << std::endl;
        
        // Check if configuration file exists
        bool config_exists = check_config_file();
        
        // Use same parameters as MATLAB
        Vector rad(2); 
        rad(0) = 600.0*1000;  // 600 km -> 600000 m
        rad(1) = 1221.5*1000; // 1221.5 km -> 1221500 m
        Real dx = 10000.0;
        Integer nparts = 2;
        Real freq = 0.01;
        Real ppw = 3.5;
        Real duration = 480.0;  // 与MATLAB中一致
        
        std::cout << "Step 1: Generating mesh..." << std::endl;
        auto OM = mesh_sphere2dA(rad, dx, nparts, freq, ppw);
        std::cout << "  Mesh generated, " << OM.size() << " domains" << std::endl;
        
        // 按照MATLAB流程添加find_connections2dA调用
        std::cout << "Step 2: Finding domain connections..." << std::endl;
        OM = find_connections2dA(OM);
        std::cout << "  Domain connections established" << std::endl;
        
        // Print basic domain information
        std::cout << "Generated " << OM.size() << " domains" << std::endl;
        
        // 1) geometry + Jacobian + initial placeholder matrices
        std::cout << "Step 3: Refining model..." << std::endl;

        // Create a backup config.toml if needed
        if (!config_exists) {
            std::string config_dir = "config";
            create_directory(config_dir);

            std::ofstream config_file("config/config.toml");
            if (config_file) {
                config_file << "# DFDM 2D Configuration\n";
                config_file << "ppw = 3.5\n";
                config_file << "frequency = 0.01\n";
                config_file.close();
            }
        }
        
        try {
            OM = refine_model2dA(OM);
        } catch (const std::exception& e) {
            std::cerr << "Error in refine_model2dA: " << e.what() << std::endl;
            throw;
        }
        
        std::cout << "  Model refinement complete" << std::endl;
        
        // 使用compute_dt2dA计算合适的时间步长和总步数（与MATLAB一致）
        std::cout << "Step 4: Computing time step..." << std::endl;
        auto [dt, nt] = compute_dt2dA(OM, duration);
        std::cout << "  Time step dt=" << dt << ", total steps nt=" << nt << std::endl;

        // 2) build accurate DFD / mass / stiffness matrices (fills kkx12, invLx11 ...)
        std::cout << "Step 5: Building DFD matrices..." << std::endl;
        std::cout << "DEBUG: Calling gen_DFDMatrices2dA with " << OM.size() << " domains..." << std::endl;
        try {
            OM = gen_DFDMatrices2dA(OM);
            std::cout << "DEBUG: gen_DFDMatrices2dA completed successfully" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "ERROR: gen_DFDMatrices2dA failed: " << e.what() << std::endl;
            throw;
        }
        std::cout << "  DFD matrices built" << std::endl;

        std::cout << "Step 6: Initializing domains..." << std::endl;
        OM = initialize_domain2dA(OM, nt);
        std::cout << "  Domain initialization complete" << std::endl;

        // 获取震源（与MATLAB一致）
        std::cout << "Step 7: Creating sources..." << std::endl;
        std::vector<SourceStruct> sources = get_source2dA(OM, freq, dt, nt);
        std::cout << "  Source creation complete: " << sources.size() << " sources" << std::endl;
        
        // 获取接收器（与MATLAB一致）
        std::cout << "Step 8: Creating receivers..." << std::endl;
        std::vector<Receiver2dA> receivers = get_receiver2dA(OM, dt, nt);
        std::cout << "  Receiver creation complete: " << receivers.size() << " receivers" << std::endl;

        // Create output directory
        std::string output_dir = "D:/新建文件夹/ware/output_cpp/";
        std::cout << "Creating output directory: " << output_dir << std::endl;
        create_directory(output_dir);

        // initial snapshot at t=0 (all zeros) - helps verify path
        std::cout << "Step 9: Saving initial wavefield (t=0)..." << std::endl;
        save_wavefields2dA(OM, 0, output_dir);
        std::cout << "  Initial wavefield saved" << std::endl;

        // 使用solver2dA进行主要求解（与MATLAB一致）
        std::cout << "======== Starting Time Evolution with Solver ========" << std::endl;
        try {
            auto result = solver2dA(OM, sources, receivers, dt, nt);
            OM = result.first;
            receivers = result.second;
            std::cout << "======== Solver Complete ========" << std::endl;
        } catch (const std::exception& ex) {
            std::cerr << "======== ERROR IN SOLVER ========" << std::endl;
            std::cerr << "[solver] Exception caught: " << ex.what() << std::endl;
            return -1;
        }

        // ---------- Save complete OM structure ----------
        std::cout << "\n======== SAVING COMPLETE SIMULATION RESULTS ========" << std::endl;

        // Save complete OM structure with all matrices and parameters
        save_complete_OM(OM, output_dir);

        // ---------- Save summary reference data from first domain ----------
        std::cout << "Saving summary reference data from first domain..." << std::endl;
        const auto& dom = OM[0].state;  // Get state structure from first domain

        // Save key matrices with detailed descriptions
        std::cout << "Saving key state matrices..." << std::endl;
        save_matrix_to_file(dom.U12, output_dir + "summary_U12.txt", "Final U12 displacement field from domain 0");
        save_matrix_to_file(dom.U21, output_dir + "summary_U21.txt", "Final U21 displacement field from domain 0");
        save_matrix_to_file(dom.Sxx11, output_dir + "summary_Sxx11.txt", "Final Sxx11 stress field from domain 0");
        save_matrix_to_file(dom.Szz11, output_dir + "summary_Szz11.txt", "Final Szz11 stress field from domain 0");

        // Save simulation summary
        std::ofstream summary_file(output_dir + "simulation_summary.txt");
        if (summary_file) {
            summary_file << "# DFDM 2D Simulation Summary" << std::endl;
            summary_file << "# Generated by C++ implementation" << std::endl;
            summary_file << std::endl;
            summary_file << "Simulation Parameters:" << std::endl;
            summary_file << "  Total domains: " << OM.size() << std::endl;
            summary_file << "  Frequency: " << freq << " Hz" << std::endl;
            summary_file << "  PPW: " << ppw << std::endl;
            summary_file << "  Duration: " << duration << " s" << std::endl;
            summary_file << "  Time step: " << dt << " s" << std::endl;
            summary_file << "  Total time steps: " << nt << std::endl;
            summary_file << "  Inner radius: " << rad(0)/1000.0 << " km" << std::endl;
            summary_file << "  Outer radius: " << rad(1)/1000.0 << " km" << std::endl;
            summary_file << "  Grid spacing: " << dx/1000.0 << " km" << std::endl;
            summary_file << std::endl;
            summary_file << "Output Structure:" << std::endl;
            summary_file << "  - OM_global_info.txt: Global domain information" << std::endl;
            summary_file << "  - domain_X/: Complete data for domain X" << std::endl;
            summary_file << "    - parameters.txt: Domain parameters" << std::endl;
            summary_file << "    - state_*.txt: State matrices and vectors" << std::endl;
            summary_file << "    - *.txt: DFD, coordinate, and material matrices" << std::endl;
            summary_file << "  - summary_*.txt: Key results from domain 0" << std::endl;
            summary_file.close();
            std::cout << "Simulation summary saved to: " << output_dir << "simulation_summary.txt" << std::endl;
        }

        std::cout << "✅ Complete simulation results saved to: " << output_dir << std::endl;
        std::cout << "Simulation complete" << std::endl;
        return 0;
        
    } catch(const std::exception& ex){
        std::cerr << "======== ERROR ========" << std::endl;
        std::cerr << "[main] Exception caught: " << ex.what() << std::endl;
        return -1;
    } catch(...) {
        std::cerr << "======== ERROR ========" << std::endl;
        std::cerr << "[main] Unknown exception caught" << std::endl;
        return -2;
    }
}