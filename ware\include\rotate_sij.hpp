#pragma once
#include "common_types.hpp"
/* Rotate stress components (Sxx,Szz) using 2x2 rotation matrix rot (row vector applied on right)
   Returns rotated copies. Rotations here are sign flips / swaps (matrix entries -1/1).
*/
inline void rotate_sij(Matrix &Sxx, Matrix &Szz, const Matrix &rot){
    // we model stress vector [sxx szz] * rot (2x2)
    Real a = rot(0,0), b = rot(0,1);
    Real c = rot(1,0), d = rot(1,1);
    Integer nx=Sxx.rows, nz=Sxx.cols;
    for(Integer i=0;i<nx;++i){
        for(Integer k=0;k<nz;++k){
            Real sxx = Sxx(i,k);
            Real szz = Szz(i,k);
            Sxx(i,k) = sxx*a + szz*b;
            Szz(i,k) = sxx*c + szz*d;
        }
    }
}

// Overload: rotate two 1-D vectors (same length) using 2×2 rotation matrix rot.
// Suitable for boundary arrays (e.g., Sxz12 / Sxz21) stored as Vector.
inline void rotate_sij(Vector &v1, Vector &v2, const Matrix &rot){
    if(rot.rows!=2 || rot.cols!=2) return;
    Real a=rot(0,0), b=rot(0,1), c=rot(1,0), d=rot(1,1);
    Integer n=v1.size();
    for(Integer i=0;i<n;++i){
        Real s1=v1[i], s2=v2[i];
        v1[i]=s1*a + s2*b;
        v2[i]=s1*c + s2*d;
    }
}