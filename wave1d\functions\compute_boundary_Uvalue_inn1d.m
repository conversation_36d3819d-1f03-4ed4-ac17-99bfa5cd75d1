function [OM] = compute_boundary_Uvalue_inn1d(OM)

ndomains = length(OM);

%%%%%%%%%%%%%%%%%%%%%%%%%
% transfer Orthonormal bases to Canonical B-spline bases
% compute boundary variables before communication
for iom = 1:ndomains
    invLT11 = OM(iom).invLT11;
    U1      = OM(iom).state.U1;

    Ub1     = invLT11*U1;

    OM(iom).state.Ubval_xm_inn = Ub1(1);
    OM(iom).state.Ubval_xp_inn = Ub1(end);
    OM(iom).state.Ub1          = Ub1;
end
%%%%%%%%%%%%%%%%%%%%%%%%%

end