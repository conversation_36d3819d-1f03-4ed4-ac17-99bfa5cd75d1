function [OM] = compute_boundary_Svalue_out1d(OM)


for iom = 1:length(OM)

    alpha_xm = OM(iom).alpha_xm;
    alpha_xp = OM(iom).alpha_xp;

    iNbrm = OM(iom).iNbrm;
    if iNbrm ~=0
        OM(iom).state.Sbval_xm_out = OM(iNbrm).state.Sb2(end);
    end

    iNbrp = OM(iom).iNbrp;
    if iNbrp ~=0
        OM(iom).state.Sbval_xp_out = OM(iNbrp).state.Sb2(1);
    end

    OM(iom).state.Sbval_xm = alpha_xm * OM(iom).state.Sbval_xm_inn + (1-alpha_xm) * OM(iom).state.Sbval_xm_out;
    OM(iom).state.Sbval_xp = alpha_xp * OM(iom).state.Sbval_xp_inn + (1-alpha_xp) * OM(iom).state.Sbval_xp_out;
end


end