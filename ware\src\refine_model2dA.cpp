#include "../include/refine_model2dA.hpp"
#include "../include/Get_Knot_Vector_shape.hpp"
#include "../include/bspln.hpp"
#include "../include/dbspln.hpp"
#include "../include/hat_pts.hpp"
#include "../include/pagemtimes.hpp"
#include "../include/eigen_wrapper.hpp"
#include "../include/dist2d.hpp"
#include "../include/config.hpp"
#include <iostream>
#include <algorithm>
#include <cmath>

/**
 * @brief Complete implementation of MATLAB refine_model2dA.m
 * This function processes each domain to compute B-spline coefficients,
 * coordinate transformations, spatial derivatives, and Jacobian matrices.
 */
std::vector<Domain2dA> refine_model2dA(std::vector<Domain2dA>& OM) {
    std::cout << "[refine_model2dA] Processing " << OM.size() << " domains" << std::endl;

    // Parameters from MATLAB version
    const Integer p = 5;        // B-spline order for main computation
    const Integer pt = 2;       // B-spline order for topography
    const Integer kpt = pt + 1; // = 3

    const Real Vp = 2750.0;     // P-wave velocity
    const Real rho = 2000.0;    // Density

    Integer ndomain = static_cast<Integer>(OM.size());

    for(Integer iom = 0; iom < ndomain; ++iom) {
        std::cout << "Processing domain " << iom << "..." << std::endl;

        Domain2dA& dom = OM[iom];

        Integer Nx1 = dom.Nx1;
        Integer Nz1 = dom.Nz1;

        // Safety check: prevent excessive memory allocation
        const Integer MAX_GRID_SIZE = 100;
        if(Nx1 > MAX_GRID_SIZE || Nz1 > MAX_GRID_SIZE) {
            std::cerr << "ERROR: Domain " << iom << " grid size too large (" << Nx1 << "x" << Nz1
                      << "), exceeds maximum allowed size " << MAX_GRID_SIZE << std::endl;
            std::cerr << "Terminating to prevent memory overflow." << std::endl;
            throw std::runtime_error("Grid size exceeds safety limits");
        }

        Integer Nx2 = Nx1 - 1;
        Integer Nz2 = Nz1 - 1;

        std::cout << "  Domain " << iom << " grid size: " << Nx1 << "x" << Nz1 << std::endl;

        // Generate parameter space grids
        Real dx = 1.0 / (Nx1 - 1);
        Real dz = 1.0 / (Nz1 - 1);

        Vector xps1(Nx1), zps1(Nz1);
        for(Integer i = 0; i < Nx1; ++i) xps1(i) = i * dx;
        for(Integer i = 0; i < Nz1; ++i) zps1(i) = i * dz;

        // Get topography data
        const Matrix& x2d11t = dom.model2dA.xa;
        const Matrix& z2d11t = dom.model2dA.za;

        Integer Nxt = x2d11t.rows;
        Integer Nzt = x2d11t.cols;

        std::cout << "  Topography grid size: " << Nxt << "x" << Nzt << std::endl;
        std::cout << "  Hat points: " << Nx1 << " and " << Nz1 << std::endl;

        Real dxt = 1.0 / (Nxt - 1);
        Real dzt = 1.0 / (Nzt - 1);

        Vector xpst1(Nxt), zpst1(Nzt);
        for(Integer i = 0; i < Nxt; ++i) xpst1(i) = i * dxt;
        for(Integer i = 0; i < Nzt; ++i) zpst1(i) = i * dzt;

        // Generate knot vectors for topography
        Vector txt1 = Get_Knot_Vector_shape(Nxt, kpt);
        Vector tzt1 = Get_Knot_Vector_shape(Nzt, kpt);

        std::cout << "  Building B-spline basis matrices..." << std::endl;

        // Build B-spline basis matrices for topography inversion
        Matrix B1xp_mat(Nxt, Nxt);
        Matrix B1zp_mat(Nzt, Nzt);

        // X direction basis matrix
        for(Integer ibx = 1; ibx <= Nxt; ++ibx) {
            for(Integer jpx = 1; jpx <= Nxt; ++jpx) {
                Real x0 = xpst1(jpx - 1);
                B1xp_mat(jpx - 1, ibx - 1) = bspln(txt1, Nxt, ibx, kpt, x0);
            }
        }

        // Z direction basis matrix
        for(Integer ibz = 1; ibz <= Nzt; ++ibz) {
            for(Integer jpz = 1; jpz <= Nzt; ++jpz) {
                Real z0 = zpst1(jpz - 1);
                B1zp_mat(jpz - 1, ibz - 1) = bspln(tzt1, Nzt, ibz, kpt, z0);
            }
        }

        std::cout << "  Computing matrix inverses..." << std::endl;

        // Compute matrix inverses
        Matrix invB1xp_mat = EigenWrapper::inv(B1xp_mat);
        Matrix invB1zp_mat = EigenWrapper::inv(B1zp_mat);

        std::cout << "  Computing B-spline coefficients..." << std::endl;

        // Compute B-spline coefficients
        Matrix coef11x_xp = pagemtimes(invB1xp_mat, x2d11t);
        Matrix coef11z_xp = pagemtimes(invB1xp_mat, z2d11t);

        // Transpose operations for z-direction coefficients
        Matrix x2d11tT = x2d11t.transpose();
        Matrix coef11x_zp = pagemtimes(invB1zp_mat, x2d11tT);
        coef11x_zp = coef11x_zp.transpose();

        Matrix z2d11tT = z2d11t.transpose();
        Matrix coef11z_zp = pagemtimes(invB1zp_mat, z2d11tT);
        coef11z_zp = coef11z_zp.transpose();

        std::cout << "  Building plotting basis matrices..." << std::endl;

        // Build basis matrices for plotting/evaluation at hat points
        Matrix B1xp_mat_plot(Nx1, Nxt);
        Matrix B1zp_mat_plot(Nz1, Nzt);

        for(Integer jpx = 1; jpx <= Nx1; ++jpx) {
            Real x0 = xps1(jpx - 1);
            for(Integer ib1x = 1; ib1x <= Nxt; ++ib1x) {
                B1xp_mat_plot(jpx - 1, ib1x - 1) = bspln(txt1, Nxt, ib1x, kpt, x0);
            }
        }

        for(Integer jpz = 1; jpz <= Nz1; ++jpz) {
            Real z0 = zps1(jpz - 1);
            for(Integer ib1z = 1; ib1z <= Nzt; ++ib1z) {
                B1zp_mat_plot(jpz - 1, ib1z - 1) = bspln(tzt1, Nzt, ib1z, kpt, z0);
            }
        }

        std::cout << "  Computing coordinate grids..." << std::endl;

        // Compute coordinate grids (11 grid)
        Matrix x2d11 = pagemtimes(B1xp_mat_plot, coef11x_xp);
        Matrix x2d11T = x2d11.transpose();
        x2d11T = pagemtimes(B1zp_mat_plot, x2d11T);
        x2d11 = x2d11T.transpose();

        Matrix z2d11 = pagemtimes(B1xp_mat_plot, coef11z_zp);
        Matrix z2d11T = z2d11.transpose();
        z2d11T = pagemtimes(B1zp_mat_plot, z2d11T);
        z2d11 = z2d11T.transpose();

        std::cout << "  Computing hat points..." << std::endl;

        // Calculate hat points
        auto [hatpointsx1, hatpointsx2] = hat_pts(Nx1, p);
        auto [hatpointsz1, hatpointsz2] = hat_pts(Nz1, p);

        std::cout << "  Building hat point basis matrices..." << std::endl;

        // Build basis matrices at hat points
        Matrix B1xp_mat_hat1(Nx1, Nxt);
        Matrix B1xp_mat_hat2(Nx2, Nxt);
        Matrix B1zp_mat_hat1(Nz1, Nzt);
        Matrix B1zp_mat_hat2(Nz2, Nzt);

        // B1xp_mat_hat1
        for(Integer ib1x = 1; ib1x <= Nxt; ++ib1x) {
            for(Integer jpx = 1; jpx <= Nx1; ++jpx) {
                Real x0 = hatpointsx1(jpx - 1);
                B1xp_mat_hat1(jpx - 1, ib1x - 1) = bspln(txt1, Nxt, ib1x, kpt, x0);
            }
        }

        // B1xp_mat_hat2
        for(Integer ib1x = 1; ib1x <= Nxt; ++ib1x) {
            for(Integer jpx = 1; jpx <= Nx2; ++jpx) {
                Real x0 = hatpointsx2(jpx - 1);
                B1xp_mat_hat2(jpx - 1, ib1x - 1) = bspln(txt1, Nxt, ib1x, kpt, x0);
            }
        }

        // B1zp_mat_hat1
        for(Integer ib1z = 1; ib1z <= Nzt; ++ib1z) {
            for(Integer jpz = 1; jpz <= Nz1; ++jpz) {
                Real z0 = hatpointsz1(jpz - 1);
                B1zp_mat_hat1(jpz - 1, ib1z - 1) = bspln(tzt1, Nzt, ib1z, kpt, z0);
            }
        }

        // B1zp_mat_hat2
        for(Integer ib1z = 1; ib1z <= Nzt; ++ib1z) {
            for(Integer jpz = 1; jpz <= Nz2; ++jpz) {
                Real z0 = hatpointsz2(jpz - 1);
                B1zp_mat_hat2(jpz - 1, ib1z - 1) = bspln(tzt1, Nzt, ib1z, kpt, z0);
            }
        }

        std::cout << "  Building derivative basis matrices..." << std::endl;

        // Build derivative basis matrices at hat points
        Matrix dB1xp_mat_hat1(Nx1, Nxt);
        Matrix dB1zp_mat_hat1(Nz1, Nzt);
        Matrix dB1xp_mat_hat2(Nx2, Nxt);
        Matrix dB1zp_mat_hat2(Nz2, Nzt);

        // dB1xp_mat_hat1
        for(Integer ib1x = 1; ib1x <= Nxt; ++ib1x) {
            for(Integer jpx = 1; jpx <= Nx1; ++jpx) {
                Real x0 = hatpointsx1(jpx - 1);
                dB1xp_mat_hat1(jpx - 1, ib1x - 1) = dbspln(txt1, Nxt, ib1x, kpt, x0, 1);
            }
        }

        // dB1xp_mat_hat2
        for(Integer ib1x = 1; ib1x <= Nxt; ++ib1x) {
            for(Integer jpx = 1; jpx <= Nx2; ++jpx) {
                Real x0 = hatpointsx2(jpx - 1);
                dB1xp_mat_hat2(jpx - 1, ib1x - 1) = dbspln(txt1, Nxt, ib1x, kpt, x0, 1);
            }
        }

        // dB1zp_mat_hat1
        for(Integer ib1z = 1; ib1z <= Nzt; ++ib1z) {
            for(Integer jpz = 1; jpz <= Nz1; ++jpz) {
                Real z0 = hatpointsz1(jpz - 1);
                dB1zp_mat_hat1(jpz - 1, ib1z - 1) = dbspln(tzt1, Nzt, ib1z, kpt, z0, 1);
            }
        }

        // dB1zp_mat_hat2
        for(Integer ib1z = 1; ib1z <= Nzt; ++ib1z) {
            for(Integer jpz = 1; jpz <= Nz2; ++jpz) {
                Real z0 = hatpointsz2(jpz - 1);
                dB1zp_mat_hat2(jpz - 1, ib1z - 1) = dbspln(tzt1, Nzt, ib1z, kpt, z0, 1);
            }
        }

        std::cout << "  Computing coordinates at hat points..." << std::endl;

        // Compute coordinates at hat points (11 grid)
        Matrix x2d11_hat = pagemtimes(B1xp_mat_hat1, coef11x_xp);
        Matrix x2d11_hatT = x2d11_hat.transpose();
        x2d11_hatT = pagemtimes(B1zp_mat_hat1, x2d11_hatT);
        x2d11_hat = x2d11_hatT.transpose();

        Matrix z2d11_hat = pagemtimes(B1xp_mat_hat1, coef11z_zp);
        Matrix z2d11_hatT = z2d11_hat.transpose();
        z2d11_hatT = pagemtimes(B1zp_mat_hat1, z2d11_hatT);
        z2d11_hat = z2d11_hatT.transpose();

        std::cout << "  Computing spatial derivatives..." << std::endl;

        // Calculate spatial derivatives - X direction
        // dxdxp11
        Matrix dxdxp11_hat = pagemtimes(dB1xp_mat_hat1, coef11x_xp);
        Matrix dxdxp11_hatT = dxdxp11_hat.transpose();
        dxdxp11_hatT = pagemtimes(B1zp_mat_hat1, dxdxp11_hatT);
        dxdxp11_hat = dxdxp11_hatT.transpose();

        // dxdxp12
        Matrix dxdxp12_hat = pagemtimes(dB1xp_mat_hat1, coef11x_xp);
        Matrix dxdxp12_hatT = dxdxp12_hat.transpose();
        dxdxp12_hatT = pagemtimes(B1zp_mat_hat2, dxdxp12_hatT);
        dxdxp12_hat = dxdxp12_hatT.transpose();

        // dxdxp21
        Matrix dxdxp21_hat = pagemtimes(dB1xp_mat_hat2, coef11x_xp);
        Matrix dxdxp21_hatT = dxdxp21_hat.transpose();
        dxdxp21_hatT = pagemtimes(B1zp_mat_hat1, dxdxp21_hatT);
        dxdxp21_hat = dxdxp21_hatT.transpose();

        // dxdxp22
        Matrix dxdxp22_hat = pagemtimes(dB1xp_mat_hat2, coef11x_xp);
        Matrix dxdxp22_hatT = dxdxp22_hat.transpose();
        dxdxp22_hatT = pagemtimes(B1zp_mat_hat2, dxdxp22_hatT);
        dxdxp22_hat = dxdxp22_hatT.transpose();

        // dzdxp11
        Matrix dzdxp11_hat = pagemtimes(dB1xp_mat_hat1, coef11z_xp);
        Matrix dzdxp11_hatT = dzdxp11_hat.transpose();
        dzdxp11_hatT = pagemtimes(B1zp_mat_hat1, dzdxp11_hatT);
        dzdxp11_hat = dzdxp11_hatT.transpose();

        // dzdxp12
        Matrix dzdxp12_hat = pagemtimes(dB1xp_mat_hat1, coef11z_xp);
        Matrix dzdxp12_hatT = dzdxp12_hat.transpose();
        dzdxp12_hatT = pagemtimes(B1zp_mat_hat2, dzdxp12_hatT);
        dzdxp12_hat = dzdxp12_hatT.transpose();

        // dzdxp21
        Matrix dzdxp21_hat = pagemtimes(dB1xp_mat_hat2, coef11z_xp);
        Matrix dzdxp21_hatT = dzdxp21_hat.transpose();
        dzdxp21_hatT = pagemtimes(B1zp_mat_hat1, dzdxp21_hatT);
        dzdxp21_hat = dzdxp21_hatT.transpose();

        // dzdxp22
        Matrix dzdxp22_hat = pagemtimes(dB1xp_mat_hat2, coef11z_xp);
        Matrix dzdxp22_hatT = dzdxp22_hat.transpose();
        dzdxp22_hatT = pagemtimes(B1zp_mat_hat2, dzdxp22_hatT);
        dzdxp22_hat = dzdxp22_hatT.transpose();

        // Calculate spatial derivatives - Z direction
        // dxdzp11
        Matrix dxdzp11_hat = pagemtimes(B1xp_mat_hat1, coef11x_zp);
        Matrix dxdzp11_hatT = dxdzp11_hat.transpose();
        dxdzp11_hatT = pagemtimes(dB1zp_mat_hat1, dxdzp11_hatT);
        dxdzp11_hat = dxdzp11_hatT.transpose();

        // dzdzp11
        Matrix dzdzp11_hat = pagemtimes(B1xp_mat_hat1, coef11z_zp);
        Matrix dzdzp11_hatT = dzdzp11_hat.transpose();
        dzdzp11_hatT = pagemtimes(dB1zp_mat_hat1, dzdzp11_hatT);
        dzdzp11_hat = dzdzp11_hatT.transpose();

        // dxdzp21
        Matrix dxdzp21_hat = pagemtimes(B1xp_mat_hat2, coef11x_zp);
        Matrix dxdzp21_hatT = dxdzp21_hat.transpose();
        dxdzp21_hatT = pagemtimes(dB1zp_mat_hat1, dxdzp21_hatT);
        dxdzp21_hat = dxdzp21_hatT.transpose();

        // dzdzp21
        Matrix dzdzp21_hat = pagemtimes(B1xp_mat_hat2, coef11z_zp);
        Matrix dzdzp21_hatT = dzdzp21_hat.transpose();
        dzdzp21_hatT = pagemtimes(dB1zp_mat_hat1, dzdzp21_hatT);
        dzdzp21_hat = dzdzp21_hatT.transpose();

        // dxdzp12
        Matrix dxdzp12_hat = pagemtimes(B1xp_mat_hat1, coef11x_zp);
        Matrix dxdzp12_hatT = dxdzp12_hat.transpose();
        dxdzp12_hatT = pagemtimes(dB1zp_mat_hat2, dxdzp12_hatT);
        dxdzp12_hat = dxdzp12_hatT.transpose();

        // dzdzp12
        Matrix dzdzp12_hat = pagemtimes(B1xp_mat_hat1, coef11z_zp);
        Matrix dzdzp12_hatT = dzdzp12_hat.transpose();
        dzdzp12_hatT = pagemtimes(dB1zp_mat_hat2, dzdzp12_hatT);
        dzdzp12_hat = dzdzp12_hatT.transpose();

        // dxdzp22
        Matrix dxdzp22_hat = pagemtimes(B1xp_mat_hat2, coef11x_zp);
        Matrix dxdzp22_hatT = dxdzp22_hat.transpose();
        dxdzp22_hatT = pagemtimes(dB1zp_mat_hat2, dxdzp22_hatT);
        dxdzp22_hat = dxdzp22_hatT.transpose();

        // dzdzp22
        Matrix dzdzp22_hat = pagemtimes(B1xp_mat_hat2, coef11z_zp);
        Matrix dzdzp22_hatT = dzdzp22_hat.transpose();
        dzdzp22_hatT = pagemtimes(dB1zp_mat_hat2, dzdzp22_hatT);
        dzdzp22_hat = dzdzp22_hatT.transpose();

        std::cout << "  Computing Jacobian matrices..." << std::endl;

        // Compute Jacobian matrices
        Matrix Jac11_hat(Nx1, Nz1);
        Matrix Jac12_hat(Nx1, Nz2);
        Matrix Jac21_hat(Nx2, Nz1);
        Matrix Jac22_hat(Nx2, Nz2);

        for(Integer i = 0; i < Nx1; ++i) {
            for(Integer j = 0; j < Nz1; ++j) {
                Jac11_hat(i, j) = dxdxp11_hat(i, j) * dzdzp11_hat(i, j) - dzdxp11_hat(i, j) * dxdzp11_hat(i, j);
            }
        }

        for(Integer i = 0; i < Nx1; ++i) {
            for(Integer j = 0; j < Nz2; ++j) {
                Jac12_hat(i, j) = dxdxp12_hat(i, j) * dzdzp12_hat(i, j) - dzdxp12_hat(i, j) * dxdzp12_hat(i, j);
            }
        }

        for(Integer i = 0; i < Nx2; ++i) {
            for(Integer j = 0; j < Nz1; ++j) {
                Jac21_hat(i, j) = dxdxp21_hat(i, j) * dzdzp21_hat(i, j) - dzdxp21_hat(i, j) * dxdzp21_hat(i, j);
            }
        }

        for(Integer i = 0; i < Nx2; ++i) {
            for(Integer j = 0; j < Nz2; ++j) {
                Jac22_hat(i, j) = dxdxp22_hat(i, j) * dzdzp22_hat(i, j) - dzdxp22_hat(i, j) * dxdzp22_hat(i, j);
            }
        }

        std::cout << "  Computing inverse coordinate derivatives..." << std::endl;

        // Compute inverse coordinate derivatives
        Matrix dxpdx11_hat(Nx1, Nz1);
        Matrix dzpdx11_hat(Nx1, Nz1);
        Matrix dxpdz11_hat(Nx1, Nz1);
        Matrix dzpdz11_hat(Nx1, Nz1);

        Matrix dxpdx12_hat(Nx1, Nz2);
        Matrix dzpdx12_hat(Nx1, Nz2);
        Matrix dxpdz12_hat(Nx1, Nz2);
        Matrix dzpdz12_hat(Nx1, Nz2);

        Matrix dxpdx21_hat(Nx2, Nz1);
        Matrix dzpdx21_hat(Nx2, Nz1);
        Matrix dxpdz21_hat(Nx2, Nz1);
        Matrix dzpdz21_hat(Nx2, Nz1);

        Matrix dxpdx22_hat(Nx2, Nz2);
        Matrix dzpdx22_hat(Nx2, Nz2);
        Matrix dxpdz22_hat(Nx2, Nz2);
        Matrix dzpdz22_hat(Nx2, Nz2);

        const Real EPS = 1e-14;

        // Compute 11 grid derivatives
        for(Integer i = 0; i < Nx1; ++i) {
            for(Integer j = 0; j < Nz1; ++j) {
                Real J = Jac11_hat(i, j);
                if(std::abs(J) > EPS) {
                    dxpdx11_hat(i, j) = dzdzp11_hat(i, j) / J;
                    dzpdx11_hat(i, j) = -dzdxp11_hat(i, j) / J;
                    dxpdz11_hat(i, j) = -dxdzp11_hat(i, j) / J;
                    dzpdz11_hat(i, j) = dxdxp11_hat(i, j) / J;
                } else {
                    dxpdx11_hat(i, j) = 1.0;
                    dzpdx11_hat(i, j) = 0.0;
                    dxpdz11_hat(i, j) = 0.0;
                    dzpdz11_hat(i, j) = 1.0;
                }
            }
        }

        // Compute 12 grid derivatives
        for(Integer i = 0; i < Nx1; ++i) {
            for(Integer j = 0; j < Nz2; ++j) {
                Real J = Jac12_hat(i, j);
                if(std::abs(J) > EPS) {
                    dxpdx12_hat(i, j) = dzdzp12_hat(i, j) / J;
                    dzpdx12_hat(i, j) = -dzdxp12_hat(i, j) / J;
                    dxpdz12_hat(i, j) = -dxdzp12_hat(i, j) / J;
                    dzpdz12_hat(i, j) = dxdxp12_hat(i, j) / J;
                } else {
                    dxpdx12_hat(i, j) = 1.0;
                    dzpdx12_hat(i, j) = 0.0;
                    dxpdz12_hat(i, j) = 0.0;
                    dzpdz12_hat(i, j) = 1.0;
                }
            }
        }

        // Compute 21 grid derivatives
        for(Integer i = 0; i < Nx2; ++i) {
            for(Integer j = 0; j < Nz1; ++j) {
                Real J = Jac21_hat(i, j);
                if(std::abs(J) > EPS) {
                    dxpdx21_hat(i, j) = dzdzp21_hat(i, j) / J;
                    dzpdx21_hat(i, j) = -dzdxp21_hat(i, j) / J;
                    dxpdz21_hat(i, j) = -dxdzp21_hat(i, j) / J;
                    dzpdz21_hat(i, j) = dxdxp21_hat(i, j) / J;
                } else {
                    dxpdx21_hat(i, j) = 1.0;
                    dzpdx21_hat(i, j) = 0.0;
                    dxpdz21_hat(i, j) = 0.0;
                    dzpdz21_hat(i, j) = 1.0;
                }
            }
        }

        // Compute 22 grid derivatives
        for(Integer i = 0; i < Nx2; ++i) {
            for(Integer j = 0; j < Nz2; ++j) {
                Real J = Jac22_hat(i, j);
                if(std::abs(J) > EPS) {
                    dxpdx22_hat(i, j) = dzdzp22_hat(i, j) / J;
                    dzpdx22_hat(i, j) = -dzdxp22_hat(i, j) / J;
                    dxpdz22_hat(i, j) = -dxdzp22_hat(i, j) / J;
                    dzpdz22_hat(i, j) = dxdxp22_hat(i, j) / J;
                } else {
                    dxpdx22_hat(i, j) = 1.0;
                    dzpdx22_hat(i, j) = 0.0;
                    dxpdz22_hat(i, j) = 0.0;
                    dzpdz22_hat(i, j) = 1.0;
                }
            }
        }

        std::cout << "  Storing results..." << std::endl;

        // Store results in domain structure
        dom.px1 = p;
        dom.pz1 = p;

        dom.x2d11 = x2d11;
        dom.z2d11 = z2d11;

        dom.dxpdx11 = dxpdx11_hat;
        dom.dzpdx11 = dzpdx11_hat;
        dom.dxpdz11 = dxpdz11_hat;
        dom.dzpdz11 = dzpdz11_hat;

        dom.dxpdx12 = dxpdx12_hat;
        dom.dzpdx12 = dzpdx12_hat;
        dom.dxpdz12 = dxpdz12_hat;
        dom.dzpdz12 = dzpdz12_hat;

        dom.dxpdx21 = dxpdx21_hat;
        dom.dzpdx21 = dzpdx21_hat;
        dom.dxpdz21 = dxpdz21_hat;
        dom.dzpdz21 = dzpdz21_hat;

        dom.dxpdx22 = dxpdx22_hat;
        dom.dzpdx22 = dzpdx22_hat;
        dom.dxpdz22 = dxpdz22_hat;
        dom.dzpdz22 = dzpdz22_hat;

        dom.Jac11 = Jac11_hat;
        dom.Jac12 = Jac12_hat;
        dom.Jac21 = Jac21_hat;
        dom.Jac22 = Jac22_hat;

        // Set material properties (create matrices filled with constant values)
        Real mu_value = Vp * Vp * rho;

        dom.rho12 = Matrix(Nx1, Nz2);
        dom.rho21 = Matrix(Nx2, Nz1);
        dom.mu22 = Matrix(Nx2, Nz2);
        dom.mu11 = Matrix(Nx1, Nz1);

        // Fill matrices with constant values
        for(Integer i = 0; i < Nx1; ++i) {
            for(Integer j = 0; j < Nz2; ++j) {
                dom.rho12(i, j) = rho;
            }
        }

        for(Integer i = 0; i < Nx2; ++i) {
            for(Integer j = 0; j < Nz1; ++j) {
                dom.rho21(i, j) = rho;
            }
        }

        for(Integer i = 0; i < Nx2; ++i) {
            for(Integer j = 0; j < Nz2; ++j) {
                dom.mu22(i, j) = mu_value;
            }
        }

        for(Integer i = 0; i < Nx1; ++i) {
            for(Integer j = 0; j < Nz1; ++j) {
                dom.mu11(i, j) = mu_value;
            }
        }

        std::cout << "  Domain " << iom << " processing completed" << std::endl;
    }

    std::cout << "[refine_model2dA] All domains processed successfully" << std::endl;
    return OM;
}