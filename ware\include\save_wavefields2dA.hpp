#pragma once
#include "mesh_sphere2dA.hpp"
#include <vector>
#include <string>

/**
 * @brief 计算物理波场 - 严格按照MATLAB版本实现
 *
 * 这个函数严格按照MATLAB save_wavefields2dA.m实现：
 * 1. 使用张量积计算物理空间中的位移场
 * 2. 将计算结果保存到dom.state.Umid
 * 3. 不包含额外的文件输出功能
 *
 * @param OM 域数组（非常量引用以便修改状态）
 * @param it 当前时间步
 * @param dir 输出目录（保留参数以兼容调用）
 */
void save_wavefields2dA(std::vector<Domain2dA>& OM, Integer it, const std::string& dir = "./");