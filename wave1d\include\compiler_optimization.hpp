/**
 * @file compiler_optimization.hpp
 * @brief 编译器优化提示和性能宏定义
 */

#pragma once

// 编译器优化提示
#ifdef __GNUC__
    #define FORCE_INLINE __attribute__((always_inline)) inline
    #define RESTRICT __restrict__
    #define LIKELY(x) __builtin_expect(!!(x), 1)
    #define UNLIKELY(x) __builtin_expect(!!(x), 0)
    #define PREFETCH(addr, rw, locality) __builtin_prefetch(addr, rw, locality)
    #define ASSUME_ALIGNED(ptr, alignment) __builtin_assume_aligned(ptr, alignment)
#elif defined(_MSC_VER)
    #define FORCE_INLINE __forceinline
    #define RESTRICT __restrict
    #define LIKELY(x) (x)
    #define UNLIKELY(x) (x)
    #define PREFETCH(addr, rw, locality) _mm_prefetch((char*)(addr), _MM_HINT_T0)
    #define ASSUME_ALIGNED(ptr, alignment) (ptr)
    #include <immintrin.h>
#else
    #define FORCE_INLINE inline
    #define RESTRICT
    #define LIKELY(x) (x)
    #define UNLIKELY(x) (x)
    #define PREFETCH(addr, rw, locality)
    #define ASSUME_ALIGNED(ptr, alignment) (ptr)
#endif

// 内存对齐宏
#define CACHE_LINE_SIZE 64
#define SIMD_ALIGNMENT 32

// 性能关键函数的优化提示
#define HOT_FUNCTION __attribute__((hot))
#define COLD_FUNCTION __attribute__((cold))

// 循环优化提示
#ifdef _OPENMP
    #define SIMD_LOOP _Pragma("omp simd")
    #define SIMD_LOOP_ALIGNED(vars, align) _Pragma("omp simd aligned(" #vars ":" #align ")")
    #define SIMD_REDUCTION(op, var) _Pragma("omp simd reduction(" #op ":" #var ")")
#else
    #define SIMD_LOOP
    #define SIMD_LOOP_ALIGNED(vars, align)
    #define SIMD_REDUCTION(op, var)
#endif

// 数值稳定性检查优化
FORCE_INLINE double safe_finite(double x) {
    return LIKELY(std::isfinite(x)) ? x : 0.0;
}

// 高性能矩阵-向量乘法模板
template<typename MatrixType, typename VectorType>
FORCE_INLINE void optimized_matvec(
    const MatrixType& matrix, 
    const VectorType& vector, 
    VectorType& result) {
    
    const size_t rows = matrix.rows;
    const size_t cols = matrix.cols;
    
    double* RESTRICT result_ptr = result.data();
    const double* RESTRICT vec_ptr = vector.data();
    
    for(size_t i = 0; i < rows; ++i) {
        double sum = 0.0;
        
        // 预取下一行
        if(i + 1 < rows) {
            PREFETCH(&matrix(i + 1, 0), 0, 3);
        }
        
        SIMD_REDUCTION(+, sum)
        for(size_t j = 0; j < cols; ++j) {
            sum += matrix(i, j) * vec_ptr[j];
        }
        
        result_ptr[i] = safe_finite(sum);
    }
}
