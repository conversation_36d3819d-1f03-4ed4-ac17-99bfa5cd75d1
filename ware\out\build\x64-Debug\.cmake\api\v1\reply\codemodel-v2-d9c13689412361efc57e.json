{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "ware", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "main2dA::@6890427a1f51a3e7e1df", "jsonFile": "target-main2dA-Debug-f4c6696a07b520d20f83.json", "name": "main2dA", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_matrix_dimensions::@6890427a1f51a3e7e1df", "jsonFile": "target-test_matrix_dimensions-Debug-97eb2938a14c3f633dc5.json", "name": "test_matrix_dimensions", "projectIndex": 0}, {"directoryIndex": 0, "id": "ware::@6890427a1f51a3e7e1df", "jsonFile": "target-ware-Debug-ff014a99f8e5bb2b97ad.json", "name": "ware", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/新建文件夹/ware/out/build/x64-Debug", "source": "D:/新建文件夹/ware"}, "version": {"major": 2, "minor": 8}}