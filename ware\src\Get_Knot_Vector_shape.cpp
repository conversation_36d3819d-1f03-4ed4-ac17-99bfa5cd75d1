#include "../include/Get_Knot_Vector_shape.hpp"
#include <limits>

Vector Get_Knot_Vector_shape(Integer np, Integer kp) {
    Integer pt = kp - 1;
    Vector t(np + kp);
    Real dx = 1.0 / (np + 1 - kp);

    Vector xis(np);
    for(Integer i = 0; i < np; ++i) {
        xis(i) = static_cast<Real>(i) / (np - 1);
    }

    for(Integer i = kp; i <= np + 1; ++i) {
        t(i - 1) = (i - kp) * dx;
    }

    if(pt % 2 == 0) {
        for(Integer ii = pt + 2; ii <= np; ++ii) {
            Integer id1 = ii - pt/2 - 1;
            Integer id2 = ii - pt/2;
            t(ii - 1) = (xis(id1 - 1) + xis(id2 - 1)) / 2.0;
        }
    } else {
        for(Integer ii = pt + 2; ii <= np; ++ii) {
            Integer id1 = ii - (pt + 1)/2;
            t(ii - 1) = xis(id1 - 1);
        }
    }

    Real eps = std::numeric_limits<Real>::epsilon();
    for(Integer i = 0; i < kp; ++i) {
        t(i) = t(kp - 1) - 10.0 * eps;
    }
    for(Integer i = np; i < np + kp; ++i) {
        t(i) = t(np) + 10.0 * eps;
    }

    return t;
} 