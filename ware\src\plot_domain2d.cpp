#include "../include/plot_domain2d.hpp"
#include <iostream>
#include <iomanip>
#include <cmath>
#include <algorithm>

void plot_domain2d(const std::vector<Domain2dA>& OM) {
    std::cout << "\n========================================\n";
    std::cout << "Domain Geometry and Material Properties\n";
    std::cout << "========================================\n";

    Real rmax = 0.0;

    for(Integer iom = 0; iom < static_cast<Integer>(OM.size()); ++iom) {
        const auto& domain = OM[iom];
        const Matrix& xa = domain.model2dA.xa;
        const Matrix& za = domain.model2dA.za;
        const Matrix& rho = domain.model2dA.rho;
        const Matrix& mu  = domain.model2dA.mu;

        Real x_min = 1e9, x_max = -1e9, z_min = 1e9, z_max = -1e9;
        Real rho_min = 1e9, rho_max = -1e9, mu_min = 1e9, mu_max = -1e9;
        Real domain_rmax = 0.0;

        Integer nx = xa.rows;
        Integer nz = xa.cols;

        for(Integer i = 0; i < nx; ++i) {
            for(Integer j = 0; j < nz; ++j) {
                Real x_km = xa(i, j) / 1000.0;
                Real z_km = za(i, j) / 1000.0;
                Real dist = std::sqrt(x_km * x_km + z_km * z_km);

                x_min = std::min(x_min, x_km);
                x_max = std::max(x_max, x_km);
                z_min = std::min(z_min, z_km);
                z_max = std::max(z_max, z_km);
                domain_rmax = std::max(domain_rmax, dist);

                if(i < rho.rows && j < rho.cols) {
                    rho_min = std::min(rho_min, rho(i, j));
                    rho_max = std::max(rho_max, rho(i, j));
                }
                if(i < mu.rows && j < mu.cols) {
                    mu_min = std::min(mu_min, mu(i, j));
                    mu_max = std::max(mu_max, mu(i, j));
                }
            }
        }

        rmax = std::max(rmax, domain_rmax);

        // Velocity statistics
        Real vp_min = 1e9, vp_max = -1e9;
        if(rho.rows > 0 && mu.rows > 0) {
            for(Integer i = 0; i < std::min(rho.rows, mu.rows); ++i) {
                for(Integer j = 0; j < std::min(rho.cols, mu.cols); ++j) {
                    if(rho(i, j) > 0) {
                        Real vp = std::sqrt(mu(i, j) / rho(i, j));
                        vp_min = std::min(vp_min, vp);
                        vp_max = std::max(vp_max, vp);
                    }
                }
            }
        }

        std::cout << "Domain " << std::setw(2) << iom + 1 << ":\n";
        std::cout << "  Grid size: " << nx << " x " << nz << " points\n";
        std::cout << "  Spatial bounds: x[" << std::fixed << std::setprecision(1) << x_min << ", " << x_max << "] km  z[" << z_min << ", " << z_max << "] km\n";
        std::cout << "  Max radius: " << domain_rmax << " km\n";
        if(rho_min < 1e8) {
            std::cout << "  Density: [" << std::setprecision(0) << rho_min << ", " << rho_max << "] kg/m^3\n";
        }
        if(mu_min < 1e8) {
            std::cout << "  Shear modulus: [" << std::scientific << std::setprecision(2) << mu_min << ", " << mu_max << "] Pa\n";
        }
        if(vp_min < 1e8) {
            std::cout << std::fixed;
            std::cout << "  P-wave velocity: [" << std::setprecision(0) << vp_min << ", " << vp_max << "] m/s\n";
        }
        std::cout << "----------------------------------------\n";
    }

    std::cout << "Global Statistics:\n";
    std::cout << "  Total domains: " << OM.size() << "\n";
    std::cout << "  Maximum radius: " << std::fixed << std::setprecision(1) << rmax << " km\n";
    std::cout << "========================================\n\n";
} 