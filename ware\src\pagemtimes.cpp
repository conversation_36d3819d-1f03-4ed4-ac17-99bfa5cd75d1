#include "../include/pagemtimes.hpp"

Matrix pagemtimes(const Matrix& A, const Matrix& B) {
    if(A.cols != B.rows) throw std::invalid_argument("pagemtimes: dimension mismatch");
    // 移除调试输出
    Matrix C(A.rows, B.cols);
    for(Integer i=0;i<A.rows;++i){
        for(Integer j=0;j<B.cols;++j){
            Real sum=0.0;
            for(Integer k=0;k<A.cols;++k){
                sum += A(i,k)*B(k,j);
            }
            C(i,j)=sum;
        }
    }
    return C;
}

Matrix permute_2d(const Matrix& A) {
    return A.transpose();
} 