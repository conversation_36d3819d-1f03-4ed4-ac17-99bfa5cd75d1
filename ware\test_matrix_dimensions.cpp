/**
 * @file test_matrix_dimensions.cpp
 * @brief 测试矩阵维度是否与MATLAB一致
 */

#include "include/common_types.hpp"
#include "include/gen_DFDMatrices2dA.hpp"
#include "include/mesh_sphere2dA.hpp"
#include "include/setup_basis.hpp"
#include <iostream>
#include <iomanip>

void test_basis_matrix_dimensions() {
    std::cout << "\n=== 测试基函数矩阵维度 ===" << std::endl;
    
    // 创建一个简单的测试域
    std::vector<Domain2dA> OM(1);
    Domain2dA& dom = OM[0];
    
    // 设置基本参数（与MATLAB一致）
    dom.px1 = 5;
    dom.pz1 = 5;
    dom.Nx1 = 21;  // 小规模测试
    dom.Nz1 = 21;
    
    std::cout << "测试参数:" << std::endl;
    std::cout << "  px1 = " << dom.px1 << ", pz1 = " << dom.pz1 << std::endl;
    std::cout << "  Nx1 = " << dom.Nx1 << ", Nz1 = " << dom.Nz1 << std::endl;
    std::cout << "  Nx2 = " << dom.Nx1-1 << ", Nz2 = " << dom.Nz1-1 << std::endl;
    
    try {
        // 调用gen_DFDMatrices2dA
        std::cout << "\n调用gen_DFDMatrices2dA..." << std::endl;
        OM = gen_DFDMatrices2dA(OM);
        
        // 检查基函数矩阵维度
        std::cout << "\n基函数矩阵维度检查:" << std::endl;
        std::cout << "  bxT1: " << dom.bxT1.rows << " × " << dom.bxT1.cols 
                  << " (期望: " << dom.Nx1 << " × " << dom.Nx1 << ")" << std::endl;
        std::cout << "  bxT2: " << dom.bxT2.rows << " × " << dom.bxT2.cols 
                  << " (期望: " << dom.Nx1 << " × " << (dom.Nx1-1) << ")" << std::endl;
        std::cout << "  bzT1: " << dom.bzT1.rows << " × " << dom.bzT1.cols 
                  << " (期望: " << dom.Nz1 << " × " << dom.Nz1 << ")" << std::endl;
        std::cout << "  bzT2: " << dom.bzT2.rows << " × " << dom.bzT2.cols 
                  << " (期望: " << dom.Nz1 << " × " << (dom.Nz1-1) << ")" << std::endl;
        
        // 检查刚度矩阵维度
        std::cout << "\n刚度矩阵维度检查:" << std::endl;
        std::cout << "  kkx12: " << dom.kkx12.rows << " × " << dom.kkx12.cols << std::endl;
        std::cout << "  kkz12: " << dom.kkz12.rows << " × " << dom.kkz12.cols << std::endl;
        std::cout << "  kkx21: " << dom.kkx21.rows << " × " << dom.kkx21.cols << std::endl;
        std::cout << "  kkz21: " << dom.kkz21.rows << " × " << dom.kkz21.cols << std::endl;
        
        // 检查逆变换矩阵维度
        std::cout << "\n逆变换矩阵维度检查:" << std::endl;
        std::cout << "  invLx11: " << dom.invLx11.rows << " × " << dom.invLx11.cols << std::endl;
        std::cout << "  invLxT11: " << dom.invLxT11.rows << " × " << dom.invLxT11.cols << std::endl;
        std::cout << "  invLx22: " << dom.invLx22.rows << " × " << dom.invLx22.cols << std::endl;
        std::cout << "  invLxT22: " << dom.invLxT22.rows << " × " << dom.invLxT22.cols << std::endl;
        
        // 验证维度正确性
        bool dimensions_correct = true;
        
        if (dom.bxT1.rows != dom.Nx1 || dom.bxT1.cols != dom.Nx1) {
            std::cout << "❌ bxT1维度错误!" << std::endl;
            dimensions_correct = false;
        }
        
        if (dom.bxT2.rows != dom.Nx1 || dom.bxT2.cols != (dom.Nx1-1)) {
            std::cout << "❌ bxT2维度错误!" << std::endl;
            dimensions_correct = false;
        }
        
        if (dom.bzT1.rows != dom.Nz1 || dom.bzT1.cols != dom.Nz1) {
            std::cout << "❌ bzT1维度错误!" << std::endl;
            dimensions_correct = false;
        }
        
        if (dom.bzT2.rows != dom.Nz1 || dom.bzT2.cols != (dom.Nz1-1)) {
            std::cout << "❌ bzT2维度错误!" << std::endl;
            dimensions_correct = false;
        }
        
        if (dimensions_correct) {
            std::cout << "✅ 所有矩阵维度正确!" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ 测试失败: " << e.what() << std::endl;
    }
}

void test_setup_basis_function() {
    std::cout << "\n=== 测试setup_basis函数 ===" << std::endl;
    
    Integer N = 21;
    Integer p = 5;
    
    std::cout << "测试参数: N = " << N << ", p = " << p << std::endl;
    
    try {
        Basis basis = setup_basis(N, p);
        
        std::cout << "返回的Basis结构:" << std::endl;
        std::cout << "  nb = " << basis.nb << " (期望: " << N << ")" << std::endl;
        std::cout << "  pb = " << basis.pb << " (期望: " << p << ")" << std::endl;
        std::cout << "  tx.size() = " << basis.tx.size() << " (期望: " << N + p + 1 << ")" << std::endl;
        
        if (basis.nb == N && basis.pb == p && basis.tx.size() == N + p + 1) {
            std::cout << "✅ setup_basis函数正确!" << std::endl;
        } else {
            std::cout << "❌ setup_basis函数有问题!" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ setup_basis测试失败: " << e.what() << std::endl;
    }
}

void test_inner_product2_function() {
    std::cout << "\n=== 测试inner_product2函数 ===" << std::endl;
    
    try {
        Basis basis1 = setup_basis(11, 3);
        Basis basis2 = setup_basis(9, 2);
        
        std::cout << "测试参数:" << std::endl;
        std::cout << "  basis1: N = " << basis1.nb << ", p = " << basis1.pb << std::endl;
        std::cout << "  basis2: N = " << basis2.nb << ", p = " << basis2.pb << std::endl;
        
        Matrix result = inner_product2(basis1, basis2);
        
        std::cout << "inner_product2结果矩阵维度: " << result.rows << " × " << result.cols << std::endl;
        std::cout << "期望维度: " << basis1.nb << " × " << basis2.nb << std::endl;
        
        if (result.rows == basis1.nb && result.cols == basis2.nb) {
            std::cout << "✅ inner_product2函数维度正确!" << std::endl;
            
            // 检查结果是否有合理的数值
            Real sum = 0.0;
            for (Integer i = 0; i < result.rows; ++i) {
                for (Integer j = 0; j < result.cols; ++j) {
                    sum += std::abs(result(i, j));
                }
            }
            std::cout << "矩阵元素绝对值总和: " << sum << std::endl;
            
            if (sum > 1e-10) {
                std::cout << "✅ inner_product2函数数值合理!" << std::endl;
            } else {
                std::cout << "⚠️ inner_product2函数可能返回零矩阵!" << std::endl;
            }
            
        } else {
            std::cout << "❌ inner_product2函数维度错误!" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ inner_product2测试失败: " << e.what() << std::endl;
    }
}

int main() {
    std::cout << "矩阵维度和函数正确性测试" << std::endl;
    std::cout << "==============================" << std::endl;
    
    test_setup_basis_function();
    test_inner_product2_function();
    test_basis_matrix_dimensions();
    
    std::cout << "\n测试完成。" << std::endl;
    return 0;
}
