#include "inner_product.hpp"
#include "Get_Knot_Vector.h"
#include "bspln.h"
#include "lgwt.h"
#include <vector>
#include <cmath>
#include <algorithm>

namespace wave {

Matrix inner_product(int N1, int pN1, int M2, int pM2)
{
    const int kN1 = pN1 + 1;
    const int kN2 = pM2 + 1;
    const int ord_gi = static_cast<int>(std::ceil((pN1 + pM2) / 2.0));

    std::vector<double> t1 = Get_Knot_Vector(N1, kN1);
    std::vector<double> t2 = Get_Knot_Vector(M2, kN2);

    std::vector<double> nodes1(t1.begin() + pN1, t1.begin() + (N1 + kN1 - pN1));
    std::vector<double> nodes2(t2.begin() + pM2, t2.begin() + (M2 + kN2 - pM2));

    std::vector<double> nodes12 = nodes1;
    nodes12.insert(nodes12.end(), nodes2.begin(), nodes2.end());
    std::sort(nodes12.begin(), nodes12.end());
    nodes12.erase(std::unique(nodes12.begin(), nodes12.end()), nodes12.end());

    const int NB_intervals12 = static_cast<int>(nodes12.size()) - 1;

    std::vector<std::vector<double>> quad_x(NB_intervals12);
    std::vector<std::vector<double>> quad_w(NB_intervals12);

    for(int kd = 0; kd < NB_intervals12; ++kd){
        auto result = lgwt(ord_gi, nodes12[kd], nodes12[kd+1]);
        quad_x[kd] = std::move(result.first);
        quad_w[kd] = std::move(result.second);
    }

    Matrix T12(static_cast<std::size_t>(N1), static_cast<std::size_t>(M2), 0.0);

    for(int ib1 = 1; ib1 <= N1; ++ib1){
        for(int jb1 = 1; jb1 <= M2; ++jb1){
            double accum = 0.0;
            for(int kd = 0; kd < NB_intervals12; ++kd){
                const auto &xi_vec = quad_x[kd];
                const auto &wi_vec = quad_w[kd];
                for(int lpt = 0; lpt < ord_gi; ++lpt){
                    double xi = xi_vec[lpt];
                    double b1tmp1 = bspln(t1, N1, ib1, kN1, xi);
                    double b1tmp2 = bspln(t2, M2, jb1, kN2, xi);
                    accum += b1tmp1 * b1tmp2 * wi_vec[lpt];
                }
            }
            T12(ib1 - 1, jb1 - 1) = accum;
        }
    }

    return T12;
}

}  