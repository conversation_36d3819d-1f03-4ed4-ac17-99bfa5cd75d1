﻿#ifndef DBSPLN_H
#define DBSPLN_H

#include <vector>
#include <cmath>
#include "bspln.h"
#include "legacy_defs.h"

inline double dbspln_rec(const std::vector<double>& t,int n,int i,int k,double x,int m){
    if(m==0) return bspln(t,n,i+1,k,x); 
    if(k==1) return 0.0;
    const double EPS=1e-12;
    double c1=0.0,c2=0.0;
    if(std::abs(t[i+k-1]-t[i])>EPS) c1=(k-1)/(t[i+k-1]-t[i]);
    if(std::abs(t[i+k]-t[i+1])>EPS) c2=(k-1)/(t[i+k]-t[i+1]);
    return c1*dbspln_rec(t,n,i,k-1,x,m-1)-c2*dbspln_rec(t,n,i+1,k-1,x,m-1);
}

inline double dbspln(const std::vector<double>& t,int n,int i,int k,double x,int m){
    int idx=i-1; //0-based
    if(idx<0||idx>=n) return 0.0;
    if(m<0||m>=k) return 0.0;
    return dbspln_rec(t,n,idx,k,x,m);
}

#endif 