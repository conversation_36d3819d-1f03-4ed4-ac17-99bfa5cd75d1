#include "../include/dbspln.hpp"
#include "../include/bspln.hpp"
#include <limits>

// MATLAB dbspln translated to C++
Real dbspln(const Vector& t, Integer n, Integer i, Integer k, Real x, Integer m) {
    const Real EPS = 1e-14;
    const Real SAFE_EPS = 1e-15;

    if(i < 1 || i > n || k < 1 || m < 0) return 0.0;
    Integer idx = i - 1;
    if(idx + k >= static_cast<Integer>(t.size())) return 0.0;

    if(m == 0) {
        return bspln(t, n, i, k, x);
    }
    if(m > 0 && m < k) {
        Real c1 = 0.0, c2 = 0.0;
        if((t(idx + k - 1) - t(idx)) > EPS)
            c1 = static_cast<Real>(k - 1) / (t(idx + k - 1) - t(idx) + SAFE_EPS);
        if((t(idx + k) - t(idx + 1)) > EPS)
            c2 = static_cast<Real>(k - 1) / (t(idx + k) - t(idx + 1) + SAFE_EPS);
        return c1 * dbspln(t, n, i, k - 1, x, m - 1) - c2 * dbspln(t, n, i + 1, k - 1, x, m - 1);
    }
    return 0.0;
} 