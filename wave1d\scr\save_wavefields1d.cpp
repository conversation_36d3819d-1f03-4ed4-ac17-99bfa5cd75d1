#include "save_wavefields1d.hpp"
#include "matrix.hpp"
//#include <matio.h>
#include <algorithm>
#include <iostream>
namespace wave {
void save_wavefields1d(std::vector<Domain1D>& domains, std::size_t it)
{
    for (auto &dom : domains)
    {
        const Matrix &invLT11 = dom.invLT11;
        const std::vector<double> &U1 = dom.state.U1;
        if (invLT11.rows == 0 || invLT11.cols == 0 ||
            U1.empty() || invLT11.cols != U1.size())
            continue;

        std::vector<double> Umid_mid  = matvec(invLT11, U1);         
        std::vector<double> Umid_real = matvec(dom.b1T, Umid_mid);   

        std::size_t rows = Umid_real.size();
        if (dom.state.Umid.rows != rows || dom.state.Umid.cols <= it) {
            std::size_t newCols = std::max<std::size_t>(dom.state.Umid.cols, it + 1);
            if (newCols == 0) newCols = it + 1;
            Matrix tmp(rows, newCols, 0.0);
            for (std::size_t r = 0; r < dom.state.Umid.rows; ++r)
                for (std::size_t c = 0; c < dom.state.Umid.cols; ++c)
                    tmp(r,c) = dom.state.Umid(r,c);
            dom.state.Umid = std::move(tmp);
        }

        for (std::size_t r = 0; r < rows; ++r)
            dom.state.Umid(r, it) = Umid_real[r];
    }
}
}  