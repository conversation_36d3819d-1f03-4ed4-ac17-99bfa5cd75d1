#include "get_receiver1d.hpp"
#include "matrix.hpp"
#include <algorithm>
#include <iostream>
#include "Get_Knot_Vector.h"
#include "bspln.h"

namespace wave {
Receiver1D get_receiver1d(const std::vector<Domain1D>& domains,
                          double dt, std::size_t nt, std::size_t rom)
{
    Receiver1D rec = create_receiver1d();
    if (rom >= domains.size()) return rec;

    rec.iom = rom;
    const auto& dom = domains[rom];
    rec.time.resize(nt);
    for (std::size_t it = 0; it < nt; ++it) rec.time[it] = (it + 1) * dt;
    rec.ur.assign(nt, 0.0);

    const int Nx1 = dom.model1d.Nx1;
    const double x_rcv = dom.model1d.xa.back();
    rec.xglobal = rec.xlocal = x_rcv;

    const int k   = dom.model1d.px + 1;

    std::vector<double> t1 = Get_Knot_Vector(Nx1, k);
    for (double& v : t1) v = v * (dom.model1d.xa.back() - dom.model1d.xa.front()) + dom.model1d.xa.front();  

    Matrix b1T(Nx1, 1, 0.0);
    for (int i = 1; i <= Nx1; ++i)
        b1T(i - 1, 0) = bspln(t1, Nx1, i, k, x_rcv);

    rec.b1T = b1T;

    double maxAbs = 0.0;
    for (int r = 0; r < Nx1; ++r) maxAbs = std::max(maxAbs, std::abs(b1T(r, 0)));
    std::cerr << "[DBG] get_receiver1d: iom=" << rom
              << " x_rcv=" << x_rcv
              << " max|b1T|=" << maxAbs << '\n';

    return rec;
}
}  