#include "../include/global_assembly.hpp"
#include "../include/common_types.hpp"

static Matrix kron(const Matrix& A,const Matrix& B){
    Matrix K(A.rows*B.rows, A.cols*B.cols);
    for(Integer i=0;i<A.rows;++i)
        for(Integer j=0;j<A.cols;++j){
            Real aij = A(i,j);
            for(Integer p=0;p<B.rows;++p)
                for(Integer q=0;q<B.cols;++q)
                    K(i*B.rows+p, j*B.cols+q) = aij * B(p,q);
        }
    return K;
}

GlobalSystem assemble_global_matrices(const std::vector<Domain2dA>& OM){
    // compute total size
    Integer total = 0;
    for(const auto& d: OM) total += d.mm11.rows;

    // Guard against excessive memory allocation. A dense matrix of size
    //   total × total  becomes prohibitive very quickly (O(total^2)).
    //   If the resulting element count would exceed ~10 million, we skip
    //   the dense global assembly for now and return empty placeholders.
    //   The calling code only uses the matrix dimensions for logging at
    //   this stage, so this avoids std::length_error while we work on a
    //   sparse implementation.
    const std::size_t max_dense_elements = 10'000'000; // 10 M doubles ≈ 80 MB
    if(static_cast<std::size_t>(total) * static_cast<std::size_t>(total) > max_dense_elements){
        std::cerr << "[assemble_global_matrices] Skipping dense assembly – size = "
                  << total << " → "
                  << (static_cast<std::size_t>(total) * static_cast<std::size_t>(total))
                  << " elements would exceed limit\n";
        return {Matrix(), Matrix()};
    }

    Matrix Mglob(total,total,0.0);
    Matrix Kglob(total,total,0.0);

    Integer offset = 0;
    for(const auto& d: OM){
        Integer n = d.mm11.rows;
        // compute domain 2-D stiffness: kron(Mz,Kx)+kron(Kz,Mx)
        Matrix KxMz = kron(d.Mz11, d.Kx11);
        Matrix KzMx = kron(d.Kz11, d.Mx11);
        Matrix Kdom(KxMz.rows, KxMz.cols);
        for(Integer i=0;i<Kdom.rows;++i){
            for(Integer j=0;j<Kdom.cols;++j){
                Kdom(i,j) = KxMz(i,j) + KzMx(i,j);
            }
        }

        for(Integer i=0;i<n;++i){
            for(Integer j=0;j<n;++j){
                Mglob(offset+i, offset+j) = d.mm11(i,j);
                Kglob(offset+i, offset+j) = Kdom(i,j);
            }
        }
        offset += n;
    }

    return {Mglob, Kglob};
} 