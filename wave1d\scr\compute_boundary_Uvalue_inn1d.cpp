/**
 * @file compute_boundary_Uvalue_inn1d.cpp
 * @brief 优化的域内边界位移计算 - 并行版本
 *
 * 核心算法: Ub1 = invLT11 * U1
 * 功能: 将位移场从标准基函数表示转换为边界基函数表示
 * 优化: OpenMP并行化 + 内存优化 + 向量化计算
 */

#include "compute_boundary_Uvalue_inn1d.hpp"
#include "matrix.hpp"
#include <cstddef>
#include <algorithm>
#include <cmath>

#ifdef _OPENMP
#include <omp.h>
#endif

namespace wave {

void compute_boundary_Uvalue_inn1d(std::vector<Domain1D>& domains)
{
    const int ndomains = static_cast<int>(domains.size());
    if(ndomains == 0) return;

    // OpenMP并行化: 域级并行，每个线程处理不同的域
    #ifdef _OPENMP
    #pragma omp parallel for schedule(static) default(none) shared(domains, ndomains)
    #endif
    for(int idx = 0; idx < ndomains; ++idx)
    {
        auto &dom = domains[static_cast<std::size_t>(idx)];

        // 获取输入数据
        const Matrix &invLT11 = dom.invLT11;
        const std::vector<double> &U1 = dom.state.U1;

        // 数据有效性检查
        if(invLT11.rows == 0 || invLT11.cols == 0 || U1.empty()) {
            dom.state.Ub1.clear();
            dom.state.Ubval_xm_inn = 0.0;
            dom.state.Ubval_xp_inn = 0.0;
            continue;
        }

        // 维度匹配检查
        if(invLT11.cols != U1.size()) {
            dom.state.Ub1.assign(invLT11.rows, 0.0);
            dom.state.Ubval_xm_inn = 0.0;
            dom.state.Ubval_xp_inn = 0.0;
            continue;
        }

        // 优化: 预分配结果向量
        dom.state.Ub1.resize(invLT11.rows);

        // 优化: 高效矩阵-向量乘法 Ub1 = invLT11 * U1
        const size_t rows = invLT11.rows;
        const size_t cols = invLT11.cols;

        for(size_t i = 0; i < rows; ++i) {
            double sum = 0.0;
            // 内循环: 向量化友好的点积计算
            for(size_t j = 0; j < cols; ++j) {
                sum += invLT11(i, j) * U1[j];
            }
            // 内联数值稳定性处理
            dom.state.Ub1[i] = std::isfinite(sum) ? sum : 0.0;
        }

        // 提取边界值
        if(!dom.state.Ub1.empty()) {
            dom.state.Ubval_xm_inn = dom.state.Ub1.front();  // 左边界
            dom.state.Ubval_xp_inn = dom.state.Ub1.back();   // 右边界
        } else {
            dom.state.Ubval_xm_inn = 0.0;
            dom.state.Ubval_xp_inn = 0.0;
        }
    }
}

} // namespace wave