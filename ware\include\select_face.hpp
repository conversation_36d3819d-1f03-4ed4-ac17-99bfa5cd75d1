#pragma once
#include "common_types.hpp"

/*
 * Select boundary vector based on jFace value (1..4 or -1..-4)
 *  1 : mo  (-x)
 *  2 : po  (+x)
 *  3 : om  (-z)
 *  4 : op  (+z)
 *  Negative value means the orientation is reversed (vector elements flipped).
 */

inline Vector select_face(const Vector &mo, const Vector &po, const Vector &om, const Vector &op, int jFace)
{
    const Vector *src = nullptr;
    switch(std::abs(jFace)){
        case 1: src = &mo; break;
        case 2: src = &po; break;
        case 3: src = &om; break;
        case 4: src = &op; break;
        default: return Vector();
    }
    Vector v(*src); // copy
    if(jFace<0){ // reverse orientation
        Vector rev(v.size());
        for(Integer i=0;i<v.size();++i) rev(i)=v[v.size()-1-i];
        return rev;
    }
    return v;
} 