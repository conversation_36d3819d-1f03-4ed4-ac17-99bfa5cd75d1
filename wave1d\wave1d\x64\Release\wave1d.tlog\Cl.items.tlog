D:\111\fin\wave1d\main1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\main1d.obj
D:\111\fin\wave1d\scr\add_source1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\add_source1d.obj
D:\111\fin\wave1d\scr\bspln.cpp;D:\111\fin\wave1d\wave1d\x64\Release\bspln.obj
D:\111\fin\wave1d\scr\check_stability.cpp;D:\111\fin\wave1d\wave1d\x64\Release\check_stability.obj
D:\111\fin\wave1d\scr\compute_boundary_Svalue_inn1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\compute_boundary_Svalue_inn1d.obj
D:\111\fin\wave1d\scr\compute_boundary_Svalue_out1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\compute_boundary_Svalue_out1d.obj
D:\111\fin\wave1d\scr\compute_boundary_Uvalue_inn1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\compute_boundary_Uvalue_inn1d.obj
D:\111\fin\wave1d\scr\compute_boundary_Uvalue_out1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\compute_boundary_Uvalue_out1d.obj
D:\111\fin\wave1d\scr\compute_dt.cpp;D:\111\fin\wave1d\wave1d\x64\Release\compute_dt.obj
D:\111\fin\wave1d\scr\compute_KS1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\compute_KS1d.obj
D:\111\fin\wave1d\scr\compute_KU1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\compute_KU1d.obj
D:\111\fin\wave1d\scr\find_neighbors.cpp;D:\111\fin\wave1d\wave1d\x64\Release\find_neighbors.obj
D:\111\fin\wave1d\scr\gen_DFDMatrices.cpp;D:\111\fin\wave1d\wave1d\x64\Release\gen_DFDMatrices.obj
D:\111\fin\wave1d\scr\get_bandwidth.cpp;D:\111\fin\wave1d\wave1d\x64\Release\get_bandwidth.obj
D:\111\fin\wave1d\scr\get_receiver1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\get_receiver1d.obj
D:\111\fin\wave1d\scr\gllnodes.cpp;D:\111\fin\wave1d\wave1d\x64\Release\gllnodes.obj
D:\111\fin\wave1d\scr\initialize_domain1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\initialize_domain1d.obj
D:\111\fin\wave1d\scr\inner_product.cpp;D:\111\fin\wave1d\wave1d\x64\Release\inner_product.obj
D:\111\fin\wave1d\scr\lgwt.cpp;D:\111\fin\wave1d\wave1d\x64\Release\lgwt.obj
D:\111\fin\wave1d\scr\mass_matrix.cpp;D:\111\fin\wave1d\wave1d\x64\Release\mass_matrix.obj
D:\111\fin\wave1d\scr\mesh_sphere1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\mesh_sphere1d.obj
D:\111\fin\wave1d\scr\save_wavefields1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\save_wavefields1d.obj
D:\111\fin\wave1d\scr\save_waveforms1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\save_waveforms1d.obj
D:\111\fin\wave1d\scr\solve1D.cpp;D:\111\fin\wave1d\wave1d\x64\Release\solve1D.obj
D:\111\fin\wave1d\scr\stiffness_matrix.cpp;D:\111\fin\wave1d\wave1d\x64\Release\stiffness_matrix.obj
D:\111\fin\wave1d\scr\update_wavefields1d.cpp;D:\111\fin\wave1d\wave1d\x64\Release\update_wavefields1d.obj
