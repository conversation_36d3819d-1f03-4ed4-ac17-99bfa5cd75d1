function [OM] = compute_boundary_Uvalue_out1d(OM)
 
ndomains = length(OM);
%%%%%%%%%%%%%%%%%%%%%%%%%%
% communication boundary variables
for iom = 1:ndomains
    alpha_xm = OM(iom).alpha_xm;
    alpha_xp = OM(iom).alpha_xp;

    iNbrm = OM(iom).iNbrm;
    if iNbrm ~=0
        OM(iom).state.Ubval_xm_out = OM(iNbrm).state.Ub1(end);
    end

    iNbrp = OM(iom).iNbrp;
    if iNbrp ~=0
        OM(iom).state.Ubval_xp_out = OM(iNbrp).state.Ub1(1);
    end

    OM(iom).state.Ubval_xm = (1-alpha_xm) * OM(iom).state.Ubval_xm_inn + alpha_xm * OM(iom).state.Ubval_xm_out;
    OM(iom).state.Ubval_xp = (1-alpha_xp) * OM(iom).state.Ubval_xp_inn + alpha_xp * OM(iom).state.Ubval_xp_out;
end
%%%%%%%%%%%%%%%%%%%%%%%%%


end