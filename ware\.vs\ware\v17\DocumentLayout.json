{"Version": 1, "WorkspaceRootPath": "D:\\project\\ware\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\src\\main2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\main2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\src\\gen_DFDMatrices2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\gen_DFDMatrices2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\src\\compute_KU2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\compute_KU2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\try5\\ware\\tests\\test_mesh_generation.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\try5\\ware\\tests\\test_single_timestep.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\try5\\ware\\tests\\test_source_receiver.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\src\\get_receiver2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\get_receiver2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\src\\config.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\config.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\src\\refine_model2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\refine_model2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\src\\pagemtimes.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\pagemtimes.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\src\\eigen_wrapper.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\eigen_wrapper.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\CMakeSettings.json||{10608CD5-279C-4A28-BD5F-BA2CFCE06219}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CMakeSettings.json||{10608CD5-279C-4A28-BD5F-BA2CFCE06219}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\src\\tensorProduct2D.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\tensorProduct2D.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\vector||{3B902123-F8A7-4915-9F01-361F908088D0}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\try5\\ware\\tests\\kuks_single.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\try5\\ware\\tests\\test_jacobian.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\try5\\ware\\src\\main.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|CMake 概述页||{B1CAA5B0-FEB1-4350-8AB9-F895876842F2}"}, {"AbsoluteMoniker": "D:1:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\try5\\ware\\tests\\reference_compare.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:2:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\try5\\ware\\tests\\reference_compare.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "main2dA.cpp", "DocumentMoniker": "D:\\project\\ware\\src\\main2dA.cpp", "RelativeDocumentMoniker": "src\\main2dA.cpp", "ToolTip": "D:\\project\\ware\\src\\main2dA.cpp", "RelativeToolTip": "src\\main2dA.cpp", "ViewState": "AgIAABEAAAAAAAAAAAAqwB0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T08:35:55.455Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "gen_DFDMatrices2dA.cpp", "DocumentMoniker": "D:\\project\\ware\\src\\gen_DFDMatrices2dA.cpp", "RelativeDocumentMoniker": "src\\gen_DFDMatrices2dA.cpp", "ToolTip": "D:\\project\\ware\\src\\gen_DFDMatrices2dA.cpp", "RelativeToolTip": "src\\gen_DFDMatrices2dA.cpp", "ViewState": "AgIAABEAAAAAAAAAAADwvyAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-30T14:17:26.776Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "compute_KU2dA.cpp", "DocumentMoniker": "D:\\project\\ware\\src\\compute_KU2dA.cpp", "RelativeDocumentMoniker": "src\\compute_KU2dA.cpp", "ToolTip": "D:\\project\\ware\\src\\compute_KU2dA.cpp", "RelativeToolTip": "src\\compute_KU2dA.cpp", "ViewState": "AgIAALEAAAAAAAAAAADwv8IAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-30T14:17:20.828Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "CMakeLists.txt", "DocumentMoniker": "D:\\project\\ware\\CMakeLists.txt", "RelativeDocumentMoniker": "CMakeLists.txt", "ToolTip": "D:\\project\\ware\\CMakeLists.txt", "RelativeToolTip": "CMakeLists.txt", "ViewState": "AgIAAAYAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-29T04:23:17.702Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "test_single_timestep.cpp", "DocumentMoniker": "D:\\try5\\ware\\tests\\test_single_timestep.cpp", "RelativeDocumentMoniker": "..\\..\\try5\\ware\\tests\\test_single_timestep.cpp", "ToolTip": "D:\\try5\\ware\\tests\\test_single_timestep.cpp", "RelativeToolTip": "..\\..\\try5\\ware\\tests\\test_single_timestep.cpp", "ViewState": "AgIAAOQAAAAAAAAAAADwv/UAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-30T08:33:22.667Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "test_mesh_generation.cpp", "DocumentMoniker": "D:\\try5\\ware\\tests\\test_mesh_generation.cpp", "RelativeDocumentMoniker": "..\\..\\try5\\ware\\tests\\test_mesh_generation.cpp", "ToolTip": "D:\\try5\\ware\\tests\\test_mesh_generation.cpp", "RelativeToolTip": "..\\..\\try5\\ware\\tests\\test_mesh_generation.cpp", "ViewState": "AgIAAF8AAAAAAAAAAADwv3AAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-30T08:25:09.672Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "test_source_receiver.cpp", "DocumentMoniker": "D:\\try5\\ware\\tests\\test_source_receiver.cpp", "RelativeDocumentMoniker": "..\\..\\try5\\ware\\tests\\test_source_receiver.cpp", "ToolTip": "D:\\try5\\ware\\tests\\test_source_receiver.cpp", "RelativeToolTip": "..\\..\\try5\\ware\\tests\\test_source_receiver.cpp", "ViewState": "AgIAACoAAAAAAAAAAADwvzsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-30T08:49:34.841Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "get_receiver2dA.cpp", "DocumentMoniker": "D:\\project\\ware\\src\\get_receiver2dA.cpp", "RelativeDocumentMoniker": "src\\get_receiver2dA.cpp", "ToolTip": "D:\\project\\ware\\src\\get_receiver2dA.cpp", "RelativeToolTip": "src\\get_receiver2dA.cpp", "ViewState": "AgIAACgAAAAAAAAAAAAAADAAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-30T08:21:27.183Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "config.cpp", "DocumentMoniker": "D:\\project\\ware\\src\\config.cpp", "RelativeDocumentMoniker": "src\\config.cpp", "ToolTip": "D:\\project\\ware\\src\\config.cpp", "RelativeToolTip": "src\\config.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-30T01:02:45.124Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "reference_compare.cpp", "DocumentMoniker": "D:\\try5\\ware\\tests\\reference_compare.cpp", "RelativeDocumentMoniker": "..\\..\\try5\\ware\\tests\\reference_compare.cpp", "ToolTip": "D:\\try5\\ware\\tests\\reference_compare.cpp", "RelativeToolTip": "..\\..\\try5\\ware\\tests\\reference_compare.cpp", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T08:28:04.904Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "refine_model2dA.cpp", "DocumentMoniker": "D:\\project\\ware\\src\\refine_model2dA.cpp", "RelativeDocumentMoniker": "src\\refine_model2dA.cpp", "ToolTip": "D:\\project\\ware\\src\\refine_model2dA.cpp", "RelativeToolTip": "src\\refine_model2dA.cpp", "ViewState": "AgIAALQAAAAAAAAAAAAqwLUAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-30T00:29:19.39Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "eigen_wrapper.cpp", "DocumentMoniker": "D:\\project\\ware\\src\\eigen_wrapper.cpp", "RelativeDocumentMoniker": "src\\eigen_wrapper.cpp", "ToolTip": "D:\\project\\ware\\src\\eigen_wrapper.cpp", "RelativeToolTip": "src\\eigen_wrapper.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAwBMMAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T14:51:57.548Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "pagemtimes.cpp", "DocumentMoniker": "D:\\project\\ware\\src\\pagemtimes.cpp", "RelativeDocumentMoniker": "src\\pagemtimes.cpp", "ToolTip": "D:\\project\\ware\\src\\pagemtimes.cpp", "RelativeToolTip": "src\\pagemtimes.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T16:55:09.91Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "tensorProduct2D.cpp", "DocumentMoniker": "D:\\project\\ware\\src\\tensorProduct2D.cpp", "RelativeDocumentMoniker": "src\\tensorProduct2D.cpp", "ToolTip": "D:\\project\\ware\\src\\tensorProduct2D.cpp", "RelativeToolTip": "src\\tensorProduct2D.cpp", "ViewState": "AgIAAAAAAAAAAAAAAADwvwgAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T12:02:09.488Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "CMakeSettings.json", "DocumentMoniker": "D:\\project\\ware\\CMakeSettings.json", "RelativeDocumentMoniker": "CMakeSettings.json", "ToolTip": "D:\\project\\ware\\CMakeSettings.json", "RelativeToolTip": "CMakeSettings.json", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-29T16:06:07.852Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "reference_compare.cpp", "DocumentMoniker": "D:\\try5\\ware\\tests\\reference_compare.cpp", "RelativeDocumentMoniker": "..\\..\\try5\\ware\\tests\\reference_compare.cpp", "ToolTip": "D:\\try5\\ware\\tests\\reference_compare.cpp", "RelativeToolTip": "..\\..\\try5\\ware\\tests\\reference_compare.cpp", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T08:28:04.904Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "vector", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\vector", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\vector", "ViewState": "AgIAAIQIAAAAAAAAAAAewIcIAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-29T08:42:59.608Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "kuks_single.cpp", "DocumentMoniker": "D:\\try5\\ware\\tests\\kuks_single.cpp", "RelativeDocumentMoniker": "..\\..\\try5\\ware\\tests\\kuks_single.cpp", "ToolTip": "D:\\try5\\ware\\tests\\kuks_single.cpp", "RelativeToolTip": "..\\..\\try5\\ware\\tests\\kuks_single.cpp", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T12:04:21.187Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "test_jacobian.cpp", "DocumentMoniker": "D:\\try5\\ware\\tests\\test_jacobian.cpp", "RelativeDocumentMoniker": "..\\..\\try5\\ware\\tests\\test_jacobian.cpp", "ToolTip": "D:\\try5\\ware\\tests\\test_jacobian.cpp", "RelativeToolTip": "..\\..\\try5\\ware\\tests\\test_jacobian.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T03:58:20.301Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "main.cpp", "DocumentMoniker": "D:\\try5\\ware\\src\\main.cpp", "RelativeDocumentMoniker": "..\\..\\try5\\ware\\src\\main.cpp", "ToolTip": "D:\\try5\\ware\\src\\main.cpp", "RelativeToolTip": "..\\..\\try5\\ware\\src\\main.cpp", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T05:12:19.898Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "CMake 概述页", "DocumentMoniker": "CMake 概述页", "ToolTip": "CMake 概述页", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-29T04:23:18.555Z"}]}]}]}