#include "../include/compute_boundary_Svalue_out2dA.hpp"
#include "../include/select_face.hpp"
#include "../include/pagemtimes.hpp"
#include "../include/rotate_full_stress.hpp"
#include "../include/common_types.hpp"

static inline Matrix mul(const Matrix &A,const Matrix &B){ return pagemtimes(A,B);} 
static inline Matrix tr(const Matrix &A){ return transpose(A);} 

// 移除旧的二维应力旋转函数
// static void apply_rotation(const Matrix &R, Vector &sx, Vector &sz) { ... }

// helper similar to process_face_x/z but for scalar vector and generic matrices
static Vector proc_x(const Matrix &invLz_iom,const Matrix &Dmat,const Matrix &invLzT_src,const Vector &vec)
{
    Integer n = vec.size();
    // 若任何矩阵为空则直接返回 vec，避免崩溃
    if(invLz_iom.rows==0 || Dmat.rows==0 || invLzT_src.rows==0){
        return vec;
    }
    Matrix row(1,n); for(Integer j=0;j<n;++j) row(0,j)=vec[j];
    Matrix tmp = mul(invLzT_src, tr(row));
    Matrix res = mul(invLz_iom, mul(Dmat,tmp));
    Vector out(n); for(Integer i=0;i<n;++i) out[i]=res(i,0); return out;
}

static Vector proc_z(const Matrix &invLx_iom,const Matrix &Dmat,const Matrix &invLxT_src,const Vector &vec)
{
    Integer n = vec.size();
    if(invLx_iom.rows==0 || Dmat.rows==0 || invLxT_src.rows==0){
        return vec;
    }
    Matrix col(n,1); for(Integer i=0;i<n;++i) col(i,0)=vec[i];
    Matrix res = mul(invLx_iom, mul(Dmat, mul(invLxT_src,col)));
    Vector out(n); for(Integer i=0;i<n;++i) out[i]=res(i,0); return out;
}

std::vector<Domain2dA> compute_boundary_Svalue_out2dA(std::vector<Domain2dA>& OM)
{
    for(Integer iom=0;iom<static_cast<Integer>(OM.size()); ++iom){
        Domain2dA &d = OM[iom];
        const Matrix &invLx11_i = d.invLx11; const Matrix &invLx22_i = d.invLx22;
        const Matrix &invLz11_i = d.invLz11; const Matrix &invLz22_i = d.invLz22;

        // Helper for shear: simply copy neighbour innr shear vectors (placeholder – no filtering)
        auto shear_from_neighbor=[&](Integer nbrIdx,int jFace,Vector &out12,Vector &out21){
            if(nbrIdx<0) return;
            const Domain2dA &nb = OM[nbrIdx];
            Vector in12mo=nb.state.Sxz12mo_innr, in12po=nb.state.Sxz12po_innr,
                   in12om=nb.state.Sxz12om_innr, in12op=nb.state.Sxz12op_innr;
            Vector in21mo=nb.state.Sxz21mo_innr, in21po=nb.state.Sxz21po_innr,
                   in21om=nb.state.Sxz21om_innr, in21op=nb.state.Sxz21op_innr;
            out12 = select_face(in12mo,in12po,in12om,in12op,jFace);
            out21 = select_face(in21mo,in21po,in21om,in21op,jFace);
        };

        auto handle=[&](int jFace,Integer nbrIdx,
                        const Matrix &Dmat_x,const Matrix &Dmat_z,bool xdir,
                        Vector &out11,Vector &out22){
            if(nbrIdx<0) return;
            const Domain2dA &nb = OM[nbrIdx];
            Vector in11mo=nb.state.Sxx11mo_innr, in11po=nb.state.Sxx11po_innr,
                   in11om=nb.state.Sxx11om_innr, in11op=nb.state.Sxx11op_innr;
            Vector in22mo=nb.state.Sxx22mo_innr, in22po=nb.state.Sxx22po_innr,
                   in22om=nb.state.Sxx22om_innr, in22op=nb.state.Sxx22op_innr;
            Vector vec11 = select_face(in11mo,in11po,in11om,in11op,jFace);
            Vector vec22 = select_face(in22mo,in22po,in22om,in22op,jFace);
            if(xdir){
                const Matrix &invLzT11_src=nb.invLzT11; const Matrix &invLzT22_src=nb.invLzT22;
                out11 = proc_x(invLz11_i,Dmat_x,invLzT11_src,vec11);
                out22 = proc_x(invLz22_i,Dmat_z,invLzT22_src,vec22);
            }else{
                const Matrix &invLxT11_src=nb.invLxT11; const Matrix &invLxT22_src=nb.invLxT22;
                out11 = proc_z(invLx11_i,Dmat_x,invLxT11_src,vec11);
                out22 = proc_z(invLx22_i,Dmat_z,invLxT22_src,vec22);
            }
        };

        // mo
        handle(d.iFace_mo,d.iNbr_mo,
               d.Dzz110mo,d.Dzz220mo,true,
               d.state.Sxx11mo_out,d.state.Sxx22mo_out);
        
        // 使用新的完整应力旋转函数
        shear_from_neighbor(d.iNbr_mo,d.iFace_mo,
                           d.state.Sxz12mo_out, d.state.Sxz21mo_out);
        rotate_full_stress(d.state.Sxx11mo_out, d.state.Sxx22mo_out, d.state.Sxz12mo_out, d.rot_mo);
        
        // po
        handle(d.iFace_po,d.iNbr_po,
               d.Dzz110po,d.Dzz220po,true,
               d.state.Sxx11po_out,d.state.Sxx22po_out);
        
        shear_from_neighbor(d.iNbr_po,d.iFace_po,
                           d.state.Sxz12po_out, d.state.Sxz21po_out);
        rotate_full_stress(d.state.Sxx11po_out, d.state.Sxx22po_out, d.state.Sxz12po_out, d.rot_po);
        
        // om
        handle(d.iFace_om,d.iNbr_om,
               d.Dxx110om,d.Dxx220om,false,
               d.state.Szz11om_out,d.state.Szz22om_out);
        
        shear_from_neighbor(d.iNbr_om,d.iFace_om,
                           d.state.Sxz12om_out, d.state.Sxz21om_out);
        rotate_full_stress(d.state.Szz11om_out, d.state.Szz22om_out, d.state.Sxz12om_out, d.rot_om);
        
        // op
        handle(d.iFace_op,d.iNbr_op,
               d.Dxx110op,d.Dxx220op,false,
               d.state.Szz11op_out,d.state.Szz22op_out);
        
        shear_from_neighbor(d.iNbr_op,d.iFace_op,
                           d.state.Sxz12op_out, d.state.Sxz21op_out);
        rotate_full_stress(d.state.Szz11op_out, d.state.Szz22op_out, d.state.Sxz12op_out, d.rot_op);
    }
    // alpha blend similar to U
    for(Integer iom=0;iom<static_cast<Integer>(OM.size()); ++iom){
        Domain2dA &d=OM[iom]; auto blend=[&](const Vector &inn,const Vector &out,Real a){Vector v(inn.size());for(Integer i=0;i<inn.size();++i) v[i]=(1-a)*inn[i]+a*out[i];return v;};
        // shear blend
        auto blendShear=[&](const Vector &inn,const Vector &out,Real a){Vector v(inn.size());for(Integer i=0;i<inn.size();++i) v[i]=(1-a)*inn[i]+a*out[i];return v;};
        d.state.Sxx11mo = blend(d.state.Sxx11mo_inn,d.state.Sxx11mo_out,d.alpha_mo);
        d.state.Sxx22mo = blend(d.state.Sxx22mo_inn,d.state.Sxx22mo_out,d.alpha_mo);
        d.state.Sxx11po = blend(d.state.Sxx11po_inn,d.state.Sxx11po_out,d.alpha_po);
        d.state.Sxx22po = blend(d.state.Sxx22po_inn,d.state.Sxx22po_out,d.alpha_po);
        d.state.Szz11om = blend(d.state.Szz11om_inn,d.state.Szz11om_out,d.alpha_om);
        d.state.Szz22om = blend(d.state.Szz22om_inn,d.state.Szz22om_out,d.alpha_om);
        d.state.Szz11op = blend(d.state.Szz11op_inn,d.state.Szz11op_out,d.alpha_op);
        d.state.Szz22op = blend(d.state.Szz22op_inn,d.state.Szz22op_out,d.alpha_op);

        // shear components
        d.state.Sxz12mo = blendShear(d.state.Sxz12mo_inn,d.state.Sxz12mo_out,d.alpha_mo);
        d.state.Sxz21mo = blendShear(d.state.Sxz21mo_inn,d.state.Sxz21mo_out,d.alpha_mo);
        d.state.Sxz12po = blendShear(d.state.Sxz12po_inn,d.state.Sxz12po_out,d.alpha_po);
        d.state.Sxz21po = blendShear(d.state.Sxz21po_inn,d.state.Sxz21po_out,d.alpha_po);
        d.state.Sxz12om = blendShear(d.state.Sxz12om_inn,d.state.Sxz12om_out,d.alpha_om);
        d.state.Sxz21om = blendShear(d.state.Sxz21om_inn,d.state.Sxz21om_out,d.alpha_om);
        d.state.Sxz12op = blendShear(d.state.Sxz12op_inn,d.state.Sxz12op_out,d.alpha_op);
        d.state.Sxz21op = blendShear(d.state.Sxz21op_inn,d.state.Sxz21op_out,d.alpha_op);
    }
    return OM;
} 