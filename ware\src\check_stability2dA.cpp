#include "../include/check_stability2dA.hpp"
#include "../include/common_types.hpp"
#include <iostream>
#include <cmath>
#include <limits>
#include <iomanip> // 用于设置输出精度

void check_stability2dA(const std::vector<Domain2dA>& OM, Integer it, Real dt, Integer log_interval)
{
    Real maxU = -std::numeric_limits<Real>::infinity();
    Real maxS = -std::numeric_limits<Real>::infinity();
    Integer maxU_domain = -1;
    Integer maxU_i = -1;
    Integer maxU_j = -1;
    Integer maxS_domain = -1;
    Integer maxS_i = -1;
    Integer maxS_j = -1;
    bool found_nan_or_inf = false;
    Integer first_bad_domain = -1;
    std::string first_bad_field;
    Integer first_bad_i = -1;
    Integer first_bad_j = -1;

    Real maxVS = 0.0; // shear wave speed
    Real min_h  = std::numeric_limits<Real>::infinity(); // smallest grid spacing

    for(Integer dom_idx = 0; dom_idx < static_cast<Integer>(OM.size()); ++dom_idx) {
        const auto &dom = OM[dom_idx];
        const Matrix &U12 = dom.state.U12;
        const Matrix &U21 = dom.state.U21;
        const Matrix &Sxx = dom.state.Sxx11;
        const Matrix &Szz = dom.state.Szz11;

        // 检查矩阵维度
        if (it % log_interval == 0 && dom_idx == 0) {
            std::cout << "[stability] Domain " << dom_idx 
                      << " matrix dims - U12: " << U12.rows << "x" << U12.cols
                      << ", U21: " << U21.rows << "x" << U21.cols
                      << ", Sxx: " << Sxx.rows << "x" << Sxx.cols
                      << ", Szz: " << Szz.rows << "x" << Szz.cols << "\n";
        }

        // 检查是否有NaN或Inf
        for(Integer i=0; i<U12.rows; ++i) {
            for(Integer j=0; j<U12.cols; ++j) {
                // 检查U12
                if(!std::isfinite(U12(i,j)) && !found_nan_or_inf) {
                    found_nan_or_inf = true;
                    first_bad_domain = dom_idx;
                    first_bad_field = "U12";
                    first_bad_i = i;
                    first_bad_j = j;
                }
                
                Real u = std::max(std::fabs(U12(i,j)), std::fabs(U21(i,j)));
                if(u > maxU) {
                    maxU = u;
                    maxU_domain = dom_idx;
                    maxU_i = i;
                    maxU_j = j;
                }
                
                // 检查U21
                if(!std::isfinite(U21(i,j)) && !found_nan_or_inf) {
                    found_nan_or_inf = true;
                    first_bad_domain = dom_idx;
                    first_bad_field = "U21";
                    first_bad_i = i;
                    first_bad_j = j;
                }
            }
        }
        
        for(Integer i=0; i<Sxx.rows; ++i) {
            for(Integer j=0; j<Sxx.cols; ++j) {
                // 检查Sxx
                if(!std::isfinite(Sxx(i,j)) && !found_nan_or_inf) {
                    found_nan_or_inf = true;
                    first_bad_domain = dom_idx;
                    first_bad_field = "Sxx";
                    first_bad_i = i;
                    first_bad_j = j;
                }
                
                // 检查Szz
                if(!std::isfinite(Szz(i,j)) && !found_nan_or_inf) {
                    found_nan_or_inf = true;
                    first_bad_domain = dom_idx;
                    first_bad_field = "Szz";
                    first_bad_i = i;
                    first_bad_j = j;
                }
                
                Real s = std::max(std::fabs(Sxx(i,j)), std::fabs(Szz(i,j)));
                if(s > maxS) {
                    maxS = s;
                    maxS_domain = dom_idx;
                    maxS_i = i;
                    maxS_j = j;
                }
            }
        }

        // compute shear wave speed vs = sqrt(mu/rho)
        if(dom.rho > 0) {
            Real vs = std::sqrt(dom.mu / dom.rho);
            if(vs > maxVS) maxVS = vs;
        }

        // estimate grid spacing from bounding box
        if(dom.Nx1 > 1 && dom.x_max > dom.x_min) {
            Real dx = (dom.x_max - dom.x_min)/(dom.Nx1-1);
            if(dx < min_h) min_h = dx;
        }
        if(dom.Nz1 > 1 && dom.z_max > dom.z_min) {
            Real dz = (dom.z_max - dom.z_min)/(dom.Nz1-1);
            if(dz < min_h) min_h = dz;
        }
    }

    Real CFL = (min_h > 0) ? maxVS * dt / min_h : 0.0;

    if(it % log_interval == 0) {
        std::cout << "[stability] it=" << it 
                  << " max|U|=" << std::scientific << std::setprecision(6) << maxU 
                  << " at domain=" << maxU_domain << " (i=" << maxU_i << ",j=" << maxU_j << ")"
                  << " max|S|=" << std::scientific << std::setprecision(6) << maxS
                  << " at domain=" << maxS_domain << " (i=" << maxS_i << ",j=" << maxS_j << ")"
                  << " CFL=" << std::fixed << std::setprecision(4) << CFL << "\n";
    }

    if(found_nan_or_inf) {
        std::cerr << "[stability] ERROR: NaN/Inf detected at it=" << it
                  << " in field " << first_bad_field
                  << " at domain=" << first_bad_domain
                  << " (i=" << first_bad_i << ",j=" << first_bad_j << ")" << "\n";
        throw std::runtime_error("Numerical instability: NaN/Inf");
    }
} 