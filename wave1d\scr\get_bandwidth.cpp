#include "get_bandwidth.hpp"
#include <cmath>

namespace wave {
std::pair<int,int> get_bandwidth(const Matrix& mat, double tol)
{
    int p = 0;
    int q = 0;
    for(std::size_t jc = 0; jc < mat.cols; ++jc){
        for(std::size_t ir = 0; ir < mat.rows; ++ir){
            double v = mat(ir, jc);
            if(std::fabs(v) > tol){
                int pdiff = static_cast<int>(ir) - static_cast<int>(jc);
                int qdiff = static_cast<int>(jc) - static_cast<int>(ir);
                if(pdiff > p) p = pdiff;
                if(qdiff > q) q = qdiff;
            }
        }
    }
    return {p, q};
}
}  