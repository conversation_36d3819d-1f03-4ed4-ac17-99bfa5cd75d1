#include "../include/compute_boundary_Uvalue_out2dA.hpp"
#include "../include/select_face.hpp"
#include "../include/pagemtimes.hpp"
#include "../include/common_types.hpp"
#include <utility>

static inline Matrix mul_local(const Matrix &A,const Matrix &B){ return pagemtimes(A,B);} 
static inline Matrix transpose_m(const Matrix &A){ return transpose(A);} 

// apply 2×2 rotation matrix to component vectors (in-place)
static void apply_rotation(const Matrix &R, Vector &comp1, Vector &comp2)
{
    if(R.rows!=2 || R.cols!=2) return; // identity fallback
    Integer n = comp1.size();
    Vector c1 = comp1, c2 = comp2;
    Real r00 = R(0,0), r01 = R(0,1), r10 = R(1,0), r11 = R(1,1);
    for(Integer i=0;i<n;++i){
        comp1[i] = r00 * c1[i] + r01 * c2[i];
        comp2[i] = r10 * c1[i] + r11 * c2[i];
    }
}

// helper to perform L * D * (Lsrc * vec(:)) kind of operation, returns Vector (row or col accordingly)
static Vector process_face_x(const Matrix &invLz_iom, const Matrix &Dmat, const Matrix &invLzT_src, const Vector &vec)
{
    // vec is row vector, length n
    Integer n = vec.size();
    Matrix tmpRow(1,n);
    for(Integer j=0;j<n;++j) tmpRow(0,j)=vec[j];
    Matrix tmp = mul_local(invLzT_src, transpose_m(tmpRow)); // column (n x 1)
    Matrix res = mul_local(invLz_iom, mul_local(Dmat, tmp)); // (n x 1)
    Vector out(n);
    for(Integer i=0;i<n;++i) out[i]=res(i,0);
    return out;
}
static Vector process_face_z(const Matrix &invLx_iom, const Matrix &Dmat, const Matrix &invLxT_src, const Vector &vec)
{
    // vec is column vector length n
    Integer n = vec.size();
    Matrix tmpCol(n,1);
    for(Integer i=0;i<n;++i) tmpCol(i,0)=vec[i];
    Matrix res = mul_local(invLx_iom, mul_local(Dmat, mul_local(invLxT_src, tmpCol)));
    Vector out(n);
    for(Integer i=0;i<n;++i) out[i]=res(i,0);
    return out;
}

std::vector<Domain2dA> compute_boundary_Uvalue_out2dA(std::vector<Domain2dA>& OM)
{
    // First pass: compute *_out using neighbour *_inn
    for(Integer iom=0;iom<static_cast<Integer>(OM.size()); ++iom){
        Domain2dA &dom = OM[iom];
        // shortcuts to invL matrices of current domain
        const Matrix &invLx11_i = dom.invLx11; const Matrix &invLx22_i = dom.invLx22;
        const Matrix &invLz11_i = dom.invLz11; const Matrix &invLz22_i = dom.invLz22;

        auto handle_dir=[&](int jFaceDir, Integer nbrIdx,
                            const Matrix &Dmat_x, const Matrix &Dmat_z,
                            bool dir_is_x) -> std::pair<Vector,Vector>{
            if(nbrIdx<0) return std::pair<Vector,Vector>{Vector(),Vector()};
            const Domain2dA &nbr = OM[nbrIdx];
            Vector U12mo_i = nbr.state.U12mo_inn;
            Vector U12po_i = nbr.state.U12po_inn;
            Vector U12om_i = nbr.state.U12om_inn;
            Vector U12op_i = nbr.state.U12op_inn;
            Vector U21mo_i = nbr.state.U21mo_inn;
            Vector U21po_i = nbr.state.U21po_inn;
            Vector U21om_i = nbr.state.U21om_inn;
            Vector U21op_i = nbr.state.U21op_inn;

            Vector vec12 = select_face(U12mo_i,U12po_i,U12om_i,U12op_i,jFaceDir);
            Vector vec21 = select_face(U21mo_i,U21po_i,U21om_i,U21op_i,jFaceDir);

            Vector out12, out21;
            if(dir_is_x){
                // use z-direction L and Dzz*/Dzx* depending on |jFace|
                const Matrix &invLzT11_src = nbr.invLzT11;
                const Matrix &invLzT22_src = nbr.invLzT22;
                out12 = process_face_x(invLz22_i, Dmat_x, invLzT22_src, vec12);
                out21 = process_face_x(invLz11_i, Dmat_z, invLzT11_src, vec21);
            }else{
                // use x-direction L
                const Matrix &invLxT11_src = nbr.invLxT11;
                const Matrix &invLxT22_src = nbr.invLxT22;
                out12 = process_face_z(invLx11_i, Dmat_x, invLxT11_src, vec12);
                out21 = process_face_z(invLx22_i, Dmat_z, invLxT22_src, vec21);
            }
            return std::make_pair(out12,out21);
        };

        // mo face
        if(dom.iNbr_mo>=0){
            int jFace = dom.iFace_mo;
            bool xdir = (std::abs(jFace)==1 || std::abs(jFace)==2);
            auto result = handle_dir(jFace, dom.iNbr_mo,
                                         xdir ? dom.Dzz220mo : dom.Dzx120mo,
                                         xdir ? dom.Dzz110mo : dom.Dzx210mo,
                                         xdir);
            Vector out12 = result.first, out21 = result.second;
            apply_rotation(dom.rot_mo, out12, out21);
            dom.state.U12mo_out = out12; dom.state.U21mo_out = out21;
        }
        // po face
        if(dom.iNbr_po>=0){
            int jFace = dom.iFace_po;
            bool xdir = (std::abs(jFace)==1 || std::abs(jFace)==2);
            auto result = handle_dir(jFace, dom.iNbr_po,
                                         xdir ? dom.Dzz220po : dom.Dzx210po,
                                         xdir ? dom.Dzz110po : dom.Dzx120po,
                                         xdir);
            Vector out12 = result.first, out21 = result.second;
            apply_rotation(dom.rot_po, out12, out21);
            dom.state.U12po_out = out12; dom.state.U21po_out = out21;
        }
        // om face
        if(dom.iNbr_om>=0){
            int jFace = dom.iFace_om;
            bool xdir = (std::abs(jFace)==3 || std::abs(jFace)==4) ? false : true; // for om/op logic reversed
            auto result = handle_dir(jFace, dom.iNbr_om,
                                         !xdir ? dom.Dxx110om : dom.Dxz120om,
                                         !xdir ? dom.Dxx220om : dom.Dxz210om,
                                         !xdir);
            Vector out12 = result.first, out21 = result.second;
            apply_rotation(dom.rot_om, out12, out21);
            dom.state.U12om_out = out12; dom.state.U21om_out = out21;
        }
        // op face
        if(dom.iNbr_op>=0){
            int jFace = dom.iFace_op;
            bool xdir = (std::abs(jFace)==3 || std::abs(jFace)==4) ? false : true;
            auto result = handle_dir(jFace, dom.iNbr_op,
                                         !xdir ? dom.Dxx110op : dom.Dxz120op,
                                         !xdir ? dom.Dxx220op : dom.Dxz210op,
                                         !xdir);
            Vector out12 = result.first, out21 = result.second;
            apply_rotation(dom.rot_op, out12, out21);
            dom.state.U12op_out = out12; dom.state.U21op_out = out21;
        }
    }

    // Second pass: alpha blending
    for(Integer iom=0;iom<static_cast<Integer>(OM.size()); ++iom){
        Domain2dA &d = OM[iom];
        auto blend=[&](const Vector &inn,const Vector &out, Real alpha){
            Vector v(inn.size()); for(Integer i=0;i<inn.size();++i) v[i]=(1-alpha)*inn[i]+alpha*out[i]; return v; };
        d.state.U12mo = blend(d.state.U12mo_inn, d.state.U12mo_out, d.alpha_mo);
        d.state.U21mo = blend(d.state.U21mo_inn, d.state.U21mo_out, d.alpha_mo);
        d.state.U12po = blend(d.state.U12po_inn, d.state.U12po_out, d.alpha_po);
        d.state.U21po = blend(d.state.U21po_inn, d.state.U21po_out, d.alpha_po);
        d.state.U12om = blend(d.state.U12om_inn, d.state.U12om_out, d.alpha_om);
        d.state.U21om = blend(d.state.U21om_inn, d.state.U21om_out, d.alpha_om);
        d.state.U12op = blend(d.state.U12op_inn, d.state.U12op_out, d.alpha_op);
        d.state.U21op = blend(d.state.U21op_inn, d.state.U21op_out, d.alpha_op);
    }
    return OM;
} 