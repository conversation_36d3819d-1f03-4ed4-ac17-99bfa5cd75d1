#pragma once

#include "common_types.hpp"
#include <vector>
#include <cmath>

/**
 * @file mesh_sphere2dA.hpp
 * @brief Domain structure and mesh generation for 2D acoustic wave propagation
 *
 * This file defines the Domain2dA structure and mesh generation functions
 * corresponding to MATLAB mesh_sphere2dA.m
 */

// Forward declarations
struct BasisStruct;
struct StateStruct;
struct Model2dA;

// New: Source structure
struct SourceStruct {
    Real x{0.0}, z{0.0};   // coordinates
    Real amp{1.0};         // amplitude
    Real f0 {10.0};        // dominant frequency (Hz)
    Vector signal;         // sampled source time function
    Vector Ft;             // Time function vector, corresponding to MATLAB version
    
    // Missing fields from MATLAB implementation
    Integer iom{0};        // domain index where source is located
    Real xlocal{0.5};      // local coordinates within domain (0-1)
    Real zlocal{0.5};      // local coordinates within domain (0-1)
    Matrix invMsg12;       // source interpolation matrix for grid 12
    Matrix invMsg21;       // source interpolation matrix for grid 21
};

/**
 * @brief Model structure containing physical domain properties
 */
struct Model2dA {
    Integer nx = 0;      // Number of grid points in x direction
    Integer nz = 0;      // Number of grid points in z direction
    Matrix xa;           // X coordinates
    Matrix za;           // Z coordinates
    Matrix xp;           // X coordinates for plotting
    Matrix zp;           // Z coordinates for plotting
    Matrix rho;          // Density field
    Matrix mu;           // Shear modulus field

    Model2dA() = default;
};

/**
 * @brief Basis function structure for B-spline discretization
 */
struct BasisStruct {
    Integer nb = 0;      // Number of basis functions
    Integer pb = 0;      // B-spline order
    Real xmin = 0.0;     // Minimum coordinate
    Real xmax = 0.0;     // Maximum coordinate
    Vector x;            // Basis function coordinates
    Vector k;            // Knot vector
    Vector tx;           // Alias for knot vector (used in some functions)
    Vector xps;          // Alias for coordinates (used in some functions)
    Matrix mat;          // Basis function matrix

    BasisStruct() = default;
};

/**
 * @brief State structure containing wave field variables
 * 严格按照MATLAB版本实现的状态结构，移除了C++特有的扩展字段
 */
struct StateStruct {
    // 加速度 - 与MATLAB完全对应
    Matrix dU2dtt12, dU2dtt21;

    // 位移场 - 当前时间步和历史时间步
    Matrix U12, U21;           // 当前时间步
    Matrix U12_0, U21_0;       // 前一时间步
    Matrix U12_1, U21_1;       // 前两时间步

    // 边界位移值 (左右边界)
    Vector U12mo, U12po;  // U12在左右边界的值
    Vector U21mo, U21po;  // U21在左右边界的值

    // 边界位移值 (上下边界)
    Vector U12om, U12op;  // U12在上下边界的值
    Vector U21om, U21op;  // U21在上下边界的值

    // 内部边界位移值 (用于域间连接)
    Vector U12mo_inn, U12po_inn, U12om_inn, U12op_inn;
    Vector U21mo_inn, U21po_inn, U21om_inn, U21op_inn;

    // 外部边界位移值 (用于边界条件)
    Vector U12mo_out, U12po_out, U12om_out, U12op_out;
    Vector U21mo_out, U21po_out, U21om_out, U21op_out;

    // 应力场 - 仅正应力分量，与MATLAB一致
    Matrix Sxx11, Szz11;  // S11 网格上的应力
    Matrix Sxx22, Szz22;  // S22 网格上的应力

    // 应力场边界值 (左右边界)
    Vector Sxx11mo, Sxx11po, Sxx22mo, Sxx22po;

    // 应力场边界值 (上下边界)
    Vector Szz11om, Szz11op, Szz22om, Szz22op;

    // 内部边界应力值 (用于域间连接)
    Vector Sxx11mo_inn, Sxx11po_inn, Sxx22mo_inn, Sxx22po_inn;
    Vector Szz11om_inn, Szz11op_inn, Szz22om_inn, Szz22op_inn;
    Vector Sxx11om_inn, Sxx11op_inn, Sxx22om_inn, Sxx22op_inn;
    Vector Szz11mo_inn, Szz11po_inn, Szz22mo_inn, Szz22po_inn;

    // 外部边界应力值 (用于边界条件)
    Vector Sxx11mo_out, Sxx11po_out, Sxx22mo_out, Sxx22po_out;
    Vector Szz11om_out, Szz11op_out, Szz22om_out, Szz22op_out;
    Vector Sxx11om_out, Sxx11op_out, Sxx22om_out, Sxx22op_out;
    Vector Szz11mo_out, Szz11po_out, Szz22mo_out, Szz22po_out;

    // 剪切应力字段 - 为了兼容现有代码而保留
    Matrix Sxz12, Sxz21;
    Vector Sxz12mo, Sxz12po, Sxz12om, Sxz12op;
    Vector Sxz21mo, Sxz21po, Sxz21om, Sxz21op;
    Vector Sxz12mo_inn, Sxz12po_inn, Sxz12om_inn, Sxz12op_inn;
    Vector Sxz21mo_inn, Sxz21po_inn, Sxz21om_inn, Sxz21op_inn;
    Vector Sxz12mo_out, Sxz12po_out, Sxz12om_out, Sxz12op_out;
    Vector Sxz21mo_out, Sxz21po_out, Sxz21om_out, Sxz21op_out;

    // 旋转应力边界值 - 为了兼容现有代码而保留
    Vector Sxx11mo_innr, Sxx11po_innr, Sxx22mo_innr, Sxx22po_innr;
    Vector Szz11om_innr, Szz11op_innr, Szz22om_innr, Szz22op_innr;
    Vector Sxx11om_innr, Sxx11op_innr, Sxx22om_innr, Sxx22op_innr;
    Vector Szz11mo_innr, Szz11po_innr, Szz22mo_innr, Szz22po_innr;
    Vector Sxz12mo_innr, Sxz12po_innr, Sxz12om_innr, Sxz12op_innr;
    Vector Sxz21mo_innr, Sxz21po_innr, Sxz21om_innr, Sxz21op_innr;

    // 波场存储 - 3D矩阵，用于存储不同时间步的波场
    std::vector<Matrix> Umid;

    StateStruct() = default;
};

/**
 * @brief Main domain structure for 2D acoustic wave propagation
 */
struct Domain2dA {
    // Model and state
    Model2dA model2dA;
    StateStruct state;

    // Basis functions
    BasisStruct Bx1, Bx2, Bz1, Bz2;

    // B-spline parameters
    Integer px1 = 5, pz1 = 5;  // B-spline orders
    Integer Nx1 = 0, Nz1 = 0;  // Number of basis functions
    Integer Nx2 = 0, Nz2 = 0;  // Number of basis functions (second set)

    // Coordinate grids
    Matrix x2d11, z2d11;
    Matrix x2d22, z2d22;

    // Jacobian matrices
    Matrix Jac11, Jac12, Jac21, Jac22;

    // Basis transformation matrices
    Matrix bxT1, bxT2, bzT1, bzT2;

    // DFD matrices - 严格按照MATLAB版本
    Matrix kkx12, kkz12, kkx21, kkz21;

    // 逆变换矩阵 - 用于基函数转换
    Matrix invLx11, invLz11, invLx22, invLz22;
    Matrix invLxT11, invLzT11, invLxT22, invLzT22;

    // Additional DFD matrices for boundary computations
    Matrix Dzz220mo, Dzz110mo, Dzx210mo, Dzx120mo;
    Matrix Dzz220po, Dzz110po, Dzx210po, Dzx120po;
    Matrix Dxx110om, Dxx220om, Dxz120om, Dxz210om;
    Matrix Dxx110op, Dxx220op, Dxz120op, Dxz210op;

    // Additional DFD matrices (more combinations)
    Matrix Dzz210mo, Dzz120mo, Dzx110mo, Dzx220mo;
    Matrix Dzz210po, Dzz120po, Dzx110po, Dzx220po;
    Matrix Dxx210om, Dxx120om, Dxz110om, Dxz220om;
    Matrix Dxx210op, Dxx120op, Dxz110op, Dxz220op;

    // Mass and stiffness matrices
    Matrix Mx11, Mx12, Mx21, Mx22;
    Matrix Mz11, Mz12, Mz21, Mz22;
    Matrix Kx11, Kx12, Kx21, Kx22;
    Matrix Kz11, Kz12, Kz21, Kz22;

    // Coupling parameters
    Real alpha_mo = 0.5, alpha_po = 0.5, alpha_om = 0.5, alpha_op = 0.5;

    // Neighbor domain indices
    Integer iNbr_mo = -1, iNbr_po = -1, iNbr_om = -1, iNbr_op = -1;

    // Face connection types
    Integer iFace_mo = 0, iFace_po = 0, iFace_om = 0, iFace_op = 0;

    // Rotation matrices for coordinate transformation
    Matrix rot_mo, rot_po, rot_om, rot_op;

    // Material properties
    Real rho = 2000.0;           // Density
    Real mu = 15125000000.0;     // Shear modulus (rho * vp^2)
    Real lambda = 15125000000.0; // Lame parameter

    // Material property matrices
    Matrix rho12, rho21;         // Density matrices for different grid combinations
    Matrix mu11, mu22;           // Shear modulus matrices

    // Coordinate transformation matrices
    Matrix dxpdx11, dxpdx22;     // dx_physical/dx_computational
    Matrix dzpdx11, dzpdx22;     // dz_physical/dx_computational
    Matrix dxpdz11, dxpdz22;     // dx_physical/dz_computational
    Matrix dzpdz11, dzpdz22;     // dz_physical/dz_computational

    // Additional coordinate transformation matrices
    Matrix dxpdx12, dxpdx21;
    Matrix dzpdx12, dzpdx21;
    Matrix dxpdz12, dxpdz21;
    Matrix dzpdz12, dzpdz21;

    // Domain boundaries
    Real x_min = 0.0, x_max = 0.0, z_min = 0.0, z_max = 0.0;

    // Boundary structure for compatibility
    struct BoundaryStruct {
        Vector U21_mo, U21_po, U12_om, U12_op;
        Vector V21_mo, V21_po, V12_om, V12_op;
        Vector Sxx11_mo, Sxx11_po, Szz11_om, Szz11_op;
        Vector Sxz12_mo, Sxz12_po, Sxz21_om, Sxz21_op;
    } boundary;

    // Domain geometry parameters
    Integer region = 0;          // Region type
    Integer iom = 0;             // Domain index
    Real r1 = 0.0, r2 = 0.0;     // Radial boundaries
    Real theta1 = 0.0, theta2 = 0.0; // Angular boundaries
    Real inflat = 0.44;          // Inflation factor for central square

    // Default constructor
    Domain2dA() {
        // Initialize rotation matrices as identity
        rot_mo = create_identity(2);
        rot_po = create_identity(2);
        rot_om = create_identity(2);
        rot_op = create_identity(2);
    }

    Matrix mm11;  // full 2-D mass matrix (Mz1 ⊗ Mx1)
    Matrix mm22;  // full 2-D mass matrix (Mz2 ⊗ Mx2)

    // Sources attached to this domain
    std::vector<SourceStruct> sources;
};

/**
 * @brief Generate mesh for 2D acoustic wave propagation
 *
 * MATLAB equivalent:
 * function OM = mesh_sphere2dA(rad,dx,nparts,freq,ppw)
 *
 * Generates a multi-domain mesh for acoustic wave propagation
 * with spherical and rectangular regions
 *
 * @param rad Radial boundaries [inner_radius, outer_radius]
 * @param dx Grid spacing
 * @param nparts Number of angular partitions
 * @param freq Frequency for wavelength-based refinement
 * @param ppw Points per wavelength
 * @return Vector of Domain2dA structures
 */
std::vector<Domain2dA> mesh_sphere2dA(const Vector& rad,Real dx,Integer nparts,Real freq,Real ppw); 