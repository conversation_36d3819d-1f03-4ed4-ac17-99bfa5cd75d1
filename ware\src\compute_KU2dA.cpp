/**
 * @file compute_KU2dA.cpp
 * @brief 严格按照MATLAB compute_KU2dA.m实现 - 从位移场计算应力场
 *
 * MATLAB原始代码逻辑：
 * 1. 计算空间导数：dUdxp11, dUdzp11, dUdxp22, dUdzp22
 * 2. 转换到物理坐标：dUdx11, dUdz11, dUdx22, dUdz22
 * 3. 计算应力：txx = mu.*dUdx, tzz = mu.*dUdz
 * 4. 转换到计算坐标：Sxx = (txx.*dxpdx + tzz.*dzpdz).*Jac
 *                    Szz = (txx.*dzpdx + tzz.*dzpdz).*Jac
 *
 * 注意：原始MATLAB代码只计算正应力分量(Sxx, Szz)，不计算剪切应力分量
 */

#include "../include/compute_KU2dA.hpp"
#include "../include/pagemtimes.hpp"
#include "../include/common_types.hpp"
#include "../include/eigen_wrapper.hpp"
#include <omp.h>

// 本地辅助函数 - 避免命名冲突
static inline Matrix matmul_local(const Matrix &A, const Matrix &B) {
    return pagemtimes(A, B);
}

static inline Matrix permute_2d_local(const Matrix &A) {
    return A.transpose();
}

/**
 * @brief x方向导数计算 - 严格对应MATLAB的dFdxp函数
 *
 * MATLAB代码：
 * function dUdxp11 = dFdxp(kkx12,U21,invLx22T,invLx11,U21mo,U21po)
 *     Ub21    = pagemtimes(invLx22T,U21);
 *     dUdxp11 = pagemtimes(-kkx12,Ub21);
 *     dUdxp11(1,:)   = dUdxp11(1,:)   - reshape(U21mo,1,length(U21mo));
 *     dUdxp11(end,:) = dUdxp11(end,:) + reshape(U21po,1,length(U21po));
 *     dUdxp11 = pagemtimes(invLx11,dUdxp11);
 * end
 */
static Matrix dFdxp(const Matrix& kkx12, const Matrix& U21,
                    const Matrix& invLx22T, const Matrix& invLx11,
                    const Vector& U21mo, const Vector& U21po) {
    // 步骤1: Ub21 = pagemtimes(invLx22T, U21)
    Matrix Ub21 = matmul_local(invLx22T, U21);

    // 步骤2: dUdxp11 = pagemtimes(-kkx12, Ub21)
    Matrix negKkx12 = kkx12 * (-1.0);
    Matrix dUdxp11 = matmul_local(negKkx12, Ub21);

    // 步骤3: 边界条件处理
    // dUdxp11(1,:) = dUdxp11(1,:) - reshape(U21mo,1,length(U21mo))
    if (dUdxp11.rows > 0) {
        for (Integer j = 0; j < dUdxp11.cols && j < U21mo.size(); ++j) {
            dUdxp11(0, j) -= U21mo[j];  // MATLAB索引1对应C++索引0
        }
    }

    // dUdxp11(end,:) = dUdxp11(end,:) + reshape(U21po,1,length(U21po))
    if (dUdxp11.rows > 0) {
        Integer lastRow = dUdxp11.rows - 1;
        for (Integer j = 0; j < dUdxp11.cols && j < U21po.size(); ++j) {
            dUdxp11(lastRow, j) += U21po[j];
        }
    }

    // 步骤4: dUdxp11 = pagemtimes(invLx11, dUdxp11)
    return matmul_local(invLx11, dUdxp11);
}

/**
 * @brief z方向导数计算 - 严格对应MATLAB的dFdzp函数
 *
 * MATLAB代码：
 * function dUdzp11 = dFdzp(kkz12,U12,invLzT22,invLz11,U12om,U12op)
 *     U12     = permute(U12,[2,1]);
 *     Ub12    = pagemtimes(invLzT22,U12);
 *     dUdzp11 = pagemtimes(-kkz12,Ub12);
 *     dUdzp11 = permute(dUdzp11,[2,1]);
 *     dUdzp11(:,1)   = dUdzp11(:,1)   - reshape(U12om,length(U12om),1);
 *     dUdzp11(:,end) = dUdzp11(:,end) + reshape(U12op,length(U12op),1);
 *     dUdzp11 = permute(dUdzp11,[2,1]);
 *     dUdzp11 = pagemtimes(invLz11,dUdzp11);
 *     dUdzp11 = permute(dUdzp11,[2,1]);
 * end
 */
static Matrix dFdzp(const Matrix& kkz12, const Matrix& U12,
                    const Matrix& invLzT22, const Matrix& invLz11,
                    const Vector& U12om, const Vector& U12op) {
    // 步骤1: U12 = permute(U12,[2,1])
    Matrix U12_perm = permute_2d_local(U12);

    // 步骤2: Ub12 = pagemtimes(invLzT22, U12)
    Matrix Ub12 = matmul_local(invLzT22, U12_perm);

    // 步骤3: dUdzp11 = pagemtimes(-kkz12, Ub12)
    Matrix negKkz12 = kkz12 * (-1.0);
    Matrix dUdzp11 = matmul_local(negKkz12, Ub12);

    // 步骤4: dUdzp11 = permute(dUdzp11,[2,1])
    dUdzp11 = permute_2d_local(dUdzp11);

    // 步骤5: 边界条件处理
    // dUdzp11(:,1) = dUdzp11(:,1) - reshape(U12om,length(U12om),1)
    if (dUdzp11.cols > 0) {
        for (Integer i = 0; i < dUdzp11.rows && i < U12om.size(); ++i) {
            dUdzp11(i, 0) -= U12om[i];
        }
    }

    // dUdzp11(:,end) = dUdzp11(:,end) + reshape(U12op,length(U12op),1)
    if (dUdzp11.cols > 0) {
        Integer lastCol = dUdzp11.cols - 1;
        for (Integer i = 0; i < dUdzp11.rows && i < U12op.size(); ++i) {
            dUdzp11(i, lastCol) += U12op[i];
        }
    }

    // 步骤6: dUdzp11 = permute(dUdzp11,[2,1])
    dUdzp11 = permute_2d_local(dUdzp11);

    // 步骤7: dUdzp11 = pagemtimes(invLz11, dUdzp11)
    dUdzp11 = matmul_local(invLz11, dUdzp11);

    // 步骤8: dUdzp11 = permute(dUdzp11,[2,1])
    return permute_2d_local(dUdzp11);
}

/**
 * @brief 主函数 - 严格按照MATLAB compute_KU2dA.m实现
 *
 * MATLAB原始逻辑：
 * 1. 计算空间导数 dUdxp, dUdzp
 * 2. 转换到物理坐标 dUdx, dUdz
 * 3. 计算应力 txx = mu.*dUdx, tzz = mu.*dUdz
 * 4. 转换到计算坐标 Sxx, Szz
 *
 * 注意：原始MATLAB代码只计算正应力分量，不计算剪切应力
 */
std::vector<Domain2dA> compute_KU2dA(std::vector<Domain2dA>& OM)
{
    for(Integer iom = 0; iom < static_cast<Integer>(OM.size()); ++iom) {
        Domain2dA &dom = OM[iom];

        // 提取位移场
        const Matrix &U12 = dom.state.U12;
        const Matrix &U21 = dom.state.U21;

        // 提取边界条件向量
        const Vector &U12mo = dom.state.U12mo;
        const Vector &U21mo = dom.state.U21mo;
        const Vector &U12po = dom.state.U12po;
        const Vector &U21po = dom.state.U21po;
        const Vector &U12om = dom.state.U12om;
        const Vector &U21om = dom.state.U21om;
        const Vector &U12op = dom.state.U12op;
        const Vector &U21op = dom.state.U21op;

        // 提取差分矩阵
        const Matrix &kkx12 = dom.kkx12;
        const Matrix &kkz12 = dom.kkz12;
        const Matrix &kkx21 = dom.kkx21;
        const Matrix &kkz21 = dom.kkz21;

        // 提取逆变换矩阵
        const Matrix &invLx11 = dom.invLx11;
        const Matrix &invLz11 = dom.invLz11;
        const Matrix &invLx22 = dom.invLx22;
        const Matrix &invLz22 = dom.invLz22;
        const Matrix &invLxT11 = dom.invLxT11;
        const Matrix &invLzT11 = dom.invLzT11;
        const Matrix &invLxT22 = dom.invLxT22;
        const Matrix &invLzT22 = dom.invLzT22;

        // 提取坐标变换导数和Jacobian
        const Matrix &dxpdx11 = dom.dxpdx11;
        const Matrix &dxpdx22 = dom.dxpdx22;
        const Matrix &dzpdx11 = dom.dzpdx11;
        const Matrix &dzpdx22 = dom.dzpdx22;
        const Matrix &dxpdz11 = dom.dxpdz11;
        const Matrix &dxpdz22 = dom.dxpdz22;
        const Matrix &dzpdz11 = dom.dzpdz11;
        const Matrix &dzpdz22 = dom.dzpdz22;
        const Matrix &Jac11 = dom.Jac11;
        const Matrix &Jac22 = dom.Jac22;

        // 提取材料参数
        const Matrix &mu11 = dom.mu11;
        const Matrix &mu22 = dom.mu22;

        // ========== 步骤1: 计算计算坐标下的空间导数 ==========
        // 严格按照MATLAB函数调用顺序

        // dUdxp11 = dFdxp(kkx12, U21, invLx22T, invLx11, U21mo, U21po)
        Matrix dUdxp11 = dFdxp(kkx12, U21, invLxT22, invLx11, U21mo, U21po);

        // dUdxp22 = dFdxp(kkx21, U12, invLx11T, invLx22, U12mo, U12po)
        Matrix dUdxp22 = dFdxp(kkx21, U12, invLxT11, invLx22, U12mo, U12po);

        // dUdzp11 = dFdzp(kkz12, U12, invLzT22, invLz11, U12om, U12op)
        Matrix dUdzp11 = dFdzp(kkz12, U12, invLzT22, invLz11, U12om, U12op);

        // dUdzp22 = dFdzp(kkz21, U21, invLzT11, invLz22, U21om, U21op)
        Matrix dUdzp22 = dFdzp(kkz21, U21, invLzT11, invLz22, U21om, U21op);

        // ========== 步骤2: 转换到物理坐标下的导数 ==========
        // MATLAB: dUdx11 = dUdxp11.*dxpdx11 + dUdzp11.*dzpdx11
        Matrix dUdx11 = dUdxp11.hadamard(dxpdx11) + dUdzp11.hadamard(dzpdx11);
        Matrix dUdx22 = dUdxp22.hadamard(dxpdx22) + dUdzp22.hadamard(dzpdx22);

        // MATLAB: dUdz11 = dUdxp11.*dxpdz11 + dUdzp11.*dzpdz11
        Matrix dUdz11 = dUdxp11.hadamard(dxpdz11) + dUdzp11.hadamard(dzpdz11);
        Matrix dUdz22 = dUdxp22.hadamard(dxpdz22) + dUdzp22.hadamard(dzpdz22);

        // ========== 步骤3: 计算应力张量 ==========
        // MATLAB: txx11 = mu11.*dUdx11
        Matrix txx11 = mu11.hadamard(dUdx11);
        Matrix txx22 = mu22.hadamard(dUdx22);

        // MATLAB: tzz11 = mu11.*dUdz11
        Matrix tzz11 = mu11.hadamard(dUdz11);
        Matrix tzz22 = mu22.hadamard(dUdz22);

        // ========== 步骤4: 转换到计算坐标下的应力 ==========
        // MATLAB: Sxx11 = (txx11.*dxpdx11 + tzz11.*dxpdz11).*Jac11
        dom.state.Sxx11 = (txx11.hadamard(dxpdx11) + tzz11.hadamard(dxpdz11)).hadamard(Jac11);
        dom.state.Sxx22 = (txx22.hadamard(dxpdx22) + tzz22.hadamard(dxpdz22)).hadamard(Jac22);

        // MATLAB: Szz11 = (txx11.*dzpdx11 + tzz11.*dzpdz11).*Jac11
        dom.state.Szz11 = (txx11.hadamard(dzpdx11) + tzz11.hadamard(dzpdz11)).hadamard(Jac11);
        dom.state.Szz22 = (txx22.hadamard(dzpdx22) + tzz22.hadamard(dzpdz22)).hadamard(Jac22);

        // 注意：原始MATLAB代码只计算正应力分量(Sxx, Szz)
        // 不计算剪切应力分量(Sxz)，因此这里不包含剪切应力计算
    }
    return OM;
}