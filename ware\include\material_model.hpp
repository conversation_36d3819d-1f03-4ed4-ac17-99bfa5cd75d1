#pragma once
#include "common_types.hpp"
#include <cmath>

namespace prem {

// ------------------------------------------------------------------------------------------
// Polynomial evaluation function (equivalent to MATLAB poly3)
// ------------------------------------------------------------------------------------------
inline Real poly3(Real x, const Real coefs[4]) {
    return coefs[0] + coefs[1]*x + coefs[2]*x*x + coefs[3]*x*x*x;
}

// ------------------------------------------------------------------------------------------
// Layer index determination (equivalent to MATLAB layer_index_prem)
// ------------------------------------------------------------------------------------------
inline Integer layer_index_prem(Real r, char opt = 'c') {
    // Define depth interfaces in km
    constexpr Real depths_interfaces[] = {0.0, 1221.5, 3480.0, 3630.0, 5600.0, 5701.0, 5771.0, 
                                         5971.0, 6151.0, 6291.0, 6346.0, 6356.0, 6368.0, 6371.0};
    constexpr Integer num_layers = sizeof(depths_interfaces)/sizeof(depths_interfaces[0]) - 1;
    
    // Check if radius is within valid range
    if (r < depths_interfaces[0] || r > depths_interfaces[num_layers]) {
        // Warning would be issued in MATLAB
        return 13; // Default to outermost layer
    }
    
    // Initialize layer index
    Integer layer_index = 13;
    
    // Determine the appropriate layer index based on radius
    for (Integer i = num_layers-1; i >= 0; i--) {
        if (r >= depths_interfaces[i] && r < depths_interfaces[i+1]) {
            layer_index = i+1;
            break;
        }
    }
    
    // Adjust layer index based on option
    if (opt == 'c' || opt == 'C') {
        if (layer_index == 13) {
            layer_index = 12;
        }
    }
    
    return layer_index;
}

// ------------------------------------------------------------------------------------------
// Density coefficient functions (equivalent to MATLAB density_coefs_prem)
// ------------------------------------------------------------------------------------------
inline void density_coefs_prem(Real r, char opt, Real coefs[4]) {
    // Determine layer index based on radius and option
    Integer n = layer_index_prem(r, opt);
    
    // Assign coefficients based on layer index
    switch (n) {
        case 1:  // Inner core
            coefs[0] = 13.0885; coefs[1] = 0.0; coefs[2] = -8.8381; coefs[3] = 0.0;
            break;
        case 2:  // Outer core
            coefs[0] = 12.5815; coefs[1] = -1.2638; coefs[2] = -3.6426; coefs[3] = -5.5281;
            break;
        case 3:  // Lower mantle
            coefs[0] = 7.9565; coefs[1] = -6.4761; coefs[2] = 5.5283; coefs[3] = -3.0807;
            break;
        case 4:
            coefs[0] = 7.9565; coefs[1] = -6.4761; coefs[2] = 5.5283; coefs[3] = -3.0807;
            break;
        case 5:
            coefs[0] = 7.9565; coefs[1] = -6.4761; coefs[2] = 5.5283; coefs[3] = -3.0807;
            break;
        case 6:  // Mid mantle/transition zone
            coefs[0] = 5.3197; coefs[1] = -1.4836; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 7:
            coefs[0] = 11.2494; coefs[1] = -8.0298; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 8:
            coefs[0] = 7.1089; coefs[1] = -3.8045; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 9:  // Upper mantle
            coefs[0] = 2.6910; coefs[1] = 0.6924; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 10:
            coefs[0] = 2.6910; coefs[1] = 0.6924; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 11:  // Crust
            coefs[0] = 2.9; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 12:
            coefs[0] = 2.6; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 13:  // Ocean
            coefs[0] = 1.020; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        default:
            // Warning would be issued in MATLAB
            coefs[0] = 2.6; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0; // Default to crust
    }
}

// ------------------------------------------------------------------------------------------
// P-wave velocity coefficient functions (equivalent to MATLAB vp_coefs_prem)
// ------------------------------------------------------------------------------------------
inline void vp_coefs_prem(Real r, char opt, Real coefs[4]) {
    // Determine layer index based on radius and option
    Integer n = layer_index_prem(r, opt);
    
    // Assign coefficients based on layer index
    switch (n) {
        case 1:  // Inner core
            coefs[0] = 11.2622; coefs[1] = 0.0; coefs[2] = -6.3640; coefs[3] = 0.0;
            break;
        case 2:  // Outer core
            coefs[0] = 11.0487; coefs[1] = -4.0362; coefs[2] = 4.8023; coefs[3] = -13.5732;
            break;
        case 3:  // Lower mantle
            coefs[0] = 15.3891; coefs[1] = -5.3181; coefs[2] = 5.5242; coefs[3] = -2.5514;
            break;
        case 4:
            coefs[0] = 24.9520; coefs[1] = -40.4673; coefs[2] = 51.4832; coefs[3] = -26.6419;
            break;
        case 5:
            coefs[0] = 29.2766; coefs[1] = -23.6027; coefs[2] = 5.5242; coefs[3] = -2.5514;
            break;
        case 6:  // Mid mantle/transition zone
            coefs[0] = 19.0957; coefs[1] = -9.8672; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 7:
            coefs[0] = 39.7027; coefs[1] = -32.6166; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 8:
            coefs[0] = 20.3926; coefs[1] = -12.2569; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 9:  // Upper mantle
            coefs[0] = 4.1875; coefs[1] = 3.9382; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 10:
            coefs[0] = 4.1875; coefs[1] = 3.9382; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 11:  // Crust
            coefs[0] = 6.8; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 12:
            coefs[0] = 5.8; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 13:  // Ocean
            coefs[0] = 1.45; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        default:
            // Warning would be issued in MATLAB
            coefs[0] = 5.8; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0; // Default to crust
    }
}

// ------------------------------------------------------------------------------------------
// S-wave velocity coefficient functions (equivalent to MATLAB vs_coefs_prem)
// ------------------------------------------------------------------------------------------
inline void vs_coefs_prem(Real r, char opt, Real coefs[4]) {
    // Determine layer index based on radius and option
    Integer n = layer_index_prem(r, opt);
    
    // Assign coefficients based on layer index
    switch (n) {
        case 1:  // Inner core
            coefs[0] = 3.6678; coefs[1] = 0.0; coefs[2] = -4.4475; coefs[3] = 0.0;
            break;
        case 2:  // Outer core (liquid - no shear waves)
            coefs[0] = 0.0; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 3:  // Lower mantle
            coefs[0] = 6.9254; coefs[1] = 1.4672; coefs[2] = -2.0834; coefs[3] = 0.9783;
            break;
        case 4:
            coefs[0] = 11.1671; coefs[1] = -13.7818; coefs[2] = 17.4575; coefs[3] = -9.2777;
            break;
        case 5:
            coefs[0] = 22.3459; coefs[1] = -17.2473; coefs[2] = -2.0834; coefs[3] = 0.9783;
            break;
        case 6:  // Mid mantle/transition zone
            coefs[0] = 9.9839; coefs[1] = -4.9324; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 7:
            coefs[0] = 22.3512; coefs[1] = -18.5856; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 8:
            coefs[0] = 8.9496; coefs[1] = -4.4597; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 9:  // Upper mantle
            coefs[0] = 2.1519; coefs[1] = 2.3481; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 10:
            coefs[0] = 2.1519; coefs[1] = 2.3481; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 11:  // Crust
            coefs[0] = 3.9; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 12:
            coefs[0] = 3.2; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        case 13:  // Ocean (no shear waves in water)
            coefs[0] = 0.0; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0;
            break;
        default:
            // Warning would be issued in MATLAB
            coefs[0] = 3.2; coefs[1] = 0.0; coefs[2] = 0.0; coefs[3] = 0.0; // Default to crust
    }
}

// ------------------------------------------------------------------------------------------
// PREM density function (equivalent to MATLAB density_prem)
// ------------------------------------------------------------------------------------------
inline Real density_prem(Real r, char opt = 'c') {
    // Normalize radius
    Real x = r / 6371.0;
    
    // Get density coefficients
    Real coefs[4];
    density_coefs_prem(r, opt, coefs);
    
    // Calculate density using polynomial
    return poly3(x, coefs);
}

// ------------------------------------------------------------------------------------------
// PREM P-wave velocity function (equivalent to MATLAB vp_prem)
// ------------------------------------------------------------------------------------------
inline Real vp_prem(Real r, char opt = 'c') {
    // Normalize radius
    Real x = r / 6371.0;
    
    // Get VP coefficients
    Real coefs[4];
    vp_coefs_prem(r, opt, coefs);
    
    // Calculate VP using polynomial
    return poly3(x, coefs);
}

// ------------------------------------------------------------------------------------------
// PREM S-wave velocity function (equivalent to MATLAB vs_prem)
// ------------------------------------------------------------------------------------------
inline Real vs_prem(Real r, char opt = 'c') {
    // Normalize radius
    Real x = r / 6371.0;
    
    // Get VS coefficients
    Real coefs[4];
    vs_coefs_prem(r, opt, coefs);
    
    // Calculate VS using polynomial
    return poly3(x, coefs);
}

// ------------------------------------------------------------------------------------------
// Simplified PREM-like radial profiles for backward compatibility (radius r in km)
// These functions use a simplified linear interpolation approach
// ------------------------------------------------------------------------------------------

// Density ρ (kg/m³) - Simplified version
inline Real rho_prem_km(Real r_km)
{
    if(r_km < 1221)              return 13000 - 6000*(r_km/1221.0);            // inner core
    else if(r_km < 3480)         return 12000 - 3000*((r_km-1221)/(3480-1221)); // outer core
    else if(r_km < 5701)         return 5500  - 800 *((r_km-3480)/(5701-3480)); // lower mantle
    else                         return 3300  - 200 *((r_km-5701)/(6346-5701)); // upper mantle (+ crust)
}

// Shear wave speed Vs (m/s) - Simplified version
inline Real vs_prem_km(Real r_km)
{
    if(r_km < 1221)              return 0.0;                                   // liquid core in PREM (Vs≈0)
    else if(r_km < 3480)         return 0.0;                                   // outer core liquid
    else if(r_km < 5701)         return 4500 + 400*((r_km-3480)/(5701-3480));   // lower mantle 4.5–4.9 km/s
    else                         return 4300 + 300*((r_km-5701)/(6346-5701));   // upper mantle 4.3–4.6 km/s
}

// P-wave speed Vp (m/s) - Simplified version (added for completeness)
inline Real vp_prem_km(Real r_km)
{
    if(r_km < 1221)              return 11000 - 400*(r_km/1221.0);             // inner core
    else if(r_km < 3480)         return 10500 - 1500*((r_km-1221)/(3480-1221)); // outer core
    else if(r_km < 5701)         return 13000 - 3000*((r_km-3480)/(5701-3480)); // lower mantle
    else                         return 8000  - 200 *((r_km-5701)/(6346-5701)); // upper mantle (+ crust)
}

} // namespace prem

// ------------------------------------------------------------------------------------------
// Public interface functions (can be called without namespace prefix)
// ------------------------------------------------------------------------------------------

// PREM model with precise polynomial approximation (radius in km, density in g/cm³, velocity in km/s)
inline Real density_prem_precise(Real r_km, char opt = 'c') {
    return prem::density_prem(r_km, opt) * 0.001; // Convert to g/cm³
}

inline Real vp_prem_precise(Real r_km, char opt = 'c') {
    return prem::vp_prem(r_km, opt) * 0.001; // Convert to km/s
}

inline Real vs_prem_precise(Real r_km, char opt = 'c') {
    return prem::vs_prem(r_km, opt) * 0.001; // Convert to km/s
} 