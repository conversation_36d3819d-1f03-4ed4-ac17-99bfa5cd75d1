<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{344c98ce-6e06-4030-9e9b-63e47790abea}</ProjectGuid>
    <RootNamespace>wave1d</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalIncludeDirectories>D:\try2\1\wave1d\include;D:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OpenMPSupport>true</OpenMPSupport>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>D:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0;D:\111\fin\wave1d\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\include\add_source1d.hpp" />
    <ClInclude Include="..\include\bspln.h" />
    <ClInclude Include="..\include\check_stability.hpp" />
    <ClInclude Include="..\include\compiler_optimization.hpp" />
    <ClInclude Include="..\include\compute_boundary_Svalue_inn1d.hpp" />
    <ClInclude Include="..\include\compute_boundary_Svalue_out1d.hpp" />
    <ClInclude Include="..\include\compute_boundary_Uvalue_inn1d.hpp" />
    <ClInclude Include="..\include\compute_boundary_Uvalue_out1d.hpp" />
    <ClInclude Include="..\include\compute_dt.hpp" />
    <ClInclude Include="..\include\compute_KS1d.hpp" />
    <ClInclude Include="..\include\compute_KU1d.hpp" />
    <ClInclude Include="..\include\create_basis_parameters.hpp" />
    <ClInclude Include="..\include\create_domain1d.hpp" />
    <ClInclude Include="..\include\create_receiver1d.hpp" />
    <ClInclude Include="..\include\create_source1d.hpp" />
    <ClInclude Include="..\include\dbspln.h" />
    <ClInclude Include="..\include\differentiation_matrices.h" />
    <ClInclude Include="..\include\domain_structs.hpp" />
    <ClInclude Include="..\include\edndx.h" />
    <ClInclude Include="..\include\find_neighbors.hpp" />
    <ClInclude Include="..\include\gen_DFDMatrices.hpp" />
    <ClInclude Include="..\include\get_bandwidth.hpp" />
    <ClInclude Include="..\include\Get_Knot_Vector.h" />
    <ClInclude Include="..\include\get_receiver1d.hpp" />
    <ClInclude Include="..\include\get_source1d.hpp" />
    <ClInclude Include="..\include\gllnodes.h" />
    <ClInclude Include="..\include\initialize_domain1d.hpp" />
    <ClInclude Include="..\include\inner_product.hpp" />
    <ClInclude Include="..\include\legacy_defs.h" />
    <ClInclude Include="..\include\lgwt.h" />
    <ClInclude Include="..\include\lgwt.hpp" />
    <ClInclude Include="..\include\mass_matrix.h" />
    <ClInclude Include="..\include\matrix.hpp" />
    <ClInclude Include="..\include\mesh_sphere1d.hpp" />
    <ClInclude Include="..\include\save_wavefields1d.hpp" />
    <ClInclude Include="..\include\save_waveforms1d.hpp" />
    <ClInclude Include="..\include\solve1D.hpp" />
    <ClInclude Include="..\include\stiffness_matrix.h" />
    <ClInclude Include="..\include\update_wavefields1d.hpp" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\main1d.cpp" />
    <ClCompile Include="..\scr\add_source1d.cpp" />
    <ClCompile Include="..\scr\bspln.cpp" />
    <ClCompile Include="..\scr\check_stability.cpp" />
    <ClCompile Include="..\scr\compute_boundary_Svalue_inn1d.cpp" />
    <ClCompile Include="..\scr\compute_boundary_Svalue_out1d.cpp" />
    <ClCompile Include="..\scr\compute_boundary_Uvalue_inn1d.cpp" />
    <ClCompile Include="..\scr\compute_boundary_Uvalue_out1d.cpp" />
    <ClCompile Include="..\scr\compute_dt.cpp" />
    <ClCompile Include="..\scr\compute_KS1d.cpp" />
    <ClCompile Include="..\scr\compute_KU1d.cpp" />
    <ClCompile Include="..\scr\find_neighbors.cpp" />
    <ClCompile Include="..\scr\gen_DFDMatrices.cpp" />
    <ClCompile Include="..\scr\get_bandwidth.cpp" />
    <ClCompile Include="..\scr\get_receiver1d.cpp" />
    <ClCompile Include="..\scr\gllnodes.cpp" />
    <ClCompile Include="..\scr\initialize_domain1d.cpp" />
    <ClCompile Include="..\scr\inner_product.cpp" />
    <ClCompile Include="..\scr\lgwt.cpp" />
    <ClCompile Include="..\scr\mass_matrix.cpp" />
    <ClCompile Include="..\scr\mesh_sphere1d.cpp" />
    <ClCompile Include="..\scr\save_wavefields1d.cpp" />
    <ClCompile Include="..\scr\save_waveforms1d.cpp" />
    <ClCompile Include="..\scr\solve1D.cpp" />
    <ClCompile Include="..\scr\stiffness_matrix.cpp" />
    <ClCompile Include="..\scr\update_wavefields1d.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>