function [receiver] = get_receiver1d(OM,dt,nt)
% GET_RECEIVER Summary of this function goes here

% receiver side
receiver = create_Receiver1d();
receiver.ur = zeros(nt,2); % for benchmark
receiver.ur(:,1) = (1:nt)'*dt;

rom = 7;
receiver.iom = rom;

ndomains = length(OM);

for iom = 1:ndomains
    if rom == iom

        x1  = OM(rom).model1d.xa(1);
        x2  = OM(rom).model1d.xa(end);
        Nx1 = OM(rom).model1d.Nx1;
        px  = OM(rom).model1d.px;
        kx1 = px + 1;

        t1 = Get_Knot_Vector(Nx1,kx1)*(x2-x1) + x1;

        bT1 = zeros(Nx1,1);
        for i = 1:Nx1
            bT1(i) = bspln(t1,Nx1,i,kx1,x2);
        end
        receiver.bT1 = bT1;
    end
end

end

