#include "../include/sparse_matrix.hpp"
#include <omp.h>
#include <vector>

// 实现稀疏矩阵向量乘法的OpenMP并行版本
std::vector<Real> spmv_omp(const CSR& A, const std::vector<Real>& x)
{
    std::vector<Real> y(A.rows, 0.0);
    if(A.rows==0 || x.empty()) return y;
    
    // 检查输入向量维度
    if(x.size() < static_cast<size_t>(A.cols)) {
        throw std::invalid_argument("Input vector size too small for matrix dimensions");
    }
    
    // OpenMP并行循环计算矩阵向量乘法
    #pragma omp parallel for schedule(dynamic, 64)
    for(Integer r=0; r<A.rows; ++r) {
        Real sum = 0.0;
        // 遍历当前行的所有非零元素
        for(Integer idx=A.rowPtr[r]; idx<A.rowPtr[r+1]; ++idx) {
            sum += A.val[idx] * x[A.colIdx[idx]];
        }
        y[r] = sum;
    }
    
    return y;
} 