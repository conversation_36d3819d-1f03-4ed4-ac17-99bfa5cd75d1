#include "../include/solver2dA.hpp"
#include "../include/update_wavefields2dA.hpp"
#include "../include/compute_boundary_Uvalue_inn2dA.hpp"
#include "../include/compute_boundary_Uvalue_out2dA.hpp"
#include "../include/compute_KU2dA.hpp"
#include "../include/compute_boundary_Svalue_inn2dA.hpp"
#include "../include/compute_boundary_Svalue_out2dA.hpp"
#include "../include/compute_KS2dA.hpp"
#include "../include/add_source2dA.hpp"
#include "../include/save_waveforms2dA.hpp"
#include "../include/save_wavefields2dA.hpp"
#include <iostream>
#include <chrono>
#include <string>

std::pair<std::vector<Domain2dA>, std::vector<Receiver2dA>> 
solver2dA(std::vector<Domain2dA>& OM, 
          const std::vector<SourceStruct>& source,
          std::vector<Receiver2dA>& rece, 
          Real dt, 
          Integer nt)
{
    // 计时器，用于评估性能
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 按照MATLAB solver2dA.m的流程执行时间积分
    for (Integer it = 0; it < nt; ++it) {
        // 定期打印进度
        if (it % 10 == 0) {
            auto current_time = std::chrono::high_resolution_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time).count();
            std::cout << "Time step " << it + 1 << "/" << nt 
                      << " (" << (100.0 * (it + 1) / nt) << "%)"
                      << " Elapsed: " << (elapsed / 1000.0) << "s" << std::endl;
        }
        
        try {
            // 1. 更新波场
            OM = update_wavefields2dA(OM, dt);
            
            // 2. 计算内边界变量
            OM = compute_boundary_Uvalue_inn2dA(OM);
            
            // 3. 计算外边界变量
            OM = compute_boundary_Uvalue_out2dA(OM);
            
            // 4. 从位移计算应变
            OM = compute_KU2dA(OM);
            
            // 5. 计算内边界应力
            OM = compute_boundary_Svalue_inn2dA(OM);
            
            // 6. 计算外边界应力
            OM = compute_boundary_Svalue_out2dA(OM);
            
            // 7. 从应变计算加速度
            OM = compute_KS2dA(OM);
            
            // 8. 注入源项
            OM = add_source2dA(OM, source, it);
            
            // 9. 保存波形 - 修改以符合MATLAB版本
            // 解决"No viable overloaded '='错误
            rece = save_waveforms2dA(OM, rece, it);
            
            // 10. 定期保存波场 - 使用与MATLAB一致的条件
            if (it % 50 == 0) {
                // 获取当前工作目录作为输出路径，或者使用配置中的路径
                std::string output_path = "output_cpp/";
                save_wavefields2dA(OM, it, output_path);
                
                // 在实际实现中，我们可以添加绘图功能
                // plot_wavefields2dA(OM, it);
                // 注意：此函数在C++中尚未实现，需要添加或使用外部可视化工具
            }
        }
        catch (const std::exception& e) {
            std::cerr << "Error at time step " << it + 1 << ": " << e.what() << std::endl;
            break;
        }
    }
    
    // 返回更新后的域和接收器
    return {OM, rece};
} 