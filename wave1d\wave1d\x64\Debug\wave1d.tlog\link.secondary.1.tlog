^D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\ADD_SOURCE1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\BSPLN.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\CHECK_STABILITY.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\COMPUTE_BOUNDARY_SVALUE_INN1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\COMPUTE_BOUNDARY_SVALUE_OUT1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\COMPUTE_BOUNDARY_UVALUE_INN1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\COMPUTE_BOUNDARY_UVALUE_OUT1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\COMPUTE_DT.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\COMPUTE_KS1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\COMPUTE_KU1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\FIND_NEIGHBORS.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\GEN_DFDMATRICES.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\GET_BANDWIDTH.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\GET_RECEIVER1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\GLLNODES.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\INITIALIZE_DOMAIN1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\INNER_PRODUCT.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\LGWT.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\MAIN1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\MASS_MATRIX.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\MESH_SPHERE1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\SAVE_WAVEFIELDS1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\SAVE_WAVEFORMS1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\SOLVE1D.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\STIFFNESS_MATRIX.OBJ|D:\TRY2\1\WAVE1D\WAVE1D\X64\DEBUG\UPDATE_WAVEFIELDS1D.OBJ
D:\try2\1\wave1d\wave1d\x64\Debug\wave1d.ilk
