#include "initialize_domain1d.hpp"
#include "matrix.hpp"
#include <algorithm>

namespace wave {
void initialize_domain1d(std::vector<Domain1D>& domains, std::size_t nt)
{
    for(auto &dom : domains){
        int Nx1 = dom.model1d.Nx1;
        if(Nx1 <= 0) continue;
        dom.state.U1_0.assign(Nx1, 0.0);
        dom.state.U1_1.assign(Nx1, 0.0);
        dom.state.U1.assign(Nx1, 0.0);
        dom.state.Ub1.assign(Nx1, 0.0);
        dom.state.dU2dtt1.assign(Nx1, 0.0);
        dom.state.S2.assign(Nx1, 0.0);
        dom.state.Sb2.assign(Nx1, 0.0);
        dom.state.dU1dx2.assign(Nx1, 0.0);
        dom.state.dS2dx1.assign(Nx1, 0.0);
        dom.state.Umid = Matrix(Nx1, nt, 0.0);

        if(dom.iNbrm < 0)
            dom.alpha_xm = 0.0;
        else
            dom.alpha_xm = 0.5;

        if(dom.iNbrp < 0)
            dom.alpha_xp = 0.0;
        else
            dom.alpha_xp = 0.5;
    }
}
}  