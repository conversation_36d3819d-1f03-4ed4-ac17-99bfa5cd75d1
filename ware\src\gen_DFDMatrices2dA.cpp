#include "../include/gen_DFDMatrices2dA.hpp"
#include "../include/stiffness_matrix.hpp"
#include "../include/mass_matrix.hpp"
#include "../include/Get_Knot_Vector.hpp"
#include "../include/lgwt.hpp"
#include "../include/eigen_wrapper.hpp"
#include "../include/setup_basis.hpp"
#include "../include/bspln.hpp"
#include <iostream>

// -----------------------------------------------------------------------------
// Local helper: Kronecker product of two small dense matrices (row-major)
// -----------------------------------------------------------------------------
static Matrix kron(const Matrix &A, const Matrix &B)
{
    Matrix K(A.rows * B.rows, A.cols * B.cols);
    for(Integer i=0;i<A.rows;++i)
        for(Integer j=0;j<A.cols;++j){
            Real aij = A(i,j);
            for(Integer p=0;p<B.rows;++p)
                for(Integer q=0;q<B.cols;++q)
                    K(i*B.rows + p, j*B.cols + q) = aij * B(p,q);
        }
    return K;
}

std::vector<Domain2dA> gen_DFDMatrices2dA(std::vector<Domain2dA>& OM){
    Integer ndomains = static_cast<Integer>(OM.size());
    for(Integer iom = 0; iom < ndomains; ++iom){
        std::cout << "DEBUG: Processing domain " << iom << " in gen_DFDMatrices2dA..." << std::endl;
        auto& dom = OM[iom];
        
        try {
            Integer px1 = dom.px1;
            Integer pz1 = dom.pz1;
            Integer Nx1 = dom.Nx1;
            Integer Nz1 = dom.Nz1;
            Integer Nx2 = Nx1 - 1;
            Integer Nz2 = Nz1 - 1;
            
            std::cout << " with " << Nx1 << " and " << Nz1 << " points ... " << std::endl;
            
            if(Nx1 < px1+2 || Nz1 < pz1+2) {
                std::cerr << "Skip domain " << iom+1 << " : N too small for p=" << px1 << std::endl;
                continue;
            }

            // 使用与MATLAB相同的变量名，使代码更易于比较
            Integer kx1 = px1 + 1;
            Integer kz1 = pz1 + 1;
            Integer kx2 = kx1 - 1;
            Integer kz2 = kz1 - 1;
            
            // 生成节点向量
            Vector tx1 = Get_Knot_Vector(Nx1, kx1);
            Vector tx2 = Get_Knot_Vector(Nx2, kx2);
            Vector tz1 = Get_Knot_Vector(Nz1, kz1);
            Vector tz2 = Get_Knot_Vector(Nz2, kz2);
            
            // 积分点设置与MATLAB一致，高斯积分阶数为6
            Integer ord_gi = 6;
            
            // 计算积分区间
            Integer NBx_intervals = Nx1 - px1;
            Integer NBz_intervals = Nz1 - pz1;
            
            // 生成高斯积分点和权重
            Matrix xint(NBx_intervals, ord_gi), wxint(NBx_intervals, ord_gi);
            for(Integer e = 0; e < NBx_intervals; ++e) {
                auto [xg, wg] = lgwt(ord_gi, tx1(px1+e), tx1(px1+e+1));
                for(Integer g = 0; g < ord_gi; ++g) {
                    xint(e, g) = xg(g);
                    wxint(e, g) = wg(g);
                }
            }
            
            Matrix zint(NBz_intervals, ord_gi), wzint(NBz_intervals, ord_gi);
            for(Integer e = 0; e < NBz_intervals; ++e) {
                auto [zg, wg] = lgwt(ord_gi, tz1(pz1+e), tz1(pz1+e+1));
                for(Integer g = 0; g < ord_gi; ++g) {
                    zint(e, g) = zg(g);
                    wzint(e, g) = wg(g);
                }
            }
            
            // X方向基函数评估
            Real dx = 1.0 / ((Real)Nx1 - 1.0);
            Vector xps1(Nx1);
            for(Integer j = 0; j < Nx1; ++j) xps1(j) = j * dx;
            
            // 计算基函数矩阵 - 严格按照MATLAB gen_DFDMatrices2dA.m第62-76行
            Matrix bx1(Nx1, Nx1);
            for(Integer i = 1; i <= Nx1; ++i) {
                for(Integer j = 1; j <= Nx1; ++j) {
                    Real xj = xps1(j-1);  // MATLAB: xj = xps1(j)
                    bx1(i-1, j-1) = bspln(tx1, Nx1, i, kx1, xj);
                }
            }

            Matrix bx2(Nx2, Nx1);  // 注意：Nx2 × Nx1，不是方阵！
            for(Integer i = 1; i <= Nx2; ++i) {
                for(Integer j = 1; j <= Nx1; ++j) {
                    Real x0 = xps1(j-1);  // MATLAB: x0 = xps1(j)
                    bx2(i-1, j-1) = bspln(tx2, Nx2, i, kx2, x0);
                }
            }
            
            // Z方向基函数评估
            Real dz = 1.0 / ((Real)Nz1 - 1.0);
            Vector zps1(Nz1);
            for(Integer j = 0; j < Nz1; ++j) zps1(j) = j * dz;

            // Z方向基函数计算 - 严格按照MATLAB gen_DFDMatrices2dA.m第79-95行
            Matrix bz1(Nz1, Nz1);
            for(Integer i = 1; i <= Nz1; ++i) {
                for(Integer j = 1; j <= Nz1; ++j) {
                    Real zj = zps1(j-1);  // MATLAB: zj = zps1(j)
                    bz1(i-1, j-1) = bspln(tz1, Nz1, i, kz1, zj);
                }
            }

            Matrix bz2(Nz2, Nz1);  // 注意：Nz2 × Nz1，不是方阵！
            for(Integer i = 1; i <= Nz2; ++i) {
                for(Integer j = 1; j <= Nz1; ++j) {
                    Real z0 = zps1(j-1);  // MATLAB: z0 = zps1(j)
                    bz2(i-1, j-1) = bspln(tz2, Nz2, i, kz2, z0);  // 正确：kz2，不是kx2
                }
            }

            // 计算刚度矩阵，与MATLAB中的stiffness_matrix调用对应
            dom.kkx12 = stiffness_matrix(px1, Nx1, tx1, tx2, xint, wxint, 0);
            dom.kkz12 = stiffness_matrix(pz1, Nz1, tz1, tz2, zint, wzint, 0);
            dom.kkx21 = stiffness_matrix(px1, Nx1, tx1, tx2, xint, wxint, 1);
            dom.kkz21 = stiffness_matrix(pz1, Nz1, tz1, tz2, zint, wzint, 1);
            
            // 计算质量矩阵，与MATLAB中的mass_matrix调用对应
            Matrix mmx11 = mass_matrix(px1, Nx1, Nx1, tx1, tx1, xint, wxint, 0);
            Matrix mmz11 = mass_matrix(pz1, Nz1, Nz1, tz1, tz1, zint, wzint, 0);
            Matrix mmx22 = mass_matrix(px1, Nx2, Nx2, tx2, tx2, xint, wxint, 1);
            Matrix mmz22 = mass_matrix(pz1, Nz2, Nz2, tz2, tz2, zint, wzint, 1);
            
            // 存储基函数转置矩阵
            dom.bxT1 = EigenWrapper::transpose(bx1);
            dom.bxT2 = EigenWrapper::transpose(bx2);
            dom.bzT1 = EigenWrapper::transpose(bz1);
            dom.bzT2 = EigenWrapper::transpose(bz2);

            // 存储质量矩阵
            dom.Mx11 = mmx11;
            dom.Mz11 = mmz11;
            dom.Mx22 = mmx22;
            dom.Mz22 = mmz22;

            // 计算完整2D质量矩阵
            dom.mm11 = kron(mmz11, mmx11);
            dom.mm22 = kron(mmz22, mmx22);

            // 计算矩阵平方根和逆矩阵，与MATLAB保持一致
            // 使用EigenWrapper中的sqrtm函数
            std::cout << "DEBUG: Computing matrix square roots for domain " << iom << "..." << std::endl;
            Matrix LxT11 = EigenWrapper::sqrtm(mmx11);
            std::cout << "DEBUG: LxT11 computed successfully" << std::endl;
            Matrix Lx11  = EigenWrapper::transpose(LxT11); // 保持与MATLAB相同，计算转置
            Matrix LxT22 = EigenWrapper::sqrtm(mmx22);
            std::cout << "DEBUG: LxT22 computed successfully" << std::endl;
            Matrix Lx22  = EigenWrapper::transpose(LxT22);

            Matrix LzT11 = EigenWrapper::sqrtm(mmz11);
            std::cout << "DEBUG: LzT11 computed successfully" << std::endl;
            Matrix Lz11  = EigenWrapper::transpose(LzT11);
            Matrix LzT22 = EigenWrapper::sqrtm(mmz22);
            std::cout << "DEBUG: LzT22 computed successfully" << std::endl;
            Matrix Lz22  = EigenWrapper::transpose(LzT22);

            // 计算逆矩阵
            std::cout << "DEBUG: Computing inverse matrices for domain " << iom << "..." << std::endl;
            Matrix invLx11  = EigenWrapper::inv(Lx11);
            std::cout << "DEBUG: invLx11 computed successfully" << std::endl;
            Matrix invLxT11 = EigenWrapper::inv(LxT11);
            std::cout << "DEBUG: invLxT11 computed successfully" << std::endl;
            Matrix invLx22  = EigenWrapper::inv(Lx22);
            std::cout << "DEBUG: invLx22 computed successfully" << std::endl;
            Matrix invLxT22 = EigenWrapper::inv(LxT22);
            std::cout << "DEBUG: invLxT22 computed successfully" << std::endl;

            Matrix invLz11  = EigenWrapper::inv(Lz11);
            std::cout << "DEBUG: invLz11 computed successfully" << std::endl;
            Matrix invLzT11 = EigenWrapper::inv(LzT11);
            std::cout << "DEBUG: invLzT11 computed successfully" << std::endl;
            Matrix invLz22  = EigenWrapper::inv(Lz22);
            std::cout << "DEBUG: invLz22 computed successfully" << std::endl;
            Matrix invLzT22 = EigenWrapper::inv(LzT22);
            std::cout << "DEBUG: invLzT22 computed successfully" << std::endl;

            // 存储所有矩阵到域结构
            dom.invLx11 = invLx11;
            dom.invLxT11 = invLxT11;
            dom.invLx22 = invLx22;
            dom.invLxT22 = invLxT22;
            dom.invLz11 = invLz11;
            dom.invLzT11 = invLzT11;
            dom.invLz22 = invLz22;
            dom.invLzT22 = invLzT22;

            // 以下部分实现边界连接矩阵
            // 设置基函数
            Basis basis_Nx1 = setup_basis(Nx1, px1);
            Basis basis_Nx2 = setup_basis(Nx2, px1-1);
            Basis basis_Nz1 = setup_basis(Nz1, pz1);
            Basis basis_Nz2 = setup_basis(Nz2, pz1-1);
            
            // 处理四个边界方向的连接
            // mo - 左边界
            Integer iNbr_mo = dom.iNbr_mo;
            if(iNbr_mo != 0 && iNbr_mo-1 < ndomains) {
                Domain2dA &nbr = OM[iNbr_mo-1];

                // 检查边界类型，决定计算Dzz还是Dzx矩阵
                Integer flip = dom.iFace_mo;
                if(std::abs(flip) == 1 || std::abs(flip) == 2) {
                    // z → z 映射
                    Basis basis_Mz1 = setup_basis(nbr.Nz1, pz1);
                    Basis basis_Mz2 = setup_basis(nbr.Nz1-1, pz1-1);
                    dom.Dzz210mo = inner_product2(basis_Nz2, basis_Mz1);
                    dom.Dzz120mo = inner_product2(basis_Nz1, basis_Mz2);
                    dom.Dzz110mo = inner_product2(basis_Nz1, basis_Mz1);
                    dom.Dzz220mo = inner_product2(basis_Nz2, basis_Mz2);
                } else {
                    // z → x 映射
                    Basis basis_Mx1 = setup_basis(nbr.Nx1, px1);
                    Basis basis_Mx2 = setup_basis(nbr.Nx1-1, px1-1);
                    dom.Dzx210mo = inner_product2(basis_Nz2, basis_Mx1);
                    dom.Dzx120mo = inner_product2(basis_Nz1, basis_Mx2);
                    dom.Dzx110mo = inner_product2(basis_Nz1, basis_Mx1);
                    dom.Dzx220mo = inner_product2(basis_Nz2, basis_Mx2);
                }
            }

            // po - 右边界 (与MATLAB一致的处理)
            Integer iNbr_po = dom.iNbr_po;
            if(iNbr_po != 0 && iNbr_po-1 < ndomains) {
                Domain2dA &nbr = OM[iNbr_po-1];
                
                Integer flip = dom.iFace_po;
                if(std::abs(flip) == 1 || std::abs(flip) == 2) {
                    // z → z 映射
                    Basis basis_Mz1 = setup_basis(nbr.Nz1, pz1);
                    Basis basis_Mz2 = setup_basis(nbr.Nz1-1, pz1-1);
                    dom.Dzz210po = inner_product2(basis_Nz2, basis_Mz1);
                    dom.Dzz120po = inner_product2(basis_Nz1, basis_Mz2);
                    dom.Dzz110po = inner_product2(basis_Nz1, basis_Mz1);
                    dom.Dzz220po = inner_product2(basis_Nz2, basis_Mz2);
                } else {
                    // z → x 映射
                    Basis basis_Mx1 = setup_basis(nbr.Nx1, px1);
                    Basis basis_Mx2 = setup_basis(nbr.Nx1-1, px1-1);
                    dom.Dzx210po = inner_product2(basis_Nz2, basis_Mx1);
                    dom.Dzx120po = inner_product2(basis_Nz1, basis_Mx2);
                    dom.Dzx110po = inner_product2(basis_Nz1, basis_Mx1);
                    dom.Dzx220po = inner_product2(basis_Nz2, basis_Mx2);
                }
            }
            
            // om - 下边界 (与MATLAB一致的处理)
            Integer iNbr_om = dom.iNbr_om;
            if(iNbr_om != 0 && iNbr_om-1 < ndomains) {
                Domain2dA &nbr = OM[iNbr_om-1];
                
                Integer flip = dom.iFace_om;
                if(std::abs(flip) == 3 || std::abs(flip) == 4) {
                    // x → x 映射
                    Basis basis_Mx1 = setup_basis(nbr.Nx1, px1);
                    Basis basis_Mx2 = setup_basis(nbr.Nx1-1, px1-1);
                    dom.Dxx210om = inner_product2(basis_Nx2, basis_Mx1);
                    dom.Dxx120om = inner_product2(basis_Nx1, basis_Mx2);
                    dom.Dxx110om = inner_product2(basis_Nx1, basis_Mx1);
                    dom.Dxx220om = inner_product2(basis_Nx2, basis_Mx2);
                } else {
                    // x → z 映射
                    Basis basis_Mz1 = setup_basis(nbr.Nz1, pz1);
                    Basis basis_Mz2 = setup_basis(nbr.Nz1-1, pz1-1);
                    dom.Dxz210om = inner_product2(basis_Nx2, basis_Mz1);
                    dom.Dxz120om = inner_product2(basis_Nx1, basis_Mz2);
                    dom.Dxz110om = inner_product2(basis_Nx1, basis_Mz1);
                    dom.Dxz220om = inner_product2(basis_Nx2, basis_Mz2);
                }
            }
            
            // op - 上边界 (与MATLAB一致的处理)
            Integer iNbr_op = dom.iNbr_op;
            if(iNbr_op != 0 && iNbr_op-1 < ndomains) {
                Domain2dA &nbr = OM[iNbr_op-1];
                
                Integer flip = dom.iFace_op;
                if(std::abs(flip) == 3 || std::abs(flip) == 4) {
                    // x → x 映射
                    Basis basis_Mx1 = setup_basis(nbr.Nx1, px1);
                    Basis basis_Mx2 = setup_basis(nbr.Nx1-1, px1-1);
                    dom.Dxx210op = inner_product2(basis_Nx2, basis_Mx1);
                    dom.Dxx120op = inner_product2(basis_Nx1, basis_Mx2);
                    dom.Dxx110op = inner_product2(basis_Nx1, basis_Mx1);
                    dom.Dxx220op = inner_product2(basis_Nx2, basis_Mx2);
                } else {
                    // x → z 映射
                    Basis basis_Mz1 = setup_basis(nbr.Nz1, pz1);
                    Basis basis_Mz2 = setup_basis(nbr.Nz1-1, pz1-1);
                    dom.Dxz210op = inner_product2(basis_Nx2, basis_Mz1);
                    dom.Dxz120op = inner_product2(basis_Nx1, basis_Mz2);
                    dom.Dxz110op = inner_product2(basis_Nx1, basis_Mz1);
                    dom.Dxz220op = inner_product2(basis_Nx2, basis_Mz2);
                }
            }
            std::cout << "DEBUG: Domain " << iom << " processing completed successfully" << std::endl;
        }
        catch(const std::exception &ex) {
            // 更健壮的错误处理，报告错误但继续其他域的处理
            std::cerr << "ERROR: Domain " << iom << " failed in gen_DFDMatrices2dA: " << ex.what() << std::endl;
            // 不终止程序，继续处理其他域
        }
    }

    return OM;
} 