﻿#ifndef GET_KNOT_VECTOR_H
#define GET_KNOT_VECTOR_H

#include <vector>
#include "legacy_defs.h"

inline std::vector<double> Get_Knot_Vector(int n,int k){
    const double eps=2.220446049250313e-16;
    std::vector<double> t(n+k,0.0);
    double dx=1.0/(n+1-k);
    for(int i=k;i<=n+1;++i) t[i-1]=(i-k)*dx;
    for(int i=0;i<k;++i) t[i]=t[k-1]-10*eps;
    for(int i=n+1;i<n+k;++i) t[i]=t[n]+10*eps;
    return t;
}

#endif 