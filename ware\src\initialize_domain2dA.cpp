#include "../include/initialize_domain2dA.hpp"
#include "../include/common_types.hpp"
#include <iostream>

std::vector<Domain2dA> initialize_domain2dA(std::vector<Domain2dA>& OM, Integer nt)
{
    std::cout << "Initializing domains with nt = " << nt << "..." << std::endl;
    
    // 第一阶段：初始化每个域的状态变量
    for(size_t iom = 0; iom < OM.size(); ++iom) {
        std::cout << "... Initialize the state of " << iom+1 << " Domain ... " << std::endl;
        
        auto &dom = OM[iom];
        Integer Nx1 = dom.Nx1;
        Integer Nz1 = dom.Nz1;
        Integer Nx2 = (Nx1>0) ? Nx1-1 : 0;
        Integer Nz2 = (Nz1>0) ? Nz1-1 : 0;
        
        if(Nx1==0 || Nz1==0) continue;
        
        // 加速度
        dom.state.dU2dtt12 = Matrix(Nx1, Nz2, 0.0);
        dom.state.dU2dtt21 = Matrix(Nx2, Nz1, 0.0);
        
        // 位移
        // U12
        dom.state.U12_0 = Matrix(Nx1, Nz2, 0.0);
        dom.state.U12_1 = Matrix(Nx1, Nz2, 0.0);
        dom.state.U12 = Matrix(Nx1, Nz2, 0.0);
        // U21
        dom.state.U21_0 = Matrix(Nx2, Nz1, 0.0);
        dom.state.U21_1 = Matrix(Nx2, Nz1, 0.0);
        dom.state.U21 = Matrix(Nx2, Nz1, 0.0);
        
        // 位移边界值 左 + 右
        // U12 (注意：创建行向量，与MATLAB保持一致)
        dom.state.U12mo = Vector(Nz2, 0.0);
        dom.state.U12po = Vector(Nz2, 0.0);
        dom.state.U12mo_inn = Vector(Nz2, 0.0);
        dom.state.U12po_inn = Vector(Nz2, 0.0);
        dom.state.U12mo_out = Vector(Nz2, 0.0);
        dom.state.U12po_out = Vector(Nz2, 0.0);
        
        // U21
        dom.state.U21mo = Vector(Nz1, 0.0);
        dom.state.U21po = Vector(Nz1, 0.0);
        dom.state.U21mo_inn = Vector(Nz1, 0.0);
        dom.state.U21po_inn = Vector(Nz1, 0.0);
        dom.state.U21mo_out = Vector(Nz1, 0.0);
        dom.state.U21po_out = Vector(Nz1, 0.0);
        
        // 位移边界值 下 + 上
        // U12
        dom.state.U12om = Vector(Nx1, 0.0);
        dom.state.U12op = Vector(Nx1, 0.0);
        dom.state.U12om_inn = Vector(Nx1, 0.0);
        dom.state.U12op_inn = Vector(Nx1, 0.0);
        dom.state.U12om_out = Vector(Nx1, 0.0);
        dom.state.U12op_out = Vector(Nx1, 0.0);
        
        // U21
        dom.state.U21om = Vector(Nx2, 0.0);
        dom.state.U21op = Vector(Nx2, 0.0);
        dom.state.U21om_inn = Vector(Nx2, 0.0);
        dom.state.U21op_inn = Vector(Nx2, 0.0);
        dom.state.U21om_out = Vector(Nx2, 0.0);
        dom.state.U21op_out = Vector(Nx2, 0.0);
        
        // 应力场
        // S11
        dom.state.Sxx11 = Matrix(Nx1, Nz1, 0.0);
        dom.state.Szz11 = Matrix(Nx1, Nz1, 0.0);
        // S22
        dom.state.Sxx22 = Matrix(Nx2, Nz2, 0.0);
        dom.state.Szz22 = Matrix(Nx2, Nz2, 0.0);
        
        // 应力场边界值 (之前缺失的部分)
        // 左 + 右
        dom.state.Sxx11mo = Vector(Nz1, 0.0);
        dom.state.Sxx11po = Vector(Nz1, 0.0);
        dom.state.Sxx11mo_inn = Vector(Nz1, 0.0);
        dom.state.Sxx11po_inn = Vector(Nz1, 0.0);
        dom.state.Sxx11mo_out = Vector(Nz1, 0.0);
        dom.state.Sxx11po_out = Vector(Nz1, 0.0);
        
        dom.state.Sxx11om_inn = Vector(Nx1, 0.0);
        dom.state.Sxx11op_inn = Vector(Nx1, 0.0);
        
        // 左 + 右 (S22)
        dom.state.Sxx22mo = Vector(Nz2, 0.0);
        dom.state.Sxx22po = Vector(Nz2, 0.0);
        dom.state.Sxx22mo_inn = Vector(Nz2, 0.0);
        dom.state.Sxx22po_inn = Vector(Nz2, 0.0);
        dom.state.Sxx22mo_out = Vector(Nz2, 0.0);
        dom.state.Sxx22po_out = Vector(Nz2, 0.0);
        
        dom.state.Sxx22om_inn = Vector(Nx2, 0.0);
        dom.state.Sxx22op_inn = Vector(Nx2, 0.0);
        
        // 应力场边界值 下 + 上
        dom.state.Szz11om = Vector(Nx1, 0.0);
        dom.state.Szz11op = Vector(Nx1, 0.0);
        dom.state.Szz11om_inn = Vector(Nx1, 0.0);
        dom.state.Szz11op_inn = Vector(Nx1, 0.0);
        dom.state.Szz11om_out = Vector(Nx1, 0.0);
        dom.state.Szz11op_out = Vector(Nx1, 0.0);
        
        dom.state.Szz11mo_inn = Vector(Nz1, 0.0);
        dom.state.Szz11po_inn = Vector(Nz1, 0.0);
        
        // 下 + 上 (S22)
        dom.state.Szz22om = Vector(Nx2, 0.0);
        dom.state.Szz22op = Vector(Nx2, 0.0);
        dom.state.Szz22om_inn = Vector(Nx2, 0.0);
        dom.state.Szz22op_inn = Vector(Nx2, 0.0);
        dom.state.Szz22om_out = Vector(Nx2, 0.0);
        dom.state.Szz22op_out = Vector(Nx2, 0.0);
        
        dom.state.Szz22mo_inn = Vector(Nz2, 0.0);
        dom.state.Szz22po_inn = Vector(Nz2, 0.0);
        
        // Umid 存储 - 根据 nt 分配 3D 数组
        // 在 C++ 中使用 2D 矩阵向量表示 3D 数组
        if(nt > 0) {
            // 创建 nt 个时间步长的存储，与MATLAB的floor(nt)保持一致
            Integer nt_floor = std::floor(static_cast<double>(nt));
            std::cout << "  Allocating Umid storage for " << nt_floor << " timesteps" << std::endl;
            
            dom.state.Umid.clear();
            dom.state.Umid.reserve(nt_floor);
            for (Integer t = 0; t < nt_floor; ++t) {
                dom.state.Umid.push_back(Matrix(Nx1, Nz1, 0.0));
            }
        }
        
        // ------------------------------------------------------------------
        // Material property matrices & geometric Jacobian placeholders
        // ------------------------------------------------------------------
        
        Real rho_val = dom.rho;         // scalar density (default 2000)
        Real mu_val  = dom.mu;          // scalar shear modulus (default set)
        
        dom.rho12 = Matrix(Nx1, Nz2, rho_val);
        dom.rho21 = Matrix(Nx2, Nz1, rho_val);
        
        dom.mu11  = Matrix(Nx1, Nz1, mu_val);
        dom.mu22  = Matrix(Nx2, Nz2, mu_val);
        
        // Jacobian matrices：初步设为 1（平直网格），后续 refine_model2dA 会更新
        dom.Jac11 = Matrix(Nx1, Nz1, 1.0);
        dom.Jac22 = Matrix(Nx2, Nz2, 1.0);
        
        // 坐标导数矩阵 dxp/dx 等也暂设为 1 或 0，保证尺寸正确
        dom.dxpdx11 = Matrix(Nx1, Nz1, 1.0);
        dom.dzpdx11 = Matrix(Nx1, Nz1, 0.0);
        dom.dxpdz11 = Matrix(Nx1, Nz1, 0.0);
        dom.dzpdz11 = Matrix(Nx1, Nz1, 1.0);
        
        dom.dxpdx22 = Matrix(Nx2, Nz2, 1.0);
        dom.dzpdx22 = Matrix(Nx2, Nz2, 0.0);
        dom.dxpdz22 = Matrix(Nx2, Nz2, 0.0);
        dom.dzpdz22 = Matrix(Nx2, Nz2, 1.0);
        
        dom.dxpdx12 = Matrix(Nx1, Nz2, 1.0);
        dom.dzpdx12 = Matrix(Nx1, Nz2, 0.0);
        dom.dxpdz12 = Matrix(Nx1, Nz2, 0.0);
        dom.dzpdz12 = Matrix(Nx1, Nz2, 1.0);
        
        dom.dxpdx21 = Matrix(Nx2, Nz1, 1.0);
        dom.dzpdx21 = Matrix(Nx2, Nz1, 0.0);
        dom.dxpdz21 = Matrix(Nx2, Nz1, 0.0);
        dom.dzpdz21 = Matrix(Nx2, Nz1, 1.0);
    }
    
    // 第二阶段：设置边界耦合 alpha 值 (在MATLAB中第117-153行)
    for(size_t iom = 0; iom < OM.size(); ++iom) {
        std::cout << "... set the alpha values of " << iom+1 << " Domain ... " << std::endl;
        
        auto &dom = OM[iom];
        
        // 检查邻域，设置相应的alpha值
        Integer iNbr_mo = dom.iNbr_mo;
        Integer iNbr_po = dom.iNbr_po;
        Integer iNbr_om = dom.iNbr_om;
        Integer iNbr_op = dom.iNbr_op;
        
        // 左邻域
        if(iNbr_mo == 0) {
            dom.alpha_mo = 0.0;
        } else {
            dom.alpha_mo = 0.5;
        }
        
        // 右邻域
        if(iNbr_po == 0) {
            dom.alpha_po = 0.0;
        } else {
            dom.alpha_po = 0.5;
        }
        
        // 下邻域
        if(iNbr_om == 0) {
            dom.alpha_om = 0.0;
        } else {
            dom.alpha_om = 0.5;
        }
        
        // 上邻域
        if(iNbr_op == 0) {
            dom.alpha_op = 0.0;
        } else {
            dom.alpha_op = 0.5;
        }
    }
    
    return OM;
} 