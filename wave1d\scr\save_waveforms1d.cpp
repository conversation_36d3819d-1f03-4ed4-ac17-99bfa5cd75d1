#include "save_waveforms1d.hpp"
#include "create_receiver1d.hpp"
#include "domain_structs.hpp"
#include "matrix.hpp"
#include <iostream>
#include <vector>
#include <cstddef>

namespace wave {

    void save_waveforms1d(Receiver1D& rece,
                          const std::vector<double>& U1,
                          std::size_t it)
    {
        if (it >= rece.ur.size()) {
            rece.ur.resize(it + 1, 0.0);
        }

        if (rece.b1T.rows != U1.size() || rece.b1T.cols != 1) {
            std::cerr << "[DBG] dim-mismatch: b1T.rows="
            << rece.b1T.rows << ", U1.size=" << U1.size() << '\n';
            rece.ur[it] = 0.0;
            return;
        }

        double val = 0.0;
        for (std::size_t i = 0; i < U1.size(); ++i) {
            val += rece.b1T(i, 0) * U1[i];
        }
        if (it == 0 && std::abs(val) < 1e-12) {
            std::cerr << "[DBG] first-step dot=0\n";
        }
        rece.ur[it] = val;
            }

}