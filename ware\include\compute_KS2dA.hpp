#pragma once

#include "mesh_sphere2dA.hpp"
#include <vector>

/**
 * @file compute_KS2dA.hpp
 * @brief C++ implementation based on MATLAB compute_KS2dA.m - Stress to acceleration conversion
 * 
 * This file implements the C++ version of the compute_KS2dA.m function from MATLAB. This function
 * calculates acceleration (second derivative of U) from the stress tensor (S),
 * which is a key step in the wave equation.
 * 
 * Corresponding MATLAB code:
 * function [OM] = compute_KS2dA(OM)
 *   for iom = 1:length(OM)
 *     ...extract matrices...
 *     dSxxdxp21 = dFdxp(kkx21, Sxx11, invLxT11, invLx22, Sxx11mo, Sxx11po);
 *     dSxxdxp12 = dFdxp(kkx12, Sxx22, invLxT22, invLx11, Sxx22mo, Sxx22po);
 *     dSzzdzp21 = dFdzp(kkz12, Szz22, invLzT22, invLz11, Szz22om, Szz22op);
 *     dSzzdzp12 = dFdzp(kkz21, Szz11, invLzT11, invLz22, Szz11om, Szz11op);
 *     OM(iom).state.dU2dtt21 = dSxxdxp21 + dSzzdzp21;
 *     OM(iom).state.dU2dtt12 = dSxxdxp12 + dSzzdzp12;
 *   end
 * end
 * 
 * Note: This implementation has removed the metric tensor multiplication and Jacobian/density
 * scaling that was added in the original C++ code, to more accurately match the MATLAB code behavior.
 */

/**
 * @brief Calculate stress to acceleration conversion
 * @param OM Vector of domains (Domain2dA), each containing all matrices and state variables needed for computation
 * @return Updated vector of domains, with dU2dtt21 and dU2dtt12 computed
 */
std::vector<Domain2dA> compute_KS2dA(std::vector<Domain2dA>& OM); 