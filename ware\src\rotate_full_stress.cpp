#include "../include/rotate_full_stress.hpp"
#include <iostream>

void rotate_full_stress(Matrix &Sxx, Matrix &Szz, Matrix &Sxz, const Matrix &rot)
{
    // 检查旋转矩阵维度
    if (rot.rows != 2 || rot.cols != 2) {
        std::cerr << "Error: Rotation matrix must be 2x2" << std::endl;
        return;
    }
    
    // 提取旋转矩阵元素
    const Real r11 = rot(0, 0);
    const Real r12 = rot(0, 1);
    const Real r21 = rot(1, 0);
    const Real r22 = rot(1, 1);
    
    // 检查输入矩阵尺寸是否匹配
    if (Sxx.rows != Szz.rows || Sxx.cols != Szz.cols || 
        Sxx.rows != Sxz.rows || Sxx.cols != Sxz.cols) {
        std::cerr << "Error: Stress component matrices must have the same dimensions" << std::endl;
        return;
    }
    
    const Integer nx = Sxx.rows;
    const Integer nz = Sxx.cols;
    
    // 临时存储旋转后的应力值
    Matrix Sxx_new(nx, nz);
    Matrix Szz_new(nx, nz);
    Matrix Sxz_new(nx, nz);
    
    // 执行完整应力张量旋转
    for (Integer i = 0; i < nx; ++i) {
        for (Integer k = 0; k < nz; ++k) {
            // 提取原始应力分量
            Real sxx = Sxx(i, k);
            Real szz = Szz(i, k);
            Real sxz = Sxz(i, k);
            
            // 应用旋转变换 - 使用完整的应力旋转公式：σ' = R·σ·R^T
            // 对于2D应力张量，旋转后的分量为：
            // σxx' = r11^2·σxx + r12^2·σzz + 2·r11·r12·σxz
            // σzz' = r21^2·σxx + r22^2·σzz + 2·r21·r22·σxz
            // σxz' = r11·r21·σxx + r12·r22·σzz + (r11·r22 + r12·r21)·σxz
            
            Sxx_new(i, k) = r11*r11*sxx + r12*r12*szz + 2*r11*r12*sxz;
            Szz_new(i, k) = r21*r21*sxx + r22*r22*szz + 2*r21*r22*sxz;
            Sxz_new(i, k) = r11*r21*sxx + r12*r22*szz + (r11*r22 + r12*r21)*sxz;
        }
    }
    
    // 更新原始矩阵
    Sxx = Sxx_new;
    Szz = Szz_new;
    Sxz = Sxz_new;
}

void rotate_full_stress(Vector &Sxx, Vector &Szz, Vector &Sxz, const Matrix &rot)
{
    // 检查旋转矩阵维度
    if (rot.rows != 2 || rot.cols != 2) {
        std::cerr << "Error: Rotation matrix must be 2x2" << std::endl;
        return;
    }
    
    // 提取旋转矩阵元素
    const Real r11 = rot(0, 0);
    const Real r12 = rot(0, 1);
    const Real r21 = rot(1, 0);
    const Real r22 = rot(1, 1);
    
    // 检查输入向量尺寸是否匹配
    const Integer n = Sxx.size();
    if (Szz.size() != n || Sxz.size() != n) {
        std::cerr << "Error: Stress component vectors must have the same length" << std::endl;
        return;
    }
    
    // 临时存储旋转后的应力值
    Vector Sxx_new(n);
    Vector Szz_new(n);
    Vector Sxz_new(n);
    
    // 执行完整应力张量旋转
    for (Integer i = 0; i < n; ++i) {
        // 提取原始应力分量
        Real sxx = Sxx[i];
        Real szz = Szz[i];
        Real sxz = Sxz[i];
        
        // 应用旋转变换，同上
        Sxx_new[i] = r11*r11*sxx + r12*r12*szz + 2*r11*r12*sxz;
        Szz_new[i] = r21*r21*sxx + r22*r22*szz + 2*r21*r22*sxz;
        Sxz_new[i] = r11*r21*sxx + r12*r22*szz + (r11*r22 + r12*r21)*sxz;
    }
    
    // 更新原始向量
    Sxx = Sxx_new;
    Szz = Szz_new;
    Sxz = Sxz_new;
} 