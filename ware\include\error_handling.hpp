#pragma once

#include <string>
#include <stdexcept>
#include <iostream>
#include <sstream>
#include <vector>

/**
 * @brief MATLAB风格的错误处理类
 * 
 * 这个命名空间包含与MATLAB兼容的错误处理和警告函数，
 * 用于提供一致的错误报告机制。
 */
namespace MatlabError {

/**
 * @brief 错误类型枚举
 */
enum class ErrorType {
    DimensionMismatch,   // 矩阵维度不匹配
    SingularMatrix,      // 奇异矩阵
    NotImplemented,      // 功能未实现
    OutOfBounds,         // 索引越界
    ValueError,          // 值错误（如无效参数）
    InternalError,       // 内部错误
    RuntimeError         // 运行时错误
};

/**
 * @brief 警告级别枚举
 */
enum class WarningLevel {
    Info,       // 信息级别
    Caution,    // 注意级别
    Warning,    // 警告级别
    Severe      // 严重级别
};

/**
 * @brief 错误处理选项
 */
struct ErrorOptions {
    bool verbose = true;         // 是否打印详细错误信息
    bool throw_exceptions = true; // 是否抛出异常
    bool log_to_file = false;    // 是否记录到文件
    std::string log_file = "matlab_errors.log"; // 日志文件名
};

// 全局错误选项
extern ErrorOptions error_options;

/**
 * @brief 设置错误处理选项
 * 
 * @param options 新的错误处理选项
 */
void set_error_options(const ErrorOptions& options);

/**
 * @brief 抛出MATLAB风格的错误
 * 
 * @param function_name 发生错误的函数名
 * @param message 错误消息
 * @param error_type 错误类型
 * @throw std::exception 如果throw_exceptions为true
 */
void error(const std::string& function_name, const std::string& message, 
           ErrorType error_type = ErrorType::RuntimeError);

/**
 * @brief 发出MATLAB风格的警告
 * 
 * @param function_name 发出警告的函数名
 * @param message 警告消息
 * @param level 警告级别
 */
void warning(const std::string& function_name, const std::string& message,
             WarningLevel level = WarningLevel::Warning);

/**
 * @brief 检查矩阵维度匹配
 * 
 * @param A 第一个矩阵的维度 (rows, cols)
 * @param B 第二个矩阵的维度 (rows, cols)
 * @param operation 操作名称
 * @param function_name 函数名称
 * @return true 如果维度匹配
 * @throw std::invalid_argument 如果维度不匹配
 */
bool check_dimensions(const std::pair<int, int>& A, const std::pair<int, int>& B, 
                      const std::string& operation, const std::string& function_name);

/**
 * @brief 检查矩阵是否为方阵
 * 
 * @param A 矩阵的维度 (rows, cols)
 * @param function_name 函数名称
 * @return true 如果是方阵
 * @throw std::invalid_argument 如果不是方阵
 */
bool check_square(const std::pair<int, int>& A, const std::string& function_name);

/**
 * @brief 检查索引是否在范围内
 * 
 * @param index 索引值
 * @param max_index 最大有效索引
 * @param function_name 函数名称
 * @return true 如果索引在范围内
 * @throw std::out_of_range 如果索引越界
 */
bool check_bounds(int index, int max_index, const std::string& function_name);

} // namespace MatlabError 