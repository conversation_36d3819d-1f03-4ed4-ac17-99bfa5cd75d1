#include "../include/create_source2d.hpp"
#include "../include/common_types.hpp"
#include "../include/bspln.hpp"
#include "../include/Get_Knot_Vector.hpp"
#include <cmath>

/**
 * @brief 创建一个空的震源结构体，与MATLAB中create_source2d()对应
 * @return 初始化的空震源结构
 */
SourceStruct create_empty_source2d() 
{
    SourceStruct source;
    // 清空所有字段，保持与MATLAB版本一致
    source.iom = -1;  // 无效索引
    source.xlocal = 0.0;
    source.zlocal = 0.0;
    source.amp = 0.0;
    source.f0 = 0.0;
    // 其他字段由Matrix/Vector构造函数初始化为空
    
    return source;
}

SourceStruct create_source2d(Integer iom, Real xlocal, Real zlocal,
                           const std::vector<Domain2dA>& OM, Real amp, Real f0,
                           Real dt, Integer nt)
{
    SourceStruct S;
    S.iom = iom;
    S.xlocal = xlocal;
    S.zlocal = zlocal;
    S.amp = amp;
    S.f0 = f0;
    
    // 检查域索引是否有效
    if (iom < 0 || iom >= static_cast<Integer>(OM.size())) {
        // 返回一个空源，因为域索引无效
        return create_empty_source2d();
    }
    
    // 引用源所在的域
    const Domain2dA& dom = OM[iom];
    
    // 获取基函数参数
    Integer Nx1 = dom.Nx1;
    Integer Nx2 = Nx1 - 1;
    Integer Nz1 = dom.Nz1;
    Integer Nz2 = Nz1 - 1;
    
    Integer px1 = dom.px1;
    Integer px2 = px1 - 1;
    Integer pz1 = dom.pz1;
    Integer pz2 = pz1 - 1;
    
    Integer kx1 = px1 + 1;
    Integer kz1 = pz1 + 1;
    Integer kx2 = px2 + 1;
    Integer kz2 = pz2 + 1;
    
    // 获取节点向量
    Vector tx1 = Get_Knot_Vector(Nx1, kx1);
    Vector tx2 = Get_Knot_Vector(Nx2, kx2);
    Vector tz1 = Get_Knot_Vector(Nz1, kz1);
    Vector tz2 = Get_Knot_Vector(Nz2, kz2);
    
    // 计算基函数在源位置的值
    Vector sgbx1(Nx1);
    Vector sgbx2(Nx2);
    Vector sgbz1(Nz1);
    Vector sgbz2(Nz2);
    
    // 注意：在MATLAB中，索引是从1开始的，所以这里我们需要+1
    for (Integer i = 0; i < Nx1; ++i) {
        sgbx1[i] = bspln(tx1, Nx1, i+1, kx1, xlocal);
    }
    for (Integer i = 0; i < Nx2; ++i) {
        sgbx2[i] = bspln(tx2, Nx2, i+1, kx2, xlocal);
    }
    for (Integer j = 0; j < Nz1; ++j) {
        sgbz1[j] = bspln(tz1, Nz1, j+1, kz1, zlocal);
    }
    for (Integer j = 0; j < Nz2; ++j) {
        sgbz2[j] = bspln(tz2, Nz2, j+1, kz2, zlocal);
    }
    
    // 应用逆质量矩阵
    Vector sgbx_inv1(Nx1);
    Vector sgbz_inv2(Nz2);
    Vector sgbx_inv2(Nx2);
    Vector sgbz_inv1(Nz1);
    
    // 手动计算矩阵乘法，确保与MATLAB结果一致
    for (Integer i = 0; i < Nx1; ++i) {
        sgbx_inv1[i] = 0;
        for (Integer j = 0; j < Nx1; ++j) {
            sgbx_inv1[i] += dom.invLx11(i, j) * sgbx1[j];
        }
    }
    
    for (Integer i = 0; i < Nz2; ++i) {
        sgbz_inv2[i] = 0;
        for (Integer j = 0; j < Nz2; ++j) {
            sgbz_inv2[i] += dom.invLz22(i, j) * sgbz2[j];
        }
    }
    
    for (Integer i = 0; i < Nx2; ++i) {
        sgbx_inv2[i] = 0;
        for (Integer j = 0; j < Nx2; ++j) {
            sgbx_inv2[i] += dom.invLx22(i, j) * sgbx2[j];
        }
    }
    
    for (Integer i = 0; i < Nz1; ++i) {
        sgbz_inv1[i] = 0;
        for (Integer j = 0; j < Nz1; ++j) {
            sgbz_inv1[i] += dom.invLz11(i, j) * sgbz1[j];
        }
    }
    
    // 创建影响矩阵
    Matrix invMsg12(Nx1, Nz2);
    Matrix invMsg21(Nx2, Nz1);
    
    for (Integer i = 0; i < Nx1; ++i) {
        for (Integer j = 0; j < Nz2; ++j) {
            invMsg12(i, j) = sgbx_inv1[i] * sgbz_inv2[j];
        }
    }
    
    for (Integer i = 0; i < Nx2; ++i) {
        for (Integer j = 0; j < Nz1; ++j) {
            invMsg21(i, j) = sgbx_inv2[i] * sgbz_inv1[j];
        }
    }
    
    S.invMsg12 = invMsg12;
    S.invMsg21 = invMsg21;
    
    // 创建震源时间函数（Ricker子波）
    Vector sig(nt);
    Real t0 = 1.5 / f0; // Ricker子波的时间偏移
    for (Integer it = 0; it < nt; ++it) {
        Real t = it * dt - t0;
        Real re = PI * f0 * t;
        sig[it] = amp * (1.0 - 2.0 * re * re) * std::exp(-(re * re));
    }
    S.signal = sig;
    
    return S;
} 