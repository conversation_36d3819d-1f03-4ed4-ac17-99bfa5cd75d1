function OM = gen_DFDMatrices(OM)
    ord_gi = 5; %order of gaussina interpolation;

    ndomains = length(OM);
    for iom = 1:ndomains
        
        x1  = OM(iom).model1d.xa(1);
        x2  = OM(iom).model1d.xa(end);
        Nx1 = OM(iom).model1d.Nx1; 
        px  = OM(iom).model1d.px;
        
        fprintf("...preparing the DFD Matrices of domain %d with %d points ... \n",iom,Nx1);
        Nx2 = Nx1 - 1;
        kx1 = px + 1;
        kx2 = kx1 -1;

        t1 = Get_Knot_Vector(Nx1,kx1)*(x2-x1) + x1;
        t2 = Get_Knot_Vector(Nx2,kx2)*(x2-x1) + x1;

        NB_intervals = Nx1 - px;
        xintervals=zeros(NB_intervals,2);

        xintervals(:,1) = t1((px+1):Nx1);
        xintervals(:,2) = t1((px+2):Nx1+1);

        
        xint = zeros(NB_intervals,ord_gi);
        wint = zeros(NB_intervals,ord_gi);
        for kd = 1:NB_intervals
            [xint(kd,:),wint(kd,:)]=lgwt(ord_gi,xintervals(kd,1),xintervals(kd,2));
        end

        
        % kk12 = zeros(Nx1,Nx2);
        % kk21 = zeros(Nx2,Nx1);
        % 
        % mm11 = zeros(Nx1,Nx1);
        % mm22 = zeros(Nx2,Nx2);

        b1   = zeros(Nx1,Nx1);
        for i = 1:Nx1
            for j = 1:Nx1
                xj      = x1 + (j-1)*(x2-x1)/(Nx1 - 1);
                b1(i,j) = bspln(t1,Nx1,i,kx1,xj);
            end
        end
        b1T = b1';
        
        % for ib1 = 1:Nx1 %b1
        %     for jb2 = 1:Nx2 %b2
        %         for kd = 1:NB_intervals
        %             for lpt = 1: ord_gi
        %                 db1tmp = dbspln(t1,Nx1,ib1,kx1,xint(kd,lpt),1);
        %                  b2tmp =  bspln(t2,Nx2,jb2,kx2,xint(kd,lpt));
        %                 kk12(ib1,jb2) = kk12(ib1,jb2) + db1tmp*b2tmp*wint(kd,lpt);
        %             end
        %         end
        %     end
        % end
        option=0;
        kk12 = stiffness_matrix(px,Nx1,t1,t2,xint,wint,option);

        % for ib2 = 1:Nx2 %b2
        %     for jb1 = 1:Nx1 %b1
        %         for kd = 1:NB_intervals
        %             for lpt = 1: ord_gi
        %                 db2tmp = dbspln(t2,Nx2,ib2,kx2,xint(kd,lpt),1);
        %                 b1tmp =  bspln(t1,Nx1,jb1,kx1,xint(kd,lpt));
        %                 kk21(ib2,jb1) = kk21(ib2,jb1) + db2tmp*b1tmp*wint(kd,lpt);
        %             end
        %         end
        %     end
        % end
        option=1;
        kk21 = stiffness_matrix(px,Nx1,t1,t2,xint,wint,option);


        % for ib1 = 1:Nx1 %b1
        %     for jb1 = 1:Nx1 %b1
        %         for kd = 1:NB_intervals
        %             for lpt = 1: ord_gi
        %                 b1tmp1 = bspln(t1,Nx1,ib1,kx1,xint(kd,lpt));
        %                 b1tmp2 = bspln(t1,Nx1,jb1,kx1,xint(kd,lpt));
        %                 mm11(ib1,jb1) = mm11(ib1,jb1) + b1tmp1*b1tmp2*wint(kd,lpt);
        %             end
        %         end
        %     end
        % end
        option = 0;
        mm11 = mass_matrix(px,Nx1,Nx1,t1,t1,xint,wint,option);

        % for ib2 = 1:Nx2 %b2
        %     for jb2 = 1:Nx2 %b2
        %         for kd = 1:NB_intervals
        %             for lpt = 1: ord_gi
        %                 b2tmp1 = bspln(t2,Nx2,ib2,kx2,xint(kd,lpt));
        %                 b2tmp2 = bspln(t2,Nx2,jb2,kx2,xint(kd,lpt));
        %                 mm22(ib2,jb2) = mm22(ib2,jb2) + b2tmp1*b2tmp2*wint(kd,lpt);
        %             end
        %         end
        %     end
        % end
        option = 1;
        mm22 = mass_matrix(px,Nx2,Nx2,t2,t2,xint,wint,option);

        % L11T    = chol(mm11);
        L11T    = sqrtm(mm11); % upper triangle
        L11     = L11T';      % lower triangle

        % L22T    = chol(mm22);
        L22T    = sqrtm(mm22);
        L22     = L22T';

        %
        OM(iom).b1T  = b1T;
        OM(iom).kk12 = kk12;
        OM(iom).kk21 = kk21;

        OM(iom).invL11  = inv(L11);
        OM(iom).invL22  = inv(L22);
        OM(iom).invLT11 = inv(L11T);
        OM(iom).invLT22 = inv(L22T);
    end

end