d:\try2\1\wave1d\wave1d\x64\release\vc143.pdb
d:\try2\1\wave1d\wave1d\x64\release\mass_matrix.obj
d:\try2\1\wave1d\wave1d\x64\release\lgwt.obj
d:\try2\1\wave1d\wave1d\x64\release\inner_product.obj
d:\try2\1\wave1d\wave1d\x64\release\initialize_domain1d.obj
d:\try2\1\wave1d\wave1d\x64\release\gllnodes.obj
d:\try2\1\wave1d\wave1d\x64\release\get_receiver1d.obj
d:\try2\1\wave1d\wave1d\x64\release\get_bandwidth.obj
d:\try2\1\wave1d\wave1d\x64\release\gen_dfdmatrices.obj
d:\try2\1\wave1d\wave1d\x64\release\find_neighbors.obj
d:\try2\1\wave1d\wave1d\x64\release\compute_ku1d.obj
d:\try2\1\wave1d\wave1d\x64\release\compute_ks1d.obj
d:\try2\1\wave1d\wave1d\x64\release\compute_dt.obj
d:\try2\1\wave1d\wave1d\x64\release\compute_boundary_uvalue_out1d.obj
d:\try2\1\wave1d\wave1d\x64\release\compute_boundary_uvalue_inn1d.obj
d:\try2\1\wave1d\wave1d\x64\release\compute_boundary_svalue_out1d.obj
d:\try2\1\wave1d\wave1d\x64\release\compute_boundary_svalue_inn1d.obj
d:\try2\1\wave1d\wave1d\x64\release\check_stability.obj
d:\try2\1\wave1d\wave1d\x64\release\bspln.obj
d:\try2\1\wave1d\wave1d\x64\release\add_source1d.obj
d:\try2\1\wave1d\wave1d\x64\release\update_wavefields1d.obj
d:\try2\1\wave1d\wave1d\x64\release\stiffness_matrix.obj
d:\try2\1\wave1d\wave1d\x64\release\solve1d.obj
d:\try2\1\wave1d\wave1d\x64\release\save_waveforms1d.obj
d:\try2\1\wave1d\wave1d\x64\release\save_wavefields1d.obj
d:\try2\1\wave1d\wave1d\x64\release\mesh_sphere1d.obj
d:\try2\1\wave1d\wave1d\x64\release\main1d.obj
d:\try2\1\wave1d\x64\release\wave1d.exe
d:\try2\1\wave1d\x64\release\wave1d.pdb
d:\try2\1\wave1d\wave1d\x64\release\wave1d.ipdb
d:\try2\1\wave1d\wave1d\x64\release\wave1d.iobj
d:\try2\1\wave1d\wave1d\x64\release\wave1d.tlog\cl.command.1.tlog
d:\try2\1\wave1d\wave1d\x64\release\wave1d.tlog\cl.items.tlog
d:\try2\1\wave1d\wave1d\x64\release\wave1d.tlog\cl.read.1.tlog
d:\try2\1\wave1d\wave1d\x64\release\wave1d.tlog\cl.write.1.tlog
d:\try2\1\wave1d\wave1d\x64\release\wave1d.tlog\link.command.1.tlog
d:\try2\1\wave1d\wave1d\x64\release\wave1d.tlog\link.read.1.tlog
d:\try2\1\wave1d\wave1d\x64\release\wave1d.tlog\link.secondary.1.tlog
d:\try2\1\wave1d\wave1d\x64\release\wave1d.tlog\link.write.1.tlog
