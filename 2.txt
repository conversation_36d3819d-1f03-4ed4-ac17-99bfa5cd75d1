1. 初始化阶段 (main2dA.m)
参数设置:
定义模拟区域的半径数组(rads)
设置每波长点数(ppw)、频率(freq)、多项式阶数(p)
设置网格间距(dx)和模拟持续时间(duration)
设置分区数量(nparts)
   OM = mesh_sphere2dA(rads,dx,nparts,freq,ppw);
   创建球形(圆形)计算域
根据半径和网格间距划分各个区域
为每个区域分配几何和材料属性
可视化计算域   plot_domain2d(OM);
建立连接关系   OM = find_connections2dA(OM);
模型细化    OM = refine_model2dA(OM);
计算时间步长    [dt,nt] = compute_dt2dA(OM,duration);
根据CFL条件计算稳定的时间步长
确定总时间步数
生成差分矩阵   OM = gen_DFDMatrices2dA(OM);
创建质量矩阵和刚度矩阵
生成B样条基函数和导数
初始化计算域:   OM = initializedomain2dA(OM,nt);
初始化位移场、应力场和边界值
为时间积分分配存储空间
设置震源   [sour] = get_source2dA(OM,freq,dt,nt);
设置接收器:    [rece] = get_receiver2dA(OM,dt,nt);
定义接收器位置
初始化波形记录存储
2. 主计算循环 (solver2dA.m) 
[OM,rece] = solver2dA(OM,sour,rece,dt,nt);
循环执行以下步骤:
更新波场:   OM = update_wavefields2dA(OM,dt);
Apply to 2.txt
使用中心差分法更新位移场
计算内部边界位移:   OM = compute_boundary_Uvalue_inn2dA(OM);
Apply to 2.txt
从当前位移场中提取域内部边界的位移值
计算外部边界位移:   OM = compute_boundary_Uvalue_out2dA(OM);
Apply to 2.txt
在相邻域之间传递边界位移值
计算应力场:   OM = compute_KU2dA(OM);
Apply to 2.txt
从位移场计算应力场
应用几何变换和材料属性
计算内部边界应力:   OM = compute_boundary_Svalue_inn2dA(OM);
Apply to 2.txt
从当前应力场中提取域内部边界的应力值
计算外部边界应力:
Apply to 2.txt
在相邻域之间传递边界应力值
计算加速度场:   OM = compute_KS2dA(OM);
Apply to 2.txt
根据应力场计算加速度
应用逆质量矩阵
施加震源:   OM = add_source2dA(OM,source,it);
Apply to 2.txt
将当前时间步的震源信号注入到计算域中
保存波形数据:   rece = save_waveforms2dA(OM,rece,it);
Apply to 2.txt
在接收器位置记录位移值
定期保存和可视化波场:    if mod(it,50)==0
        OM = save_wavefields2dA(OM,it);
        plot_wavefields2dA(OM,it);
    end
Apply to 2.txt
每隔50个时间步保存一次完整波场
可视化当前波场分布
3. 输出阶段 (main2dA.m结尾)
保存参考数据:   dom = OM(1).state;
   outDir = fullfile('..','ware','tests','full_reference');
   dlmwrite(fullfile(outDir,'U12.txt'), dom.U12, 'delimiter',' ');
   dlmwrite(fullfile(outDir,'U21.txt'), dom.U21, 'delimiter',' ');
   dlmwrite(fullfile(outDir,'Sxx11.txt'), dom.Sxx11, 'delimiter',' ');
   dlmwrite(fullfile(outDir,'Szz11.txt'), dom.Szz11, 'delimiter',' ');
Apply to 2.txt
输出参考波场数据供C++版本比较验证
4. 关键计算模块详解
位移到应力的转换 (compute_KU2dA.m)
从正交基到B样条基的转换
应用差分矩阵计算导数
施加边界条件
转换回正交基
应用坐标变换
应用材料属性计算应力
应力到加速度的转换 (compute_KS2dA.m)
应用差分矩阵计算应力导数
处理边界条件
应用逆质量矩阵计算加速度
波场更新 (update_wavefields2dA.m)
使用Leap-frog时间积分格式
U(t+dt) = 2U(t) - U(t-dt) + dt^2acc(t)
波场可视化与输出 (save_wavefields2dA.m, plot_wavefields2dA.m)
从计算网格到物理网格的变换
平均不同网格上的位移场
可视化或保存结果
这个计算流程实现了基于交错网格有限差分法的弹性波模拟，能够准确处理复杂几何形状和材料分布下的波场传播。