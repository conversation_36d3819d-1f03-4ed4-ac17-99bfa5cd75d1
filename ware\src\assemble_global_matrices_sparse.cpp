#include "../include/global_assembly_sparse.hpp"
#include "../include/common_types.hpp"

// local Kronecker product (dense) – small matrices only
static Matrix kron(const Matrix &A,const Matrix &B){
    Matrix K(A.rows*B.rows, A.cols*B.cols);
    for(Integer i=0;i<A.rows;++i)
        for(Integer j=0;j<A.cols;++j){
            Real a=A(i,j);
            for(Integer p=0;p<B.rows;++p)
                for(Integer q=0;q<B.cols;++q)
                    K(i*B.rows+p, j*B.cols+q) = a*B(p,q);
        }
    return K;
}

SparseSystem assemble_global_matrices_sparse(const std::vector<Domain2dA>& OM)
{
    std::cout << "DEBUG: Computing total system size..." << std::endl;
    // total size
    Integer total=0;
    for(size_t i = 0; i < OM.size(); ++i) {
        Integer domain_size = OM[i].mm11.rows;
        std::cout << "DEBUG: Domain " << i << " size: " << domain_size << std::endl;
        total += domain_size;
    }
    std::cout << "DEBUG: Total system size: " << total << std::endl;

    CSR M(total,total), K(total,total);

    Integer offset=0;
    for(size_t dom_idx = 0; dom_idx < OM.size(); ++dom_idx) {
        const auto &dom = OM[dom_idx];
        std::cout << "DEBUG: Processing domain " << dom_idx << "..." << std::endl;

        Integer n = dom.mm11.rows;
        std::cout << "DEBUG: Domain " << dom_idx << " matrix sizes - Mx11: "
                  << dom.Mx11.rows << "x" << dom.Mx11.cols << std::endl;

        const Matrix &Mx = dom.Mx11;
        const Matrix &Mz = dom.Mz11;
        const Matrix &Kx = dom.Kx11;
        const Matrix &Kz = dom.Kz11;
        std::cout << "DEBUG: Computing Kronecker products for domain " << dom_idx << "..." << std::endl;
        Matrix Mblock = kron(Mz, Mx);
        std::cout << "DEBUG: Mblock computed, size: " << Mblock.rows << "x" << Mblock.cols << std::endl;

        Matrix Kblock = kron(Mz, Kx);
        std::cout << "DEBUG: Kblock computed, size: " << Kblock.rows << "x" << Kblock.cols << std::endl;

        Matrix tmp = kron(Kz, Mx);
        std::cout << "DEBUG: tmp matrix computed, adding to Kblock..." << std::endl;

        for(Integer i=0;i<Kblock.rows;++i)
            for(Integer j=0;j<Kblock.cols;++j)
                Kblock(i,j)+=tmp(i,j);

        std::cout << "DEBUG: Assembling sparse matrices for domain " << dom_idx << "..." << std::endl;
        // push rows
        for(Integer r=0;r<n;++r){
            Integer globalRow = offset + r;
            for(Integer c=0;c<n;++c){
                Real mv = Mblock(r,c);
                if(mv!=0.0) M.push_back(offset+c, mv);
                Real kv = Kblock(r,c);
                if(kv!=0.0) K.push_back(offset+c, kv);
            }
            M.start_row(); K.start_row();
        }
        offset += n;
        std::cout << "DEBUG: Domain " << dom_idx << " assembly complete." << std::endl;
    }
    M.finish(); K.finish();
    return {std::move(M), std::move(K)};
} 