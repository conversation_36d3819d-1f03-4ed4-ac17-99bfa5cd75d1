#include "../include/mass_matrix.hpp"
#include "../include/bspln.hpp"
#include "../include/dbspln.hpp"

// Numerical mass (or stiffness) matrix assembly using 1-D Gauss nodes/weights
// der=0 → ∫ B_i B_j ; der=1 → ∫ B'_i B'_j  etc.

Matrix mass_matrix(Integer p,
                   Integer N1, Integer N2,
                   const Vector& t1, const Vector& t2,
                   const Matrix& xint, const Matrix& wint,
                   int der)
{
    Integer NB_intervals = xint.rows; // rows correspond to element intervals
    Integer nGauss       = xint.cols;

    Matrix M(N1, N2, 0.0);

#ifdef USE_EIGEN
    // Convert to Eigen for faster outer-product accumulation
    Eigen::MatrixXd MEig = Eigen::MatrixXd::Zero(N1, N2);

    Eigen::VectorXd bi(N1);
    Eigen::VectorXd bj(N2);

    for(Integer kd = 0; kd < NB_intervals; ++kd){
        for(Integer g = 0; g < nGauss; ++g){
            Real x  = xint(kd, g);
            Real w  = wint(kd, g);

            // Fill basis vectors
            for(Integer i = 1; i <= N1; ++i){
                bi(i-1) = (der==0) ? bspln(t1, N1, i, p+1, x)
                                   : dbspln(t1, N1, i, p+1, x, der);
            }
            for(Integer j = 1; j <= N2; ++j){
                bj(j-1) = (der==0) ? bspln(t2, N2, j, p+1, x)
                                   : dbspln(t2, N2, j, p+1, x, der);
            }

            MEig += w * bi * bj.transpose(); // rank-1 outer product
        }
    }

    // Convert back to custom Matrix
    for(Integer i=0;i<N1;++i)
        for(Integer j=0;j<N2;++j)
            M(i,j) = MEig(i,j);
#else
    // Fallback scalar implementation
    for(Integer kd = 0; kd < NB_intervals; ++kd){
        for(Integer g = 0; g < nGauss; ++g){
            Real x  = xint(kd, g);
            Real w  = wint(kd, g);

            for(Integer i=1;i<=N1;++i){
                Real bi = (der==0) ? bspln(t1, N1, i, p+1, x)
                                   : dbspln(t1, N1, i, p+1, x, der);
                if(std::abs(bi) < 1e-14) continue;
                for(Integer j=1;j<=N2;++j){
                    Real bj = (der==0) ? bspln(t2, N2, j, p+1, x)
                                       : dbspln(t2, N2, j, p+1, x, der);
                    if(std::abs(bj) < 1e-14) continue;
                    M(i-1,j-1) += bi * bj * w;
                }
            }
        }
    }
#endif

    return M;
} 