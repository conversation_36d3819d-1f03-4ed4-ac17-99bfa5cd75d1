function [dt,nt] = compute_dt(OM,duration)
    dt  = 9999999;
    CFL_condition = 0.18;
    for iom = 1:length(OM)
        x1   = OM(iom).model1d.xa(1);
        x2   = OM(iom).model1d.xa(end);
        Nx   = OM(iom).model1d.Nx1;
        
        Vmax = max( OM(iom).model1d.Vp(:) );
        tmp  = CFL_condition*(x2-x1)/(Nx-1)/Vmax; 
        dt = min(dt,tmp);
    end
    nt = ceil(duration/dt);
    fprintf("Time step and number of time steps are: %d, %d,...\n",dt,nt)
end

