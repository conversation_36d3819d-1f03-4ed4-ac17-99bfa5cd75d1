#pragma once
#include "mesh_sphere2dA.hpp"
#include <vector>

/**
 * @brief Create a source structure, implementing MATLAB version functionality
 * @param iom Index of the domain containing the source
 * @param xlocal Source x-coordinate in local coordinate system [0,1]
 * @param zlocal Source z-coordinate in local coordinate system [0,1]
 * @param OM Array of domain structures, used to get domain parameters and calculate source influence matrix
 * @param amp Source amplitude
 * @param f0 Source dominant frequency (Hz)
 * @param dt Time step
 * @param nt Number of time steps
 * @return Initialized source structure
 */
SourceStruct create_source2d(Integer iom, Real xlocal, Real zlocal, 
                           const std::vector<Domain2dA>& OM, Real amp, Real f0,
                           Real dt, Integer nt);

/**
 * @brief Create an empty source structure, corresponding to create_source2d() in MATLAB
 * @return Initialized empty source structure
 */
SourceStruct create_empty_source2d(); 