#include "stiffness_matrix.h"
#include "bspln.h"
#include "dbspln.h"
#include <vector>
#include <algorithm>
#include <cstddef>
#include <cmath>

std::vector<std::vector<double>> stiffness_matrix(
    int p,
    int N1,
    int N2, 
    const std::vector<double>& tx1,
    const std::vector<double>& tx2,
    const std::vector<std::vector<double>>& xint,
    const std::vector<std::vector<double>>& wxint,
    int option)
{
    int k  = p + 1;
    std::size_t NB_intervals = xint.size();
    std::size_t ord_gi       = NB_intervals ? xint[0].size() : 0;

    auto calculate_val = [&](int ib1, int jb1, int opt) {
        double val = 0.0;
        int M1 = N1 - 1;
        for (size_t kd = 0; kd < NB_intervals; ++kd) {
            for (size_t lpt = 0; lpt < ord_gi; ++lpt) {
                double xi = xint[kd][lpt];
                double w = wxint[kd][lpt];
                double b1tmp1 = 0.0, b1tmp2 = 0.0;
                if (opt == 0) {
                    b1tmp1 = dbspln(tx1, N1, ib1, k, xi, 1);
                    b1tmp2 = bspln(tx2, N2, jb1, k - 1, xi);
                } else {
                    b1tmp1 = dbspln(tx2, M1, ib1, k - 1, xi, 1);
                    b1tmp2 = bspln(tx1, N1, jb1, k, xi);
                }
                if (std::isfinite(b1tmp1) && std::isfinite(b1tmp2))
                    val += b1tmp1 * b1tmp2 * w;
            }
        }
        return val;
    };

    if (option == 0) {
        std::vector<std::vector<double>> kk(N1, std::vector<double>(N2, 0.0));

        for (int ib1 = 1; ib1 <= 2 * p; ++ib1) {
            for (int jb1 = 1; jb1 <= 3 * p; ++jb1) {
                 if ((ib1 - 1) < N1 && (jb1 - 1) < N2)
                    kk[ib1 - 1][jb1 - 1] = calculate_val(ib1, jb1, 0);
            }
        }

        for (int i = 2 * p + 1; i <= N1 - 2 * p; ++i) {
             for (int j_offset = 0; j_offset <= 2 * p; ++j_offset) {
                int j_dest = i - p + j_offset;
                int j_src = j_dest - 1;
                 if ((i - 1) < N1 && (j_dest - 1) >= 0 && (j_dest - 1) < N2 && (i-2) >= 0 && (j_src-1) >=0 && (j_src - 1) < N2) {
                   kk[i - 1][j_dest - 1] = kk[i - 2][j_src - 1];
                }
            }
        }

        for (int ib1 = N1 - 2 * p + 1; ib1 <= N1; ++ib1) {
            for (int jb1 = N1 - 3 * p; jb1 <= N2; ++jb1) {
                 if ((ib1 - 1) < N1 && (jb1 - 1) >= 0 && (jb1 - 1) < N2)
                    kk[ib1 - 1][jb1 - 1] = calculate_val(ib1, jb1, 0);
            }
        }
        return kk;

    } else { 
        int M1 = N1 - 1;
        std::vector<std::vector<double>> kk(M1, std::vector<double>(N1, 0.0));

      
        for (int ib1 = 1; ib1 <= 2 * p; ++ib1) {
            for (int jb1 = 1; jb1 <= 3 * p; ++jb1) {
                if((ib1-1) < M1 && (jb1-1) < N1)
                    kk[ib1 - 1][jb1 - 1] = calculate_val(ib1, jb1, 1);
            }
        }

        for (int i = 2 * p + 1; i <= N1 - 2 * p; ++i) {
            if (i > M1) continue;
            for (int j_offset = 0; j_offset <= 2 * p; ++j_offset) {
                int j_dest = i - p + j_offset;
                int j_src = j_dest - 1;
                if ((i - 1) < M1 && (j_dest - 1) >=0 && (j_dest - 1) < N1 && (i-2) >= 0 && (j_src-1) >=0 && (j_src - 1) < N1) {
                    kk[i - 1][j_dest - 1] = kk[i - 2][j_src - 1];
                }
            }
        }
        
  
        for (int ib1 = N1 - 2 * p + 1; ib1 <= M1; ++ib1) {
            for (int jb1 = N1 - 3 * p; jb1 <= N1; ++jb1) {
                if((ib1-1) < M1 && (jb1-1) >= 0 && (jb1-1) < N1)
                    kk[ib1 - 1][jb1 - 1] = calculate_val(ib1, jb1, 1);
            }
        }
        return kk;
    }
}