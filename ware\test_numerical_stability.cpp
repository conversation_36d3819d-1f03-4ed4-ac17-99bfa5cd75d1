/**
 * @file test_numerical_stability.cpp
 * @brief 测试数值稳定性改进，验证MATLAB风格的病态矩阵处理
 */

#include "include/common_types.hpp"
#include "include/eigen_wrapper.hpp"
#include <iostream>
#include <iomanip>

using namespace EigenWrapper;

void test_ill_conditioned_matrix() {
    std::cout << "\n=== 测试病态矩阵求逆 ===" << std::endl;
    
    // 创建一个高条件数的矩阵（类似MATLAB中的hilb函数）
    Integer n = 5;
    Matrix A(n, n);
    
    // Hilbert矩阵：A(i,j) = 1/(i+j+1)，这是一个著名的病态矩阵
    for (Integer i = 0; i < n; ++i) {
        for (Integer j = 0; j < n; ++j) {
            A(i, j) = 1.0 / (i + j + 1);
        }
    }
    
    std::cout << "原始Hilbert矩阵 (5x5):" << std::endl;
    for (Integer i = 0; i < n; ++i) {
        for (Integer j = 0; j < n; ++j) {
            std::cout << std::setw(12) << std::setprecision(6) << A(i, j) << " ";
        }
        std::cout << std::endl;
    }
    
    try {
        std::cout << "\n尝试求逆..." << std::endl;
        Matrix A_inv = inv(A);
        
        std::cout << "求逆成功！逆矩阵:" << std::endl;
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                std::cout << std::setw(12) << std::setprecision(6) << A_inv(i, j) << " ";
            }
            std::cout << std::endl;
        }
        
        // 验证 A * A_inv ≈ I
        Matrix product = matmul(A, A_inv);
        std::cout << "\n验证 A * A_inv (应该接近单位矩阵):" << std::endl;
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                std::cout << std::setw(12) << std::setprecision(6) << product(i, j) << " ";
            }
            std::cout << std::endl;
        }
        
        // 计算误差
        Real max_error = 0.0;
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                Real expected = (i == j) ? 1.0 : 0.0;
                Real error = std::abs(product(i, j) - expected);
                max_error = std::max(max_error, error);
            }
        }
        std::cout << "\n最大误差: " << max_error << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "求逆失败: " << e.what() << std::endl;
    }
}

void test_near_singular_matrix() {
    std::cout << "\n=== 测试接近奇异的矩阵 ===" << std::endl;
    
    Integer n = 3;
    Matrix A(n, n);
    
    // 创建一个接近奇异的矩阵
    A(0, 0) = 1.0;  A(0, 1) = 2.0;  A(0, 2) = 3.0;
    A(1, 0) = 4.0;  A(1, 1) = 5.0;  A(1, 2) = 6.0;
    A(2, 0) = 7.0;  A(2, 1) = 8.0;  A(2, 2) = 9.0 + 1e-12; // 几乎线性相关
    
    std::cout << "接近奇异的矩阵:" << std::endl;
    for (Integer i = 0; i < n; ++i) {
        for (Integer j = 0; j < n; ++j) {
            std::cout << std::setw(15) << std::setprecision(10) << A(i, j) << " ";
        }
        std::cout << std::endl;
    }
    
    try {
        std::cout << "\n尝试求逆..." << std::endl;
        Matrix A_inv = inv(A);
        std::cout << "求逆成功（使用了容错机制）" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "求逆失败: " << e.what() << std::endl;
    }
}

void test_singular_matrix() {
    std::cout << "\n=== 测试完全奇异的矩阵 ===" << std::endl;
    
    Integer n = 3;
    Matrix A(n, n);
    
    // 创建一个完全奇异的矩阵（第三行是前两行的线性组合）
    A(0, 0) = 1.0;  A(0, 1) = 2.0;  A(0, 2) = 3.0;
    A(1, 0) = 4.0;  A(1, 1) = 5.0;  A(1, 2) = 6.0;
    A(2, 0) = 5.0;  A(2, 1) = 7.0;  A(2, 2) = 9.0; // = A(0,:) + A(1,:)
    
    std::cout << "奇异矩阵:" << std::endl;
    for (Integer i = 0; i < n; ++i) {
        for (Integer j = 0; j < n; ++j) {
            std::cout << std::setw(10) << std::setprecision(6) << A(i, j) << " ";
        }
        std::cout << std::endl;
    }
    
    try {
        std::cout << "\n尝试求逆..." << std::endl;
        Matrix A_inv = inv(A);
        std::cout << "求逆成功（使用了伪逆）" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "求逆失败: " << e.what() << std::endl;
    }
}

int main() {
    std::cout << "数值稳定性测试 - MATLAB风格的病态矩阵处理" << std::endl;
    std::cout << "================================================" << std::endl;
    
    test_ill_conditioned_matrix();
    test_near_singular_matrix();
    test_singular_matrix();
    
    std::cout << "\n测试完成。" << std::endl;
    return 0;
}
