function [OM] = mesh_sphere1d(rads,dx,freq,px_order,ppw)

    % % rad= [600.0, 1221.5, 2914.25, 3480.0, 3630.0, 5600.0, 5701.0, 5771.0, 5971.0, 6151.0, 6291.0, 6346.0, 6356.0, 6368.0, 6371.0]*1000;
    
    % rad= [600.0, 1221.5, 2914.25, 3480.0]*1000;
    % dx     = 10000;
    % freq   = 1;
    % px_order = 4;
    % ppw = 3;

    flag_plotting=1;

    fmax = 3*freq;

    len_rad = length(rads);
    allrad  = zeros(1,2*len_rad);

    allrad(1:len_rad)     = -rads(len_rad:-1:1);
    allrad(len_rad+1:end) = rads;

    len_allrad = length(allrad);

    ndomains   = (len_allrad-1);

    OM(ndomains, 1) = create_domain1d();

    % vpmax =-99999999;
    vpmin = 99999999;

    for iom = 1:ndomains

        clear xa1 rho1 Vp1 lambda1;

        r1 = allrad(iom);
        r2 = allrad(iom+1);
        dr = abs(r2-r1);
        nax = max(3, ceil(dr/dx) ); % note that nx here is used for the shape function
        xa1 = (0:nax-1)'*dr/(nax-1) + r1;
        
        % fprintf(" the nunber of points is %d ...\n ", length(xa));
        radius = abs(xa1);
        rmax = max(radius)*(1-0.51*eps);
        rmin = min(radius)*(1+0.51*eps);

        
        rho1    = zeros(nax,1);
        Vp1     = zeros(nax,1);
        lambda1 = zeros(nax,1);
        for ii=1:nax
            r   = min( max(rmin, abs( xa1(ii)) ), rmax );
            % rho = density_prem(r/1000,'c')*1000;
            % Vp  = vp_prem(r/1000,'c')*1000;

            rho = 3000;
            Vp  = 10000;
            
            rho1(ii)    = rho;
            Vp1(ii)     = Vp;
            lambda1(ii) = rho*Vp*Vp;

            vpmin = min(vpmin, Vp);
            % vpmax = min(vpmax, Vp);
        end
        wavemin = vpmin/(fmax);
        Nx1 = max(3,ceil(dr/wavemin*ppw));
        px  = min(Nx1-1,px_order);
        xp1  = 0:1/(Nx1-1):1;

        x1d  = (0:1/(Nx1-1):1)*(r2-r1) + r1;

        % template = model1d(xa1,xp1,rho1,Vp1,lambda1,px,Nx1);
        % template = struct('xa', xa1, 'xp', xp1, 'rho', rho1, 'Vp', Vp1, 'lambda', lambda1, 'px', px, 'Nx1', Nx1);
        % OM(iom).model1d = template;
        OM(iom).model1d.xa     = xa1;
        OM(iom).model1d.x1d    = x1d;
        OM(iom).model1d.xp     = xp1;
        OM(iom).model1d.rho    = rho1;
        OM(iom).model1d.Vp     = Vp1;
        OM(iom).model1d.lambda = lambda1;
        OM(iom).model1d.px     = px;
        OM(iom).model1d.Nx1    = Nx1;

       
        fprintf("... Info1 required for wave simulation is %d with %d points ... \n",iom,OM(iom).model1d.Nx1);
    end
    



    if flag_plotting==1
        figure
        hold on
        for iom = 1:ndomains
            % plot(OM(iom).model1d.rho,OM(iom).model1d.x,'k','LineWidth',1.5);
            plot(OM(iom).model1d.Vp/1e3, OM(iom).model1d.xa/1e3,'k','LineWidth',1.5); hold on
            if iom >1 && iom <ndomains
                x1 = OM(iom).model1d.xa(end)/1e3;
                v1 = OM(iom).model1d.Vp(end)/1e3;

                x2 = OM(iom+1).model1d.xa(1)/1e3;
                v2 = OM(iom+1).model1d.Vp(1)/1e3;

                plot([v1,v2],[x1,x2],'k','LineWidth',1.5);  hold on
            end
            fprintf("... the shape is domain %d with %d points ... \n",iom,length(OM(iom).model1d.xa));
        end
        hold off
        xlabel('Vp (m/s)')
        ylabel('depth (m)')
        ylim([0,max(rads)]/1e3)
        set(gca,'fontsize',15)
    end


end


function density = density_prem(r, opt)
    % Return PREM density profile
    % r : radius (r=0 at the Earth's center)
    
    % Get density coefficients based on radius and option
    coefs = density_coefs_prem(r, opt);
    
    % Normalize radius
    x = r / 6371;
    
    % Calculate density using polynomial
    density = poly3(x, coefs);
end


function f=poly3(x, coefs)
    % 3-order polynomial degree
    f = coefs(1) + coefs(2)*x + coefs(3).*x.*x + coefs(4).*x.*x.*x;
end

function coefs = density_coefs_prem(r, opt)
    % Return PREM density polynomial coefficients
    % r : radius (r=0 at the Earth's center)
    
    % Determine layer index based on radius and option
    n = layer_index_prem(r, opt);
    
    % Initialize coefs array
    coefs = zeros(1, 4);
    
    % Assign coefficients based on layer index
    switch n
        case 1
            coefs = [13.0885, 0.0, -8.8381, 0.0];
        case 2
            coefs = [12.5815, -1.2638, -3.6426, -5.5281];
        case 3
            coefs = [7.9565, -6.4761, 5.5283, -3.0807];
        case 4
            coefs = [7.9565, -6.4761, 5.5283, -3.0807];
        case 5
            coefs = [7.9565, -6.4761, 5.5283, -3.0807];
        case 6
            coefs = [5.3197, -1.4836, 0.0, 0.0];
        case 7
            coefs = [11.2494, -8.0298, 0.0, 0.0];
        case 8
            coefs = [7.1089, -3.8045, 0.0, 0.0];
        case 9
            coefs = [2.6910, 0.6924, 0.0, 0.0];
        case 10
            coefs = [2.6910, 0.6924, 0.0, 0.0];
        case 11
            coefs = [2.9, 0.0, 0.0, 0.0];
        case 12
            coefs = [2.6, 0.0, 0.0, 0.0];
        case 13
            coefs = [1.020, 0.0, 0.0, 0.0];
        otherwise
            warning('Invalid layer index in density_coefs_prem');
    end
end

function coefs = vp_coefs_prem(r, opt)
    % Return PREM isotropic p-wave velocity polynomial coefficients
    % r : radius (r=0 at the Earth's center)
    
    % Determine layer index based on radius and option
    n = layer_index_prem(r, opt);
    
    % Initialize coefficients array
    coefs = zeros(1, 4);
    
    % Assign coefficients based on layer index
    switch n
        case 1
            coefs = [11.2622, 0.0, -6.3640, 0.0];
        case 2
            coefs = [11.0487, -4.0362, 4.8023, -13.5732];
        case 3
            coefs = [15.3891, -5.3181, 5.5242, -2.5514];
        case 4
            coefs = [24.9520, -40.4673, 51.4832, -26.6419];
        case 5
            coefs = [29.2766, -23.6027, 5.5242, -2.5514];
        case 6
            coefs = [19.0957, -9.8672, 0.0, 0.0];
        case 7
            coefs = [39.7027, -32.6166, 0.0, 0.0];
        case 8
            coefs = [20.3926, -12.2569, 0.0, 0.0];
        case 9
            coefs = [4.1875, 3.9382, 0.0, 0.0];
        case 10
            coefs = [4.1875, 3.9382, 0.0, 0.0];
        case 11
            coefs = [6.8, 0.0, 0.0, 0.0];
        case 12
            coefs = [5.8, 0.0, 0.0, 0.0];
        case 13
            coefs = [1.45, 0.0, 0.0, 0.0];
        otherwise
            warning('Invalid layer index in vp_coefs_prem');
    end
end

function layer_index = layer_index_prem(r, opt)
    % Return PREM layer index corresponding to radius r
    % r : radius (r=0 at the Earth's center)
    
    % Define depth interfaces
    depths_interfaces = [0.0, 1221.5, 3480.0, 3630.0, 5600.0, 5701.0, 5771.0, 5971.0, 6151.0, 6291.0, 6346.0, 6356.0, 6368.0, 6371.0];
    
    % Check if radius is within valid range
    if r < depths_interfaces(1) || r > depths_interfaces(end)
        warning('Invalid radius in layer_index_prem');
    end
    
    % Initialize layer index
    layer_index = 13;
    
    % Determine the appropriate layer index based on radius
    for i = 12:-1:1
        if r >= depths_interfaces(i) && r < depths_interfaces(i + 1)
            layer_index = i;
            break;
        end
    end
    
    % Adjust layer index based on option
    switch opt
        case {'C', 'c'}
            if layer_index == 13
                layer_index = 12;
            end
        case {'O', 'o'}
            % No change required for 'O' or 'o'
        otherwise
            warning('Invalid option OPT in layer_index_prem');
    end
end

function vp = vp_prem(r, opt)
    % Return PREM isotropic p-wave velocity profile
    % r : radius (r=0 at the Earth's center)
    
    % % Define coefficients array
    % coefs = zeros(1, 4);
    
    % Normalize radius
    x = r / 6371;
    
    % Get VP coefficients based on radius and option
    coefs = vp_coefs_prem(r, opt);
    
    % Calculate VP using polynomial
    vp = poly3(x, coefs);
end





