function [OM] = compute_KU1d(OM)
%COMPUTE_INTERNAL_FORCES Summary of this function goes here

ndomains = length(OM);
%%%%%%%%%%%%%%%%%%%%%%%
% from displacement to stress
for iom = 1:ndomains
    kk21     = OM(iom).kk21;
    invLT11  = OM(iom).invLT11;
    invL22   = OM(iom).invL22;

    U1       = OM(iom).state.U1;
    Ubval_xm = OM(iom).state.Ubval_xm;
    Ubval_xp = OM(iom).state.Ubval_xp;
    % Ub1      = OM(iom).state.Ub1;

    Ub1    = invLT11*U1;
    dU1dx2 = -kk21*Ub1;

    dU1dx2(1)   = dU1dx2(1)   - Ubval_xm;
    dU1dx2(end) = dU1dx2(end) + Ubval_xp;

    OM(iom).state.dU1dx2 = invL22*dU1dx2;
end
%%%%%%%%%%%%%%%%%%%%%%%%%

%%%%%%%%%%%%%%%%%%%%%%%
% calculate the stress
for iom = 1:ndomains
    dU1dx2 = OM(iom).state.dU1dx2;
    % Vp2   = OM(iom).Vp2;
    Vp2 = 10000;
    S2    = Vp2*Vp2*dU1dx2;
    OM(iom).state.S2 = S2;
end
%%%%%%%%%%%%%%%%%%%%%%%%%

end