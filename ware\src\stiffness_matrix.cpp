#include "../include/stiffness_matrix.hpp"
#include "../include/bspln.hpp"
#include "../include/dbspln.hpp"
#include "../include/mass_matrix.hpp"

Matrix stiffness_matrix(Integer p,Integer N1,
                        const Vector& t1,const Vector& t2,
                        const Matrix& xint,const Matrix& wint,
                        int option){

    Integer kx1 = p + 1;            // order of first basis
    Integer kx2 = kx1 - 1;          // order of second basis (Nx1-1)

    Integer NB_intervals = xint.rows;
    Integer ord_gi       = xint.cols;

    if(option==0){
        Matrix kk(N1, N1-1, 0.0);
#ifdef USE_EIGEN
        Eigen::MatrixXd KEig = Eigen::MatrixXd::Zero(N1, N1-1);
        Eigen::VectorXd dvec(N1);
        Eigen::VectorXd bvec(N1-1);
        for(Integer kd = 0; kd < NB_intervals; ++kd){
            for(Integer lpt = 0; lpt < ord_gi; ++lpt){
                Real x = xint(kd,lpt);
                Real w = wint(kd,lpt);
                // fill dvec and bvec
                for(Integer i=1;i<=N1;++i)
                    dvec(i-1) = dbspln(t1,N1,i,kx1,x,1);
                for(Integer j=1;j<=N1-1;++j)
                    bvec(j-1) = bspln(t2,N1-1,j,kx2,x);

                KEig += w * dvec * bvec.transpose();
            }
        }
        for(Integer i=0;i<N1;++i)
            for(Integer j=0;j<N1-1;++j)
                kk(i,j) = KEig(i,j);
        return kk;
#else
        // scalar fallback
        for(Integer ib=1; ib<=N1; ++ib){
            for(Integer jb=1; jb<=N1-1; ++jb){
                Real sum=0.0;
                for(Integer kd=0; kd<NB_intervals; ++kd){
                    for(Integer lpt=0; lpt<ord_gi; ++lpt){
                        Real x = xint(kd,lpt);
                        Real w = wint(kd,lpt);
                        Real di = dbspln(t1,N1,ib,kx1,x,1);
                        Real  b = bspln(t2,N1-1,jb,kx2,x);
                        sum += di * b * w;
                    }
                }
                kk(ib-1,jb-1) = sum;
            }
        }
        return kk;
#endif
    }else{
        Matrix kk(N1-1, N1, 0.0);
#ifdef USE_EIGEN
        Eigen::MatrixXd KEig = Eigen::MatrixXd::Zero(N1-1, N1);
        Eigen::VectorXd dvec(N1-1);
        Eigen::VectorXd bvec(N1);
        for(Integer kd=0; kd<NB_intervals; ++kd){
            for(Integer lpt=0; lpt<ord_gi; ++lpt){
                Real x = xint(kd,lpt);
                Real w = wint(kd,lpt);
                for(Integer i=1;i<=N1-1;++i)
                    dvec(i-1) = dbspln(t2,N1-1,i,kx2,x,1);
                for(Integer j=1;j<=N1;++j)
                    bvec(j-1) = bspln(t1,N1,j,kx1,x);
                KEig += w * dvec * bvec.transpose();
            }
        }
        for(Integer i=0;i<N1-1;++i)
            for(Integer j=0;j<N1;++j)
                kk(i,j) = KEig(i,j);
        return kk;
#else
        for(Integer ib=1; ib<=N1-1; ++ib){
            for(Integer jb=1; jb<=N1; ++jb){
                Real sum=0.0;
                for(Integer kd=0; kd<NB_intervals; ++kd){
                    for(Integer lpt=0; lpt<ord_gi; ++lpt){
                        Real x = xint(kd,lpt);
                        Real w = wint(kd,lpt);
                        Real di = dbspln(t2,N1-1,ib,kx2,x,1);
                        Real  b = bspln(t1,N1,jb,kx1,x);
                        sum += di * b * w;
                    }
                }
                kk(ib-1,jb-1) = sum;
            }
        }
        return kk;
#endif
    }
} 