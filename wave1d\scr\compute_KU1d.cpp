

#include "compute_KU1d.hpp"
#include "matrix.hpp"
#include <iostream>
#include <algorithm>
#include <cmath>

#ifdef _OPENMP
#include <omp.h>
#endif

namespace wave {

void compute_KU1d(std::vector<Domain1D>& domains)
{
    const int ndomains = static_cast<int>(domains.size());
    if(ndomains == 0) return;

    #ifdef _OPENMP
    #pragma omp parallel for schedule(static) default(none) shared(domains, ndomains)
    #endif
    for(int idx = 0; idx < ndomains; ++idx)
    {
        auto &dom = domains[static_cast<std::size_t>(idx)];

        const Matrix &kk21 = dom.kk21;
        const Matrix &invLT11 = dom.invLT11;
        const Matrix &invL22 = dom.invL22;
        const std::vector<double> &U1 = dom.state.U1;
        const double Ubval_xm = dom.state.Ubval_xm;
        const double Ubval_xp = dom.state.Ubval_xp;

        if(U1.empty() || invLT11.cols == 0 || invLT11.cols != U1.size()) {
            dom.state.dU1dx2.assign(U1.size(), 0.0);
            continue;
        }
        std::vector<double> Ub1(invLT11.rows);
        for(size_t i = 0; i < invLT11.rows; ++i) {
            double sum = 0.0;
            for(size_t j = 0; j < invLT11.cols; ++j) {
                sum += invLT11(i, j) * U1[j];
            }
            Ub1[i] = sum;
        }

        // 第三步: 检查kk21矩阵维度
        if(kk21.cols == 0 || kk21.cols != Ub1.size()) {
            dom.state.dU1dx2.assign(Ub1.size(), 0.0);
            continue;
        }

        // 第四步: 计算位移梯度 dU1dx2 = -kk21 * Ub1
        std::vector<double> dU1dx2(kk21.rows);
        for(size_t i = 0; i < kk21.rows; ++i) {
            double sum = 0.0;
            for(size_t j = 0; j < kk21.cols; ++j) {
                sum += kk21(i, j) * Ub1[j];
            }
            dU1dx2[i] = -sum;  // 添加负号
        }

        // 第五步: 添加边界条件
        if(!dU1dx2.empty()) {
            dU1dx2.front() -= Ubval_xm;  // 左边界
            dU1dx2.back() += Ubval_xp;   // 右边界
        }

        // 第六步: 检查invL22矩阵维度
        if(invL22.cols == 0 || invL22.cols != dU1dx2.size()) {
            dom.state.dU1dx2.assign(dU1dx2.size(), 0.0);
            continue;
        }

        // 第七步: 计算最终应变 dU1dx2_final = invL22 * dU1dx2
        dom.state.dU1dx2.resize(invL22.rows);
        for(size_t i = 0; i < invL22.rows; ++i) {
            double sum = 0.0;
            for(size_t j = 0; j < invL22.cols; ++j) {
                sum += invL22(i, j) * dU1dx2[j];
            }
            // 内联数值稳定性处理
            dom.state.dU1dx2[i] = std::isfinite(sum) ? sum : 0.0;
        }
    }

    // 第二阶段: 并行计算应力场 S2 = Vp² * dU1dx2
    #ifdef _OPENMP
    #pragma omp parallel for schedule(static) default(none) shared(domains, ndomains)
    #endif
    for(int idx = 0; idx < ndomains; ++idx)
    {
        auto &dom = domains[static_cast<std::size_t>(idx)];
        const double Vp2 = dom.Vp2;
        const std::vector<double> &dU1dx2 = dom.state.dU1dx2;

        // 优化: 直接在目标向量上计算
        dom.state.S2.resize(dU1dx2.size());
        const size_t n = dU1dx2.size();

        for(size_t i = 0; i < n; ++i) {
            const double stress_value = Vp2 * dU1dx2[i];
            dom.state.S2[i] = std::isfinite(stress_value) ? stress_value : 0.0;
        }
    }

    // 调试信息输出: 仅在第一次调用时输出最大应变和应力值
    static bool debug_output_once = true;
    if(debug_output_once) {
        double max_strain = 0.0, max_stress = 0.0;
        for(const auto &dom : domains) {
            for(double strain : dom.state.dU1dx2) {
                max_strain = std::max(max_strain, std::fabs(strain));
            }
            for(double stress : dom.state.S2) {
                max_stress = std::max(max_stress, std::fabs(stress));
            }
        }
        std::cout << "[DEBUG] KU1d complete: max strain |dU1dx2| = " << max_strain
                  << ", max stress |S2| = " << max_stress << std::endl;
        debug_output_once = false;
    }
}

} // namespace wave