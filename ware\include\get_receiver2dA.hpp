#pragma once
#include "common_types.hpp"
#include "receiver.hpp"
#include "mesh_sphere2dA.hpp"
#include <vector>

/**
 * @brief Create receiver structures, corresponding to the MATLAB version of get_receiver2dA
 * @param OM Array of domain structures
 * @param dt Time step
 * @param nt Total number of time steps
 * @return Array of receiver structures
 */
std::vector<Receiver2dA> get_receiver2dA(const std::vector<Domain2dA>& OM, 
                                        Real dt, 
                                        Integer nt); 