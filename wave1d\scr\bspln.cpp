﻿#include <cmath>
#include <vector>
#include "bspln.h"
#include <functional>

constexpr double EPS = 1e-12;

double bspln(const std::vector<double>& t,
             int n, int i, int k, double x)
{
    int idx0 = i - 1;
    if(idx0 < 0 || idx0 >= n)                       return 0.0;
    if(idx0 + k >= static_cast<int>(t.size()))      return 0.0;

    constexpr double EPS = 1e-12;

    std::function<double(int,int)> rec = [&](int idx, int kk)->double
    {
        if(kk == 1){
    bool inRange  = (x >  t[idx] + EPS) && (x <= t[idx+1] + EPS);

    bool rightEnd = (std::fabs(x - t.back()) < EPS) && (idx == n - 1);

    return (inRange || rightEnd) ? 1.0 : 0.0;
}
        double c1 = 0.0, c2 = 0.0;
        if(std::fabs(t[idx+kk-1] - t[idx]) > EPS)
            c1 = (x - t[idx]) / (t[idx+kk-1] - t[idx]);
        if(std::fabs(t[idx+kk] - t[idx+1]) > EPS)
            c2 = (t[idx+kk] - x) / (t[idx+kk] - t[idx+1]);
        return c1 * rec(idx    , kk-1) +   
               c2 * rec(idx + 1, kk-1);    
    };

    return rec(idx0, k);
}