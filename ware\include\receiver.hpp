#pragma once
#include "common_types.hpp"
#include <vector>

// Extended receiver structure, consistent with MATLAB version
struct Receiver2dA {
    // Existing fields
    Real x{0.0}, z{0.0};           // Physical coordinates
    Integer iom{0};                // Domain index where receiver is located, using 0-based index (corresponds to MATLAB's 1-based)
    
    // Local coordinates (xlocal and zlocal in MATLAB)
    Real xlocal{0.0}, zlocal{0.0}; // Local parametric coordinates [0,1]
    
    // Grid indices
    Integer ix{0}, iz{0};          // Grid indices (if needed)
    
    // B-spline coefficients (rbx1/rbx2/rbz1/rbz2 in MATLAB)
    Vector rbx1;                   // B-spline coefficients x direction order1
    Vector rbx2;                   // B-spline coefficients x direction order2 
    Vector rbz1;                   // B-spline coefficients z direction order1
    Vector rbz2;                   // B-spline coefficients z direction order2
    
    // Waveform records
    Vector ur;                     // Time history (replaces ur matrix in MATLAB)
    std::vector<Real> trace_u12;   // U12 waveform record
    std::vector<Real> trace_u21;   // U21 waveform record
    
    // Time array for output (corresponds to first column of ur in MATLAB)
    std::vector<Real> time;        // Time points array
}; 