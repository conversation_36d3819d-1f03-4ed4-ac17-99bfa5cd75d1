#include "../include/Get_Knot_Vector.hpp"
#include <limits>

/**
 * @file Get_Knot_Vector.cpp
 * @brief Implementation translating MATLAB Get_Knot_Vector.m
 */

Vector Get_Knot_Vector(Integer n, Integer k) {
    const Real eps_val = 2.220446049250313e-16; // MATLAB eps

    Vector t(n + k);
    for(Integer i = 0; i < n + k; ++i) {
        t(i) = 0.0;
    }

    Real dx = 1.0 / (n + 1 - k);

    for(Integer i = k; i <= n + 1; ++i) {
        t(i - 1) = (i - k) * dx;
    }

    Real left_val  = t(k - 1) - 20.0 * eps_val;
    Real right_val = t(n)     + 20.0 * eps_val;

    for(Integer i = 0; i < k; ++i) {
        t(i) = left_val;
    }
    for(Integer i = n; i < n + k; ++i) {
        t(i) = right_val;
    }

    return t;
} 