/**
 * @file gen_DFDMatrices.cpp
 * @brief 优化的DFD矩阵生成 - 并行版本
 *
 * 功能: 生成差分有限域方法所需的所有矩阵
 * 包括: 刚度矩阵(kk12, kk21)、质量矩阵(mm11, mm22)、逆变换矩阵
 * 优化: OpenMP并行化 + 内存优化 + 数值计算优化
 */

#include "gen_DFDMatrices.hpp"
#include "stiffness_matrix.h"
#include "mass_matrix.h"
#include "Get_Knot_Vector.h"
#include "bspln.h"
#include "dbspln.h"
#include "lgwt.h"
#include "matrix.hpp"
#include <iostream>
#include <fstream>
#include <iomanip>
#include <filesystem>
#include <limits>
#include <string>
#include <algorithm>

#ifdef _OPENMP
#include <omp.h>
#endif

namespace wave {

void gen_DFDMatrices(std::vector<Domain1D>& domains)
{
    const int ord_gi = 5;  // 高斯积分阶数
    const int ndomains = static_cast<int>(domains.size());

    if(ndomains == 0) return;
    // OpenMP并行化: 域级并行，每个线程处理不同的域
    #ifdef _OPENMP
    #pragma omp parallel for schedule(dynamic) default(none) shared(domains, ndomains, ord_gi)
    #endif
    for(int iom_idx = 0; iom_idx < ndomains; ++iom_idx)
    {
        auto &dom = domains[static_cast<std::size_t>(iom_idx)];
        const int Nx1 = dom.model1d.Nx1;

        // 跳过无效域
        if(Nx1 < 3) continue;

        // 优化: 预计算常用参数
        const int px = dom.model1d.px;
        const int kx1 = px + 1;
        const int Nx2 = Nx1 - 1;
        const int kx2 = kx1 - 1;
        const double x1 = dom.model1d.xa.front();
        const double x2 = dom.model1d.xa.back();
        const double domain_length = x2 - x1;

        // 第一步: 生成节点向量
        std::vector<double> t1 = Get_Knot_Vector(Nx1, kx1);
        std::vector<double> t2 = Get_Knot_Vector(Nx2, kx2);

        // 向量化的坐标变换
        const size_t t1_size = t1.size();
        const size_t t2_size = t2.size();
        for(size_t i = 0; i < t1_size; ++i) {
            t1[i] = t1[i] * domain_length + x1;
        }
        for(size_t i = 0; i < t2_size; ++i) {
            t2[i] = t2[i] * domain_length + x1;
        }

        // 第二步: 生成高斯积分节点和权重
        const int NB_intervals = Nx1 - px;
        std::vector<std::vector<double>> xint(NB_intervals, std::vector<double>(ord_gi));
        std::vector<std::vector<double>> wint(NB_intervals, std::vector<double>(ord_gi));

        for(int kd = 0; kd < NB_intervals; ++kd) {
            const double a = t1[px + kd];
            const double b = t1[px + kd + 1];
            auto nodesWeights = lgwt(ord_gi, a, b);
            xint[kd] = std::move(nodesWeights.first);
            wint[kd] = std::move(nodesWeights.second);
        }

        // 第三步: 优化的B样条基函数矩阵计算
        Matrix b1(Nx1, Nx1, 0.0);
        const double dx = domain_length / (Nx1 - 1);

        for(int i = 1; i <= Nx1; ++i) {
            for(int j = 1; j <= Nx1; ++j) {
                const double xj = x1 + (j - 1) * dx;
                b1(i - 1, j - 1) = bspln(t1, Nx1, i, kx1, xj);
            }
        }

        // 优化: 使用移动语义
        dom.b1T = transpose(b1);
        dom.state.b1T = dom.b1T;

        // 第四步: 生成刚度矩阵
        auto kk12_vec = stiffness_matrix(px, Nx1, Nx2, t1, t2, xint, wint, 0);
        auto kk21_vec = stiffness_matrix(px, Nx1, Nx2, t1, t2, xint, wint, 1);

        // 优化: 高效的矩阵数据拷贝
        dom.kk12 = Matrix(Nx1, Nx2, 0.0);
        dom.kk21 = Matrix(Nx2, Nx1, 0.0);

        for(int i = 0; i < Nx1; ++i) {
            for(int j = 0; j < Nx2; ++j) {
                dom.kk12(i, j) = kk12_vec[i][j];
            }
        }
        for(int i = 0; i < Nx2; ++i) {
            for(int j = 0; j < Nx1; ++j) {
                dom.kk21(i, j) = kk21_vec[i][j];
            }
        }

        // 第五步: 生成质量矩阵
        auto mm11_vec = mass_matrix(px, Nx1, Nx1, t1, t1, xint, wint, 0);
        auto mm22_vec = mass_matrix(px, Nx2, Nx2, t2, t2, xint, wint, 1);

        Matrix mm11(Nx1, Nx1, 0.0);
        Matrix mm22(Nx2, Nx2, 0.0);

        // 优化: 高效的矩阵数据拷贝
        for(int i = 0; i < Nx1; ++i) {
            for(int j = 0; j < Nx1; ++j) {
                mm11(i, j) = mm11_vec[i][j];
            }
        }
        for(int i = 0; i < Nx2; ++i) {
            for(int j = 0; j < Nx2; ++j) {
                mm22(i, j) = mm22_vec[i][j];
            }
        }

        // 第六步: 优化的对称化处理
        // 确保质量矩阵的对称性（数值误差修正）
        for(int i = 0; i < Nx1; ++i) {
            for(int j = i + 1; j < Nx1; ++j) {
                const double avg = 0.5 * (mm11(i, j) + mm11(j, i));
                mm11(i, j) = mm11(j, i) = avg;
            }
        }
        for(int i = 0; i < Nx2; ++i) {
            for(int j = i + 1; j < Nx2; ++j) {
                const double avg = 0.5 * (mm22(i, j) + mm22(j, i));
                mm22(i, j) = mm22(j, i) = avg;
            }
        }

        // 第七步: 计算矩阵平方根和逆矩阵
        // 这些是数值密集型操作，但由于矩阵相对较小，域级并行已足够
        Matrix L11T = sqrtm(mm11);
        Matrix L22T = sqrtm(mm22);

        dom.invLT11 = inverse_full(L11T);
        dom.invLT22 = inverse_full(L22T);

        // 优化: 使用转置操作而非重新计算逆矩阵
        dom.invL11 = transpose(dom.invLT11);
        dom.invL22 = transpose(dom.invLT22);
    }
}

} // namespace wave