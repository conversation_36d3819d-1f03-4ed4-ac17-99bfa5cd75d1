clear;
addpath('./functions')
rads = [600.0, 1221.5, 2914.25, 3480.0]*1000;
ppw  = 3.5;
freq = 0.05;  % Hz
p    = 4;
dx   = 10000;
duration = 1000;
% Vp = 10000; % m/s;

flag_save_files=1; ndts=100;

outputdir='./proj_wave1d';
if ~exist(outputdir,'dir')
    mkdir(outputdir)
end

% simu = simulation1d();
OM = mesh_sphere1d(rads,dx,freq,p,ppw);

for iom = 1:length(OM)
    fprintf("... Info2 required for wave simulation is %d with %d points ... \n",iom,OM(iom).model1d.Nx1);
end

% get neighbors' info
OM = find_neighbors(OM);

% generate DFDM matrices related
OM = gen_DFDMatrices(OM);

% setting dt
[dt,nt] = compute_dt(OM,duration);

% initial other parameters
OM = initializedomain1d(OM,nt);

% get source info
[source] = get_source1d(OM,freq,dt,nt);


% get receiver info
[receivers] = get_receiver1d(OM,dt,nt);

% begin simulation
[OM,receivers]=solve1D(OM,source,receivers,nt,dt);


% % save file
if flag_save_files==1
    for iom=1:length(OM)
        filenamex=[outputdir,'/wfs_element',num2str(iom),'.mat'];
        x1d = OM(iom).model1d.x1d;
        wfs  = OM(iom).state.Umid;
        save(filenamex,'wfs','x1d','-v7.3');
    end
end

if flag_save_files == 1
    for ir=1:length(receivers)
        file_irece = [outputdir,'/rece',num2str(ir,2),'.txt'];
        ur = receivers(ir).ur;
        save(file_irece,"ur",'-ascii');
    end
end