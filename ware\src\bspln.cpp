#include "bspln.hpp"
#include <cmath>
#include <cstdint>
#include <limits>
#include <iostream>

/**
 * @file bspln.cpp
 * @brief Implementation of B-spline basis function evaluation
 *
 * This file corresponds exactly to MATLAB function bspln.m
 * Direct translation maintaining MATLAB's 1-based indexing logic
 */

// B-spline function implementation based on successful DFDM_2D_1.0-unstructured
// This is the corrected version that matches the working implementation
Real bspln(const Vector& t, Integer n, Integer i, Integer k, Real x) {
    // Use exact EPS value from DFDM_2D_1.0-unstructured for MATLAB compatibility
    const Real EPS = 1e-14;
    const Real safe_eps = 1e-15;

    // Convert to 0-based indexing for internal calculations
    // MATLAB uses 1-based, but we work with 0-based internally
    if (i < 1 || i > n || k < 1) {
        return 0.0;
    }

    // Convert to 0-based for array access
    Integer i0 = i - 1;  // Convert to 0-based

    // Bounds checking for array access
    if (i0 + k >= static_cast<Integer>(t.size())) {
        return 0.0;
    }

    // Implementation based on successful DFDM_2D_1.0-unstructured version
    Real b = 0;
    Real c1 = 0, c2 = 0;

    if (k == 1) {
        // Base case: k = 1
        if (x > t(i0) && !(x > t(i0 + 1))) {
            b = 1;
        } else {
            b = 0;
        }
    } else {
        // Recursive case: k > 1
        // First coefficient
        if ((t(i0 + k - 1) - t(i0)) > EPS) {
            c1 = (x - t(i0)) / (t(i0 + k - 1) - t(i0) + safe_eps);
        } else {
            c1 = 0;
        }

        // Second coefficient - use > instead of abs() like MATLAB
        if ((t(i0 + k) - t(i0 + 1)) > EPS) {
            c2 = (t(i0 + k) - x) / (t(i0 + k) - t(i0 + 1) + safe_eps);
        } else {
            c2 = 0;
        }

        // Recursive calls with MATLAB 1-based indexing
        b = c1 * bspln(t, n, i, k - 1, x) + c2 * bspln(t, n, i + 1, k - 1, x);
    }

    return b;
}