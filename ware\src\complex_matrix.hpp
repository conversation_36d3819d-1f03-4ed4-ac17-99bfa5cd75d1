#pragma once

#include "common_types.hpp"
#include <complex>
#include <vector>

namespace EigenWrapper {

// 复数类型别名
using Complex = std::complex<Real>;

/**
 * @brief 复数矩阵类
 * 
 * 提供与Matrix类兼容的复数矩阵操作
 */
class ComplexMatrix {
public:
    Integer rows{0}, cols{0};
    std::vector<Complex> data;

    // 存储顺序与Matrix类保持一致
    static constexpr bool is_col_major = Matrix::is_col_major;

    // 构造函数
    ComplexMatrix() = default;
    ComplexMatrix(Integer r, Integer c);
    ComplexMatrix(Integer r, Integer c, const Complex& val);
    ComplexMatrix(const Matrix& real, const Matrix& imag);
    
    // 索引计算方法 - 支持列优先存储
    inline Integer idx(Integer i, Integer j) const {
        if (is_col_major) {
            // 列优先索引: col*rows + row (MATLAB兼容)
            return j * rows + i;
        } else {
            // 行优先索引: row*cols + col
            return i * cols + j;
        }
    }

    // 访问元素
    Complex& operator()(Integer i, Integer j);
    const Complex& operator()(Integer i, Integer j) const;

    // 调整大小
    void resize(Integer r, Integer c);

    // 基本算术运算
    ComplexMatrix operator+(const ComplexMatrix& other) const;
    ComplexMatrix operator-(const ComplexMatrix& other) const;
    ComplexMatrix operator*(const Complex& scalar) const;

    // 转置和共轭转置
    ComplexMatrix transpose() const;
    ComplexMatrix ctranspose() const;

    // 获取实部和虚部
    Matrix real() const;
    Matrix imag() const;
};

/**
 * @brief 复数向量类
 * 
 * 提供与Vector类兼容的复数向量操作，继承自ComplexMatrix
 */
class ComplexVector : public ComplexMatrix {
public:
    // 构造函数
    ComplexVector() = default;
    ComplexVector(Integer n);
    ComplexVector(Integer n, const Complex& val);
    ComplexVector(const Vector& real, const Vector& imag);

    // 访问元素
    Complex& operator()(Integer i);
    const Complex& operator()(Integer i) const;

    // 向量大小
    Integer size() const;
};

// 矩阵乘法操作
ComplexMatrix matmul(const ComplexMatrix& A, const ComplexMatrix& B);
ComplexVector matvec(const ComplexMatrix& A, const ComplexVector& x);
Complex dot(const ComplexVector& a, const ComplexVector& b);

// 创建特殊矩阵
ComplexMatrix czeros(Integer rows, Integer cols);
ComplexMatrix cones(Integer rows, Integer cols);
ComplexMatrix create_cidentity(Integer n);

// 复数矩阵与实矩阵的转换
ComplexMatrix toComplex(const Matrix& A);
Matrix real(const ComplexMatrix& A);
Matrix imag(const ComplexMatrix& A);
Matrix abs(const ComplexMatrix& A);
Matrix arg(const ComplexMatrix& A);

} // namespace EigenWrapper 