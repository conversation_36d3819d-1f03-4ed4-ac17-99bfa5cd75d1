function check_stability(OM,it)
%CHECK_STABILITY Summary of this function goes here
%   Detailed explanation goes here

ndomains = length(OM);

maxU = -999999999;
for iom = 1:ndomains
    Umid = OM(iom).state.Umid(:,it);
    tmp  = max(Umid(:));
    maxU = max(maxU,tmp);
end
fprintf("Time step %d: maximum displacement is %e\n",it,maxU);

if max(abs(maxU(:)))>1e10
    fprintf("unstable,return!!!\n");
    return
end

end

